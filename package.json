{"name": "rbac_html", "version": "1.0.0", "description": "rbac_html", "author": "lyy", "private": true, "scripts": {"dev": "cross-env LYY_ENV=dev webpack serve --node-env development --progress --config build/webpack.dev.conf.js ", "sit": "cross-env LYY_ENV=sit webpack serve --node-env development --progress --config build/webpack.dev.conf.js ", "release": "cross-env LYY_ENV=release webpack serve --node-env development --progress --config build/webpack.dev.conf.js ", "master": "cross-env LYY_ENV=master webpack serve --node-env development --progress --config build/webpack.dev.conf.js ", "start": "npm run dev", "build:dev": "cross-env LYY_ENV=dev node build/build.js", "build:sit": "cross-env LYY_ENV=sit node build/build.js", "build:release": "cross-env LYY_ENV=release node build/build.js", "build": "cross-env LYY_ENV=master node build/build.js"}, "dependencies": {"@leyaoyao/qt-hook": "^0.0.30", "axios": "^0.21.1", "babel-loader": "^8.2.5", "better-scroll": "^2.4.2", "core-js": "^3.22.7", "element-ui": "^2.15.7", "es6-promise": "^4.2.5", "js-cookie": "2.2.0", "js-md5": "^0.7.3", "loadsh": "0.0.4", "lodash": "^4.17.21", "moment": "^2.29.1", "sortablejs": "^1.15.0", "vue": "^2.6.14", "vue-awesome-swiper": "^3.1.3", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.1", "vuedraggable": "^2.23.0", "vuex": "^3.0.1"}, "devDependencies": {"@babel/core": "^7.18.2", "@babel/plugin-transform-runtime": "^7.17.10", "@babel/preset-env": "^7.18.2", "@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@leyaoyao/icon": "^2.0.89", "@soda/friendly-errors-webpack-plugin": "^1.8.1", "autoprefixer": "^7.1.2", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "chalk": "^2.0.1", "clipboard": "^2.0.4", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^4.0.1", "cross-env": "^7.0.3", "css-loader": "^0.28.0", "css-minimizer-webpack-plugin": "^4.0.0", "dayjs": "^1.11.13", "html-webpack-plugin": "^5.5.0", "husky": "^3.0.9", "less": "^3.9.0", "less-loader": "^11.0.0", "mini-css-extract-plugin": "^2.6.0", "mockjs": "^1.0.1-beta3", "node-notifier": "^5.1.2", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "url-loader": "^0.5.8", "v-viewer": "^1.4.2", "vue-loader": "^15.9.8", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.6.14", "wangeditor": "^3.1.1", "webpack": "^5.72.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.8.1", "webpack-merge": "^5.8.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "engines": {"node": ">= 14.0.0", "npm": ">= 7.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}