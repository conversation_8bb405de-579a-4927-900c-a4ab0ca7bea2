const path = require('path');
function resolve (dir) {
  return path.join(__dirname, '.', dir);
}
module.exports = {
  context: path.resolve(__dirname, './'),
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    alias: {
        'vue$': 'vue/dist/vue.esm.js',
        '@': resolve('src'),
        '@css': resolve('src/assets/css'),
        '@js': resolve('src/utils'),
        '@api': resolve('src/api'),
        '@img': resolve('src/assets/images'),
        '@components': resolve('src/components'),
        '@views': resolve('src/views'),
    }
  }
};
