server {
    listen 80;
    server_name usop.leyaoyao.com;
    root /etc/nginx/html;
    access_log  /var/log/nginx/access.log main;
    error_log   /var/log/nginx/error.log;
    index  index.html;
    error_page 404 /404.html;
    location =/404.html {
       root html;
       index  index.html index.htm;
       expires -1;
    }

  location / {
         rewrite / /admin/index.html permanent;
         expires -1;
    }
    location = /admin {
        rewrite /admin /admin/index.html permanent;
         expires -1;
    }
    location = /admin/ {
        rewrite /admin /admin/index.html permanent;
         expires -1;
    }
    location ^~ /admin {
        rewrite /admin(.*)$ $1 break;
        proxy_pass        http://web-server-admin.uat-web-server:8023;
        index  index.html index.htm;
         expires -1;
    }

    location /nstatus {
         check_status;
         access_log off;
         #allow IP;
         #deny all;
     }
}

