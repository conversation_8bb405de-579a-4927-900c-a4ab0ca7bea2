server {
    listen 80;
    server_name usp.leyaoyao.com;
    root /etc/nginx/html;
    access_log  /var/log/nginx/access.log main;
    error_log   /var/log/nginx/error.log;
    index  index.html;
    error_page 404 /404.html;
    location =/404.html {
       root html;
       index  index.html index.htm;
       expires -1;
    }

    location ~ .*.html$ {
        root /etc/nginx/html;
        index  index.html index.htm;
        expires -1;
        break;
    }

    location ~.*.(gif|jpg|jpeg|png|bmp|swf)$ {
        root /etc/nginx/html;
        expires 7d;
        break;
    }
    location ~.*.(js|css)$ {
        root /etc/nginx/html;
        expires 7d;
        break;
    }

    location / {
         root /etc/nginx/html;
         expires 7d;
         break;
    }
    location ^~ /admin {
         proxy_pass http://web-server-authority-admin.uat-web-server:8095;
         expires -1;
         break;
    }
    
    location ^~ /gw {
         proxy_pass http://middle-api-gateway.uat-middle:6827;
         expires -1;
         break;
    }

    location /nstatus {
         check_status;
         access_log off;
         #allow IP;
         #deny all;
     }
}

