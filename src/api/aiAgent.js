import request from '@/utils/request'

/**
 * 获取智能体应用列表
 * @param {Object} params - 查询参数
 * @param {string} params.appName - 应用名称
 * @param {string} params.status - 状态 (0: 禁用, 1: 启用)
 * @param {number} params.pageIndex - 页码
 * @param {number} params.pageSize - 每页大小
 * @returns {Promise}
 */
export function getAiAgentList(params) {
  return request({
    url: '/ai-agent/list',
    method: 'get',
    params
  })
}

/**
 * 创建智能体应用
 * @param {Object} data - 应用数据
 * @param {string} data.appName - 应用名称
 * @param {string} data.description - 应用描述
 * @param {string} data.category - 应用类别
 * @param {number} data.status - 状态 (0: 禁用, 1: 启用)
 * @returns {Promise}
 */
export function createAiAgent(data) {
  return request({
    url: '/ai-agent/create',
    method: 'post',
    data
  })
}

/**
 * 更新智能体应用
 * @param {Object} data - 应用数据
 * @param {number} data.id - 应用ID
 * @param {string} data.appName - 应用名称
 * @param {string} data.description - 应用描述
 * @param {string} data.category - 应用类别
 * @param {number} data.status - 状态 (0: 禁用, 1: 启用)
 * @returns {Promise}
 */
export function updateAiAgent(data) {
  return request({
    url: '/ai-agent/update',
    method: 'post',
    data
  })
}

/**
 * 删除智能体应用
 * @param {Object} data - 删除参数
 * @param {number} data.id - 应用ID
 * @returns {Promise}
 */
export function deleteAiAgent(data) {
  return request({
    url: `/ai-agent/delete?id=${data.id}`,
    method: 'post'
  })
}

/**
 * 切换智能体应用状态
 * @param {Object} data - 状态切换参数
 * @param {number} data.id - 应用ID
 * @param {number} data.status - 新状态 (0: 禁用, 1: 启用)
 * @returns {Promise}
 */
export function toggleAiAgentStatus(data) {
  return request({
    url: '/ai-agent/toggle-status',
    method: 'post',
    data
  })
}

/**
 * 获取智能体应用详情
 * @param {Object} params - 查询参数
 * @param {number} params.id - 应用ID
 * @returns {Promise}
 */
export function getAiAgentDetail(params) {
  return request({
    url: '/ai-agent/detail',
    method: 'get',
    params
  })
}

/**
 * 批量删除智能体应用
 * @param {Object} data - 批量删除参数
 * @param {Array<number>} data.ids - 应用ID数组
 * @returns {Promise}
 */
export function batchDeleteAiAgent(data) {
  return request({
    url: '/ai-agent/batch-delete',
    method: 'post',
    data
  })
}

/**
 * 批量切换智能体应用状态
 * @param {Object} data - 批量状态切换参数
 * @param {Array<number>} data.ids - 应用ID数组
 * @param {number} data.status - 新状态 (0: 禁用, 1: 启用)
 * @returns {Promise}
 */
export function batchToggleAiAgentStatus(data) {
  return request({
    url: '/ai-agent/batch-toggle-status',
    method: 'post',
    data
  })
}

/**
 * 导出智能体应用列表
 * @param {Object} params - 导出参数
 * @param {string} params.appName - 应用名称
 * @param {string} params.status - 状态
 * @returns {Promise}
 */
export function exportAiAgentList(params) {
  return request({
    url: '/ai-agent/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取应用类别列表
 * @returns {Promise}
 */
export function getAiAgentCategories() {
  return request({
    url: '/ai-agent/categories',
    method: 'get'
  })
}

/**
 * 复制智能体应用
 * @param {Object} data - 复制参数
 * @param {number} data.id - 源应用ID
 * @param {string} data.newAppName - 新应用名称
 * @returns {Promise}
 */
export function copyAiAgent(data) {
  return request({
    url: '/ai-agent/copy',
    method: 'post',
    data
  })
}
