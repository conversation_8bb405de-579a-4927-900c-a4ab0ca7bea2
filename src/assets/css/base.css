* {
  padding: 0;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-touch-callout: default;
  -webkit-user-select: text;
  user-select: text;
  /* touch-action: none; */
}

html {
  -webkit-text-size-adjust: none;
  height: 100%;
}
body {
  font-size: 14px;
  vertical-align: baseline;
  text-size-adjust: none;
  -webkit-user-select: none;
  margin: 0 auto;
  font-family: "Microsoft YaHei", "Microsoft JhengHei";
  background: #eee;
  height: 100%;
  /* overflow: hidden; */
}
img {
  border: none;
  vertical-align: top;
}
a {
  /*color: #2979ff;*/
  color: #666;
  text-decoration: none;
}
a:hover {
    color: #49a9ee;
}

li {
  list-style: none;
}

*:focus {
  outline: none;
  outline: 0;
}

b, i, em {
  font-style: normal;
}

b {
  font-weight: normal;
}

a:active {
  opacity: .8;
}

input, textarea, select {
  -webkit-appearance: none;
  -webkit-border-radius: 0;
  border: none;
  background: #fff;
  font-family: inherit;
}

textarea {
  resize: none;
}

::-webkit-input-placeholder {
  color: #999;
}
.icon {
   width: 25px; height: 20px;
   margin-right: 5px;
   vertical-align: middle;
   fill: currentColor;
   overflow: hidden;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.clearfix::before, .clearfix::after {
  content: "";
  display: table;
}
.clearfix::after {
  content: '';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.flexbox {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}

.flex {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
}

.nowrap {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.color-999 {
  color: #999;
}

.font-small {
  font-size: 12px;
}

.bd, .bd-b, .bd-t, .bd-l {
  position: relative;
}

.bd:after {
  content: "";
  width: 200%;
  height: 200%;
  z-index: 1;
  position: absolute;
  pointer-events: none;
  left: 0;
  top: 0;
  right: 0;
  border: 1px solid #eee;/*no*/
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scale(0.5);
  transform: scale(0.5);
}

.bd-b:after {
  content: "";
  position: absolute;
  pointer-events: none;
  left: 0;
  bottom: 0;
  right: 0;
  border-top: 1px solid #eee;/*no*/
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

.bd-t:after {
  content: "";
  position: absolute;
  pointer-events: none;
  left: 0;
  top: 0;
  right: 0;
  border-top: 1px solid #eee;/*no*/
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

  .bd-l:after {
  content: "";
  position: absolute;
  pointer-events: none;
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  border-left: 1px solid #eee;/*no*/
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}
.margin-right-10 {
  margin-right: 10px;
}
.margin-left-10 {
  margin-left: 10px;
}
.margin-bottom-10 {
  margin-bottom: 10px;
}
.margin-top-10 {
  margin-top: 10px;
}
.padding-right-10 {
  padding-right: 10px;
}
.padding-left-10 {
  padding-left: 10px;
}
.padding-bottom-10 {
  padding-bottom: 10px;
}
.padding-top-10 {
  padding-top: 10px;
}
.QRCode-upload label {
  line-height: 100px !important;
}
.el-tree__empty-text {
  display: none;
}
.box100 {
  width: 100px;
  height: 100px;
}
.primary{
  color: #409EFF;
}
.success{
  color: #67C23A;
}
.warning{
  color: #E6A23C;
}
.danger{
  color: #F56C6C;
}
.info{
  color: #909399;
}
.blue{
  color: #409EFF;
}
.width50{width: 50%;}
/*element-ui style reset*/
.long-text .el-form-item__label{width: 123px !important;}
/*.short-text .el-form-item__label{width: 55px !important;}*/
/*.short-text .el-form-item__content{margin-left:55px !important;}*/

.ovh{
  overflow: hidden;
}
/* 对element样式处理 */
body .traffic-msg-box .red-text{
  margin:20px auto 0;
  color:#FF0000;
}
body .traffic-msg-box .red{
  color:#FF0000;
}
body .traffic-msg-box .text1{
  margin-bottom:20px;
}
body .traffic-msg-box .ccc{
  color:#999;
}
body .traffic-msg-box .text2{
  color:#666;
  font-size:14px;
}