.link-text{
    color: #2b6cf1;
	cursor: pointer;
}
.elCard-wrapper{
    min-height:100%;
}
::-webkit-scrollbar {
    width: 8px!important;
    height: 8px!important;
    background-color: #f5f5f5!important;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    background-color: #fff;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #aaa;
}