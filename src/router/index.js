import Vue from "vue";
import Router from "vue-router";

// import Layout from '@/views/layout/layout'
// import Index from '@/views/index/index'
// import Login from '@/views/login/login'

const Layout = () => import(/* webpackChunkName: "layout" */ '@/views/layout/layout');
const Login = ()=>import(/* webpackChunkName: "login" */ "@/views/login/login");
const Dashboard = ()=>import(/* webpackChunkName: "dashboard" */ "@/views/dashboard/dashboard");
// const Index = resolve => { require(['@/views/index/index'], resolve) };
const SystemManage =()=>import(/* webpackChunkName: "systemManage" */ "@/views/SystemManage/SystemManage"); //系统管理
const ResourcesManage =()=>import(/* webpackChunkName: "resourcesManage" */ "@/views/ResourcesManage/ResourcesManage"); //资源管理
const RoleManage = ()=>import(/* webpackChunkName: "roleManage" */ "@/views/RoleManage/RoleManage"); //角色管理
const UserManage = ()=>import(/* webpackChunkName: "userManage" */ "@/views/UserManage/UserManage"); //用户管理
const LogManage = ()=>import(/* webpackChunkName: "logManage" */ "@/views/LogManage/LogManage"); //日志管理
const DeviceOffline = ()=>import(/* webpackChunkName: "deviceOffline" */ "@/views/DeviceManage/DeviceOffline"); //设备离线明细
const DeviceOfflineBatch = ()=>import(/* webpackChunkName: "deviceOfflineBatch" */ "@/views/DeviceManage/deviceOfflineBatch.vue"); //设备离线明细，导入批量查询
// 对账上传
const FileRecordIndex = ()=>import(/* webpackChunkName: "fileRecordIndex" */ "@/views/finance/FileRecord/index"); // 对账数据上传
const ImportData = ()=>import(/* webpackChunkName: "fileRecordImportData" */ "@/views/finance/FileRecord/children/importData"); // 上传对账数据
const ImportRecord = ()=>import(/* webpackChunkName: "fileRecordImportRecord" */ "@/views/finance/FileRecord/children/importRecord"); // 上传对账数据
// 对账记录
const CheckIndex = ()=>import(/* webpackChunkName: "checkListIndex" */ "@/views/finance/checkList/index"); // 对账记录页
const CheckList = ()=>import(/* webpackChunkName: "checkListList" */ "@/views/finance/checkList/children/checkList"); // 对账记录
const CheckCompletedRecord = ()=>import(/* webpackChunkName: "checkListCompletedRecord" */ "@/views/finance/checkList/children/completedRecord"); // 确认对账记录
const CheckUp =()=>import(/* webpackChunkName: "checkListCheckUp" */ "@/views/finance/checkList/children/checkUp"); // 对账
// 付款渠道管理
const PaymentChannelIndex = ()=>import(/* webpackChunkName: "paymentChannelIndex" */ "@/views/finance/paymentChannel/index"); // 渠道管理首页
const PaymentChannelList = ()=>import(/* webpackChunkName: "paymentChannelChannelList" */ "@/views/finance/paymentChannel/children/channelList"); // 渠道管理列表
const ByMerchant = ()=>import(/* webpackChunkName: "ByMerchant" */ "@/views/finance/paymentChannel/children/byMerchant"); // 渠道管理列表
const ByReceiver = ()=>import(/* webpackChunkName: "ByReceiver" */ "@/views/finance/paymentChannel/children/byReceiver"); // 渠道管理列表
const Default = ()=>import(/* webpackChunkName: "Default" */ "@/views/finance/paymentChannel/children/default"); // 渠道管理列表
const ChannelChangeRecords = ()=>import(/* webpackChunkName: "paymentChannelChannelList" */ "@/views/finance/paymentChannel/children/changeRecords"); // 渠道变更记录列表
const PaymentChannelCreated = ()=>import(/* webpackChunkName: "paymentChannelChannelCreated" */ "@/views/finance/paymentChannel/children/channelCreated"); // 创建新渠道
// 应付对账记录
const DealWithCheckIndex = ()=>import(/* webpackChunkName: "dealWithCheckIndex" */ "@/views/finance/dealWithCheck/index"); // 应付对账查看
const DealWithCheckMonthlyList =()=> import(/* webpackChunkName: "dealWithCheckMonthlyList" */ "@/views/finance/dealWithCheck/children/dealWithCheckMonthlyList"); // 渠道管理列表
const DealWithCheckDailyList =()=> import(/* webpackChunkName: "dealWithCheckDailyList" */ "@/views/finance/dealWithCheck/children/dealWithCheckDailyList"); // 创建新渠道
// 支付渠道管理
const PayChannelIndex =()=> import(/* webpackChunkName: "payChannelIndex" */ "@/views/finance/payChannel/index"); // 渠道管理首页
const PayChannelList =()=> import(/* webpackChunkName: "payChannelList" */ "@/views/finance/payChannel/children/channelList"); // 渠道管理列表
const PayChannelEdit =()=> import(/* webpackChunkName: "payChannelEdit" */ "@/views/finance/payChannel/children/channelEdit"); // 创建新渠道
// 历史交易汇总
const HistoryPayChannelIndex =()=> import(/* webpackChunkName: "historyPayChannelIndex" */ "@/views/finance/historyPayChannelData/index");// 历史交易汇总首页
const HistoryPayChannelData =()=> import(/* webpackChunkName: "historyPayChannelData" */ "@/views/finance/historyPayChannelData/children/historyData"); // 历史交易汇总内容
// 单日交易汇总
const SingleDayPayChannelIndex =()=>import(/* webpackChunkName: "singleDayPayChannelIndex" */ "@/views/finance/singleDayPayChannelData/index"); // 历史交易汇总首页
const SingleDayPayChannelData =()=>import(/* webpackChunkName: "singleDayPayChannelData" */ "@/views/finance/singleDayPayChannelData/children/historyData"); // 历史交易汇总内容
const SignRecordList =()=>import(/* webpackChunkName: "signRecordList" */ "@/views/finance/signRecordList/index"); // 注册签约记录

// ======================================美团模块===================================================
// const MerchantStoreIndex = resolve => { require(['@/views/MeiTuan/merchantStore/index'], resolve) }; // 商家配置门店首页
// const MerchantStoreList = resolve => { require(['@/views/MeiTuan/merchantStore/children/storeList'], resolve) }; // 商家配置门店列表
const GroupCheckIndex =()=> import(/* webpackChunkName: "mtGroupCheckIndex" */ "@/views/MeiTuan/groupCheck/index"); // 商家团购设置首页
const GroupCheckList =()=> import(/* webpackChunkName: "mtGroupCheckList" */ "@/views/MeiTuan/groupCheck/children/checkList"); // 商家团购设置列表
const PayList = ()=>import(/* webpackChunkName: "mtPayList" */ "@/views/MeiTuan/payList/payList"); // 美团店铺待支付名单

// =========================================广告=========================================================
const AdvertisementIndex =()=> import(/* webpackChunkName: "advertisementIndex" */ "@/views/advertisement/index"); // 编辑模板

const CloudCode =()=> import(/* webpackChunkName: "advertisementCloudCode" */ "@/views/advertisement/children/cloudCode");
const CdkeyManage =()=> import(/* webpackChunkName: "advertisementCdkeyManage" */ "@/views/advertisement/children/cdkeyManage");
const AdLocationConfig =()=> import(/* webpackChunkName: "advertisementAdLocationConfig" */ "@/views/advertisement/children/adLocationConfig");
const OfflineFansActivity =()=> import(/* webpackChunkName: "advertisementOfflineFansActivity" */ "@/views/advertisement/children/offlineFansActivity");
const BackerActivity =()=> import(/* webpackChunkName: "advertisementBackerActivity" */ "@/views/advertisement/children/backerActivity");
const VipTask =()=> import(/* webpackChunkName: "advertisementVipTask" */ "@/views/advertisement/children/vipTask");
const DbjLottery =()=> import(/* webpackChunkName: "advertisementDbjLottery" */ "@/views/advertisement/children/dbjLottery"); // 兑多多抽奖
const DbjDoubleCoins =()=> import(/* webpackChunkName: "advertisementDbjDoubleCoins" */ "@/views/advertisement/children/dbjDoubleCoins"); // 兑多多翻倍出奖
const CombineCoins =()=> import(/* webpackChunkName: "advertisementCombineCoins" */ "@/views/advertisement/children/combineCoins");
const MaterialVerify =()=> import(/* webpackChunkName: "advertisementMaterialVerify" */ "@/views/advertisement/children/materialVerify"); // 素材审核
const GoldenPackage =()=> import(/* webpackChunkName: "advertisementGoldenPackage" */ "@/views/advertisement/children/goldenPackage"); // 金主套餐
const AdvertiseSubsidy =()=> import(/* webpackChunkName: "advertisementAdvertiseSubsidy" */ "@/views/advertisement/children/advertiseSubsidy"); // 广告商家分成补贴
const CommissionRatioSetting =()=> import(/* webpackChunkName: "advertisementCommissionRatioSetting" */ "@/views/advertisement/wwjUpgradeOrder/commissionRatioSetting"); // 抽佣比例设置
const LookUpAdActive =()=> import(/* webpackChunkName: "advertisementLookUpAdActive" */ "@/views/advertisement/children/lookUpAdActive"); // 查看相关活动没有曝光条件
const upgradeOrder =()=> import(/* webpackChunkName: "advertisementUpgradeOrder" */ "@/views/advertisement/upgradeOrder/upgradeOrder.vue"); // 升单配置
const ChargeUpgrade =()=> import(/* webpackChunkName: "advertisementChargeUpgrade" */ '@/views/chargeUpgrade/index');// 充电桩升购
const OnlineListManage =()=> import(/* webpackChunkName: "advertisementOnlineListManage" */ '@/views/chargeUpgrade/children/onlineListManage'); // 充电桩升购--上线清单管理
const UpgradeOrderList =()=> import(/* webpackChunkName: "advertisementUpgradeOrderList" */ '@/views/chargeUpgrade/children/orderList'); // 充电桩升购--订单列表
const ChargeInsurance =()=> import(/* webpackChunkName: "advertisementChargeInsurance" */ '@/views/chargeInsurance/index'); // 充电桩保险加购
const OnlineListManageInsurance =()=> import(/* webpackChunkName: "advertisementOnlineListManageInsurance" */ '@/views/chargeInsurance/children/onlineListManage'); // 充电桩保险加购-上线名单管理
const OnlineListManageInsurance3 =()=> import(/* webpackChunkName: "advertisementOnlineListManage3" */ '@/views/chargeInsurance/children/onlineListManage3'); // 充电桩升购--上线清单管理3.0
const InsuranceOrderList =()=> import(/* webpackChunkName: "advertisementInsuranceOrderList" */ '@/views/chargeInsurance/children/orderList');
const HierarchyManage = ()=> import(/* webpackChunkName: "advertisementHierarchyManage" */ '@/views/chargeUpgrade/children/hierarchyManage'); // 充电桩升购-用户价格分层设置
const OnlineListManageInsuranceMonthCard  = () => import(/* webpackChunkName: "monthCardOnlineListManage" */ '@/views/chargeInsurance/children/monthCardOnlineListManage');// 充电桩保险加购(月卡)-上线名单管理
const InsuranceOrderListMonthCard   = () => import(/* webpackChunkName: "monthCardOrderList" */ '@/views/chargeInsurance/children/monthCardOrderList');// 充电桩保险加购(月卡)-订单列表
const IntegralAvatarOrder =()=> import(/* webpackChunkName: "advertisementIntegralAvatarOrder" */ "@/views/advertisement/children/integralAvatarOrder"); // 积分头像购买订单
const SaasOnlineListManageInsurance = () => import(/* webpackChunkName: "advertisementSaasOnlineListManage" */ '@/views/chargeInsurance/children/saasOnlineList'); // 充电桩保险加购-saas
const ForcedOpeningList = () => import(/* webpackChunkName: "advertisementForcedOpeningList" */ '@/views/chargeInsurance/children/forcedOpeningList'); // 充电桩保险加购-强开名单
const ChargeAdvertisement = () => import(/* webpackChunkName: "advertisementChargeAdvertisement" */ '@/views/chargingAdvertisement/index'); // 充电桩商创
const LvYuanAdvertisement = () => import(/* webpackChunkName: "advertisementLvYuanAdvertisement" */ '@/views/chargingAdvertisement/children/lvyuanAdvertisement'); // 绿源广告
const FlowMiniprogram = () => import(/* webpackChunkName: "advertisementFlowMiniprogram" */ '@/views/chargingAdvertisement/children/flowMiniprogram'); // 流量小程序
const AdvertActivityConfig = () => import(/* webpackChunkName: "advertisementAdvertActivityConfig" */ '@/views/chargingAdvertisement/children/advertActivityConfig'); // 商创活动配置
// =========================================客服中心=========================================================
const ImportManagement =()=> import(/* webpackChunkName: "ImportManagement" */ "@/views/synthesizeImportManagement/index"); // 综合进件管理
const ChangeMerchantNameRecord =()=> import(/* webpackChunkName: "ChangeMerchantNameRecord" */ "@/views/synthesizeImportManagement/children/changeMerchantNameRecord");// 更改商户名称记录
const ReplaceMerchantNumberRecord =()=> import(/* webpackChunkName: "ReplaceMerchantNumberRecord" */ "@/views/synthesizeImportManagement/children/merchantNumberReplaceRecord"); // 变更商户渠道号记录
const CustomerQuery = ()=> import(/* webpackChunkName: "CustomerQuery" */ "@/views/CustomerService/customerQuery/customerQuery"); //客服查询页面
const CustomerModule = ()=> import(/* webpackChunkName: "CustomerModule" */ "@/views/CustomerService/customerModule/index"); //客服模块管理
const UserBalance = ()=> import(/* webpackChunkName: "UserBalance" */ "@views/baseManager/userBalance/userBalance.vue"); // 余币查询
const WechatCustomService = ()=> import(/* webpackChunkName: "WechatCustomService" */ "@views/baseManager/wechatCustomService/wechatCustomService.vue"); // 微信客服审核
const AutoAdvertie = ()=> import(/* webpackChunkName: "AutoAdvertie" */ "@views/baseManager/autoAdvertie/autoAdvertie.vue"); // 自主广告上线审核
const merchantAdjustmentCoins = ()=> import(/* webpackChunkName: "merchantAdjustmentCoins" */ "@views/baseManager/merchantAdjustmentCoins/merchantAdjustmentCoins.vue"); // 商家调整余额余币记录
const FactoryInfo = ()=> import(/* webpackChunkName: "FactoryInfo" */ "@views/baseManager/factoryInfo/factoryInfo.vue"); // 工厂资料审核
const PageRecords = ()=> import(/* webpackChunkName: "PageRecords" */ "@views/CustomerService/pageRecords/pageRecords"); // 派币派余额查询
const UserLogout = ()=> import(/* webpackChunkName: "UserLogout" */ "@views/userLogout/userLogout"); // 用户注销记录

// 运营后台
const newUserOptimization = ()=> import(/* webpackChunkName: "newUserOptimization" */ "@/views/admin/dataStatistics/newUserOptimization"); // 历史交易汇总首页
const operationLog = ()=> import(/* webpackChunkName: "operationLog" */ "@/views/admin/operationLog/operationLog"); // 历史交易汇总首页

const deviceModify = ()=> import(/* webpackChunkName: "deviceModify" */ "@/views/admin/device/deviceModify"); // 历史交易汇总首页
/**
 * 业务模块 start
 * */
//优惠券活动统计页面
const Coupon = ()=> import(/* webpackChunkName: "Coupon" */ "@/views/professionalWork/coupon/index"); // 优惠券活动统计
const CouponList = ()=> import(/* webpackChunkName: "CouponList" */ "@views/professionalWork/coupon/couponList"); // 优惠券活动统计列表
const CouponEdit = ()=> import(/* webpackChunkName: "CouponEdit" */ "@/views/professionalWork/coupon/couponEdit"); // 优惠券活动统计创建和编辑
//超值卡页面
const SuperCard = ()=> import(/* webpackChunkName: "SuperCard" */ "@/views/professionalWork/superCard/index"); // 优惠券活动统计
const SuperCardList = () => import(/* webpackChunkName: "SuperCardList" */ "@views/professionalWork/superCard/superCardList"); // 优惠券活动统计列表
const SuperCardEdit = () => import(/* webpackChunkName: "SuperCardEdit" */ "@/views/professionalWork/superCard/superCardEdit"); // 优惠券活动统计创建和编辑
//会员卡页面
const MemberCard = () => import(/* webpackChunkName: "MemberCard" */ "@/views/professionalWork/memberCard/memberCard"); // 会员卡页面
//增值产品归集展示
const IncreaseIncomeProduct = () => import(/* webpackChunkName: "IncreaseIncomeProduct" */ "@/views/professionalWork/appreciation/appreciation"); // 会员卡页面
//自动分账-插件费统计页面
const AutomaticAccountSplitting = () => import(/* webpackChunkName: "AutomaticAccountSplitting" */ "@/views/professionalWork/automaticAccountSplitting/automaticAccountSplitting"); // 自动分账-插件费统计页面
//拼团购币页面
const GroupBuying = () => import(/* webpackChunkName: "GroupBuying" */ "@/views/professionalWork/groupBuying/groupBuying"); // 拼团购币页面

const ClearDeploy = () => import(/* webpackChunkName: "ClearDeploy" */ "@/views/professionalWork/cleardeploy/index"); // 二清结算配置页
const ClearDeployList = () => import(/* webpackChunkName: "ClearDeployList" */ "@/views/professionalWork/cleardeploy/cleardeployList"); // 二清结算list
const ClearDeployEdit = () => import(/* webpackChunkName: "ClearDeployEdit" */ "@/views/professionalWork/cleardeploy/cleardeployEdit"); // 二清结算edit

// 工厂流量卡收入
const TrafficIncome = () => import(/* webpackChunkName: "TrafficIncome" */ "@/views/admin/trafficIncome/trafficIncome");

// 主账号修改登陆账号记录
const MerchantAccount = () => import(/* webpackChunkName: "MerchantAccount" */ "@/views/professionalWork/merchantAccount/merchantAccount");
/**
 * 业务模块 end
 * */
//交易记录查询页面
const TransactionRecord = () => import(/* webpackChunkName: "TransactionRecord" */ "@/views/transactionRecord/transactionRecord"); // 拼团购币页面

/**
 * 功能介绍中心 start
 * */
// B端后台品类消息推送
const MerchantMessage = () => import(/* webpackChunkName: "MerchantMessage" */ "@/views/functionIntroduction/merchantMessage/merchantMessage.vue");
// 教程内容管理
const Tutorial = () => import(/* webpackChunkName: "Tutorial" */ "@/views/functionIntroduction/tutorial/index.vue");
// 教程内容管理子路由页面
const TutorialIndex = () => import(/* webpackChunkName: "TutorialIndex" */ "@/views/functionIntroduction/tutorial/tutorial.vue"); // 教程内容管理主页面
const TutorialEdit = () => import(/* webpackChunkName: "TutorialIndex" */ "@/views/functionIntroduction/tutorial/children/content/tutorialContentSub.vue"); // 教程内容管理新建或编辑页面
// 常见问题管理
const Problem = () => import(/* webpackChunkName: "Problem" */ "@/views/functionIntroduction/problem/problem.vue");
// 系统消息推送
const SystemMessage = () => import(/* webpackChunkName: "SystemMessage" */ "@/views/functionIntroduction/systemMessage/systemMessage.vue");
// 首页banner管理
const HomeBanner = () => import(/* webpackChunkName: "HomeBanner" */ "@/views/functionIntroduction/homeBanner/homeBanner.vue");
// 产品推文
const ProductTweetsIndex = () => import(/* webpackChunkName: "ProductTweetsIndex" */ "@/views/functionIntroduction/productTweets/productTweets.vue");
const ProductTweetsRouter = () => import(/* webpackChunkName: "ProductTweetsRouter" */ "@/views/functionIntroduction/productTweets/index.vue");
const ProductTweetsEdit = () => import(/* webpackChunkName: "ProductTweetsEdit" */ "@/views/functionIntroduction/productTweets/productTweetsSub.vue");
/**
 * 功能介绍中心 end
 * */

// 服务套餐配置
const ServiceConfigIndex = () => import(/* webpackChunkName: "ServiceConfigIndex" */ "@views/professionalWork/serviceConfig/index.vue");
const ServiceConfigList = () => import(/* webpackChunkName: "ServiceConfigList" */ "@views/professionalWork/serviceConfig/serviceConfig.vue");
const ServiceConfigEdit = () => import(/* webpackChunkName: "ServiceConfigEdit" */ "@views/professionalWork/serviceConfig/serviceConfigSub.vue");

// 服务套餐配置-洗水类
const ServiceConfigWashIndex = () => import(/* webpackChunkName: "ServiceConfigIndex" */ "@views/professionalWork/serviceConfigWash/index.vue");
const ServiceConfigWashList = () => import(/* webpackChunkName: "ServiceConfigList" */ "@views/professionalWork/serviceConfigWash/serviceConfigWash.vue");
const ServiceConfigWashEdit = () => import(/* webpackChunkName: "ServiceConfigEdit" */ "@views/professionalWork/serviceConfigWash/serviceConfigWashSub.vue");

/**
 * 基础管理 start
 * */



//拉卡拉分账
const LakalaIndex = () => import(/* webpackChunkName: "LakalaIndex" */ "@views/lakala/index");
const MerchantDetail = () => import(/* webpackChunkName: "MerchantDetail" */ "@views/lakala/Detail.vue");

//乐潮玩
const Lechaowan = () => import(/* webpackChunkName: "Lechaowan" */ "@views/lechaowan/index");
//“删除仓位-扭蛋机
const DelPosition = () => import(/* webpackChunkName: "DelPosition" */ "@views/delPosition/index");
// 公众号授权
const OfficialAccountQrcode = () => import(/* webpackChunkName: "OfficialAccountQrcode" */ "@views/officialAccountQrcode/officialAccountQrcode.vue");
const AuthSuccess = () => import(/* webpackChunkName: "AuthSuccess" */ "@views/officialAccountQrcode/authSuccess.vue");

/**
 * 基础管理 end
 * */
/**
 * 业务查询 start
 * */
const AccountCancellation = () => import(/* webpackChunkName: "AccountCancellation" */ "@views/professionalQuery/accountCancellation/accountCancellation.vue"); // 账号注销
/**
 * 业务查询 end
 * */
// 消防对接设备
const FireEquipment = () => import(/* webpackChunkName: "FireEquipment" */ "@/views/admin/fireEquipment/fireEquipment.vue"); // 消防对接设备-首页

//设备交易分析
const DeviceAnalysisSettings =() => import(/* webpackChunkName: "DeviceAnalysisSettings" */ "@/views/deviceAnalysisSettings/deviceAnalysisSettings.vue"); // 设备交易分析

//生产商支付渠道
const merchantPayChannel = () => import(/* webpackChunkName: "merchantPayChannel" */ "@/views/merchantPay/merchantPayChannel.vue");

//代理商客户设备同步记录
const agentKdEquipment = () => import(/* webpackChunkName: "agentKdEquipment" */ "@/views/professionalWork/agentClientManager/agentKdEquipment.vue");

// 客服中心-提现审核
const withdraw = () => import(/* webpackChunkName: "withdraw" */ "@/views/professionalWork/withdraw/index.vue");

// =========================================刷脸支付 start==============================================
const faceBrushIndex = () => import(/* webpackChunkName: "faceBrushIndex" */ "@/views/faceBrush/index.vue");
const faceBrushPayMinus = () => import(/* webpackChunkName: "faceBrushPayMinus" */ "@/views/faceBrush/payMinus/payMinus.vue");
const faceBrushDeviceDataList = () => import(/* webpackChunkName: "faceBrushDeviceDataList" */ "@/views/faceBrush/deviceDataList/deviceDataList.vue");
const scanPermissionSetting = () => import(/* webpackChunkName: "scanPermissionSetting" */ "@/views/faceBrush/scanPermissionSetting/index.vue"); // 扫码支付记录
// =========================================刷脸支付 end  ==============================================

// =========================================设备管理 start==============================================
const EquipmentManageIndex = () => import(/* webpackChunkName: "EquipmentManageIndex" */ "@/views/EquipmentManage/index.vue");
const equipmentQRcodeManage = () => import(/* webpackChunkName: "equipmentQRcodeManage" */ "@/views/EquipmentManage/equipmentQRcodeManage/index.vue");
const equipmentOfflineSituation = () => import(/* webpackChunkName: "equipmentQRcodeManage" */ "@/views/EquipmentManage/equipmentOfflineSituation.vue");
// =========================================设备管理 end  ==============================================

const PcRender = () => import("@/components/PcRender/index")

Vue.use(Router);

// export default new Router({
// 	routes: [
// 		{ path: '/', name: 'root', component: Layout, redirect: '/index',children: [
// 			{ path: '/index', name: 'index', component: Index}
// 		]},
// 		{ path: '/login', name: 'login', component: Login}
// 	]
// })

import saas2 from './saas2';
import multiplatform from './multiplatform';
// 签署协议管理路由
import { protocol } from "@/router/protocol";
import {OperationManageRouter} from './operationManage.js'; // 运营管理中心路由
import {riskManagement} from "./riskManagement"; // 风控
import {thirdPartyBoard} from "@/router/thirdPartyBoard"; // 第三方平台
import {BaseManageRouter} from './baseManage.js'; // 基础管理路由
import {commercialize} from './commercialize.js'; // 商业化
import { dataReports } from './dataReports'; // 数据报表
import {TrafficCardRouter} from './trafficCard.js'; // 流量卡
import entertainmentDrainage from './entertainmentDrainage';
import announcement from './announcement';
import customerIncrementValue from './customerIncrementValue';
import cloudCodeRouter from './cloudCode';// 聚合拉新
import integralMall from "./integralMall";
import { paymentRouter } from '@/router/payment.js'; // 支付
import assembleFansRouter from "@/router/assembleFans"; // 功能性涨粉
import infoMatchPlatformRouter from "@/router/infoMatchPlatform"; // 信息撮合平台
import activitiesQuery from "@/router/activitiesQuery"; // 增值活动查询
import { equipmentRouter } from '@/router/equipment.js'; // 设备
import { VendingRouter } from '@/router/vending'; // 新零售
import { chargeManagement } from "@/router/chargeManagement";
// import TradeManagement from '@/router/tradeManagement'; // 新零售

import operationAndMaintenance from './operationAndMaintenance.js' // 运维操作
// import customerService from "./customerService"; // 客户服务
// import chargingRules from "./chargingRules"; // 收费管理
// import subsidy from "./subsidy"; // 补贴管理
import cpcAdvert from "./cpcAdvert"; // 广告管理
import memberManagement from "./memberManagement"; // 会员管理
// import preTask from "./preTask"; // 前置关注
// import dataStatistics from "./dataStatistics"; // 数据统计
// import huokeyiActive from "./huokeyiActive"; // 获客易列表页
// import equipmentOnline from "./equipmentOnline" // 设备在线
import smallVenue from "./smallVenue"; // 多金宝小场地
import pageSetting from "./bEndPageSetting"; // B端页面设置
import { chargeCSRouter } from "./chargeCSOperate.js"; // 充电桩客服操作

// 智能体应用
const AiAgent = ()=>import(/* webpackChunkName: "aiAgent" */ "@/views/aiAgent/index");

const router = new Router({
  // mode: 'history',
  routes: [
    {
      path: "*",
      name: "root",
      component: Layout,
      redirect: "/dashboard",
      children: [
        ...announcement,
        ...saas2,
        ...multiplatform,
        ...protocol,
        ...OperationManageRouter,
        ...riskManagement,
        ...thirdPartyBoard,
        ...BaseManageRouter,
        ...commercialize,
        ...dataReports,
        ...TrafficCardRouter,
        ...paymentRouter,
        ...equipmentRouter,
        ...VendingRouter,
        ...chargeManagement,
        // ...TradeManagement,
        // ...equipmentOnline,
        ...chargeCSRouter,


        { path: "/dashboard", name: "dashboard", component: Dashboard },
        { path: "/MerchantDetail", name: "MerchantDetail", component: MerchantDetail },
        { path: "/lakala", name: "lakala", component: LakalaIndex },
        { path: "/lakalaAdmin", name: "lakalaAdmin", component: LakalaIndex },
        { path: "/lechaowan", name: "lechaowan", component: Lechaowan },
        { path: "/delPosition", name: "delPosition", component: DelPosition },
        {
          path: "/authSuccess",
          name: "authSuccess",
          component: AuthSuccess
        },
        {
          path: "/officialAccountQrcode",
          name: "officialAccountQrcode",
          component: OfficialAccountQrcode
        },
        { path: "/SystemManage", name: "SystemManage", component: SystemManage },
        { path: "/ResourcesManage", name: "ResourcesManage", component: ResourcesManage },
        { path: "/RoleManage", name: "RoleManage", component: RoleManage },
        // 运营后台
        { path: '/newUserOptimization', name: 'newUserOptimization', component: newUserOptimization },
        { path: '/operationLog', name: 'operationLog', component: operationLog },
        { path: '/deviceModify', name: 'deviceModify', component: deviceModify },
        { path: '/trafficIncome', name: 'trafficIncome', component: TrafficIncome },
        // modify by lss ********
        { path: "/UserManage", name: "UserManage", component: UserManage },
        { path: "/LogManage", name: "LogManage", component: LogManage },
        { path: "/deviceOffline", name: "deviceOffline", component: DeviceOffline },
        { path: "/deviceOfflineBatch", name: "deviceOfflineBatch", component: DeviceOfflineBatch },
        {
          path: "/FinanceFileRecordIndex",
          name: "FinanceFileRecordIndex",
          component: FileRecordIndex,
          redirect: "/FinanceFileRecordIndex/ImportData",
          children: [
            {
              path: "ImportData",
              name: "FileImportData",
              component: ImportData
            },
            {
              path: "ImportRecord",
              name: "FileImportRecord",
              component: ImportRecord
            }
          ]
        },
        {
          path: "/FinanceCheckListIndex",
          name: "FinanceCheckListIndex",
          component: CheckIndex,
          redirect: "/FinanceCheckListIndex/CheckList",
          children: [
            {
              path: "CheckList",
              name: "CheckList",
              component: CheckList
            },
            {
              path: "CheckCompletedRecord",
              name: "CheckCompletedRecord",
              component: CheckCompletedRecord
            },
            {
              path: "CheckUp",
              name: "CheckUp",
              component: CheckUp
            }
          ]
        },
        {
          path: "/PaymentChannelIndex",
          name: "PaymentChannelIndex",
          component: PaymentChannelIndex,
          redirect: "/PaymentChannelIndex/PaymentChannelList",
          children: [
            {
              path: "PaymentChannelList",
              name: "PaymentChannelList",
              component: PaymentChannelList,
              redirect: "/PaymentChannelIndex/PaymentChannelList/Default",
              children: [
                {
                  path: 'ByMerchant',
                  name: 'ByMerchant',
                  component: ByMerchant,
                },
                {
                  path: 'ByReceiver',
                  name: 'ByReceiver',
                  component: ByReceiver,
                },
                {
                  path: 'Default',
                  name: 'Default',
                  component: Default,
                },
              ]
            },
            {
              path: "ChangeRecords",
              name: "ChannelChangeRecords",
              component: ChannelChangeRecords
            },
            {
              path: "PaymentChannelCreated",
              name: "PaymentChannelCreated",
              component: PaymentChannelCreated
            }
          ]
        },
        {
          path: "/DealWithCheckIndex",
          name: "DealWithCheckIndex",
          component: DealWithCheckIndex,
          redirect: "/DealWithCheckIndex/DealWithCheckMonthlyList",
          children: [
            {
              path: "DealWithCheckMonthlyList",
              name: "DealWithCheckMonthlyList",
              component: DealWithCheckMonthlyList
            },
            {
              path: "DealWithCheckDailyList",
              name: "DealWithCheckDailyList",
              component: DealWithCheckDailyList
            }
          ]
        },
        {
          path: "/PayChannelIndex",
          name: "PayChannelIndex",
          component: PayChannelIndex,
          redirect: "/PayChannelIndex/PayChannelList",
          children: [
            {
              path: "PayChannelList",
              name: "PayChannelList",
              component: PayChannelList
            },
            {
              path: "PayChannelEdit",
              name: "PayChannelEdit",
              component: PayChannelEdit
            }
          ]
        },
        {
          path: "/HistoryPayChannelIndex",
          name: "HistoryPayChannelIndex",
          component: HistoryPayChannelIndex,
          redirect: "/HistoryPayChannelIndex/HistoryPayChannelData",
          children: [
            {
              path: "HistoryPayChannelData",
              name: "HistoryPayChannelData",
              component: HistoryPayChannelData
            }
          ]
        },
        {
          path: "/SingleDayPayChannelIndex",
          name: "SingleDayPayChannelIndex",
          component: SingleDayPayChannelIndex,
          redirect: "/SingleDayPayChannelIndex/SingleDayPayChannelData",
          children: [
            {
              path: "SingleDayPayChannelData",
              name: "SingleDayPayChannelData",
              component: SingleDayPayChannelData
            }
          ]
        },
        {
          path: "/SignRecordList",
          name: "SignRecordList",
          component: SignRecordList,
        },
        {
          path: "/GroupCheckIndex",
          name: "GroupCheckIndex",
          component: GroupCheckIndex,
          redirect: "/GroupCheckIndex/GroupCheckList",
          children: [
            {
              path: "GroupCheckList",
              name: "GroupCheckList",
              component: GroupCheckList
            }
          ]
        },
        {
          path: "/MeiTuan/PayList",
          name: "PayList",
          component: PayList,
        },
        {
          path: '/merchantPayChannel',
          name: 'merchantPayChannel',
          component: merchantPayChannel
        },
        // =====================广告========================
        {
          path: "/advertisement",
          name: "AdvertisementIndex",
          component: AdvertisementIndex,
          redirect: "/advertisement/cloudCode",
          children: [
            {
              path: "CloudCode",
              name: "CloudCode",
              component: CloudCode
            },
            {
              path: "AdLocationConfig",
              name: "AdLocationConfig",
              component: AdLocationConfig
            },
            {
              path: "VipTask",
              name: "VipTask",
              component: VipTask
            },
            {
              path: "DbjLottery",
              name: "DbjLottery",
              component: DbjLottery
            },
            {
              path: "CombineCoins",
              name: "CombineCoins",
              component: CombineCoins
            },
            {
              path: "DbjDoubleCoins",
              name: "DbjDoubleCoins",
              component: DbjDoubleCoins
            },
            {
              path: "MaterialVerify",
              name: "MaterialVerify",
              component: MaterialVerify
            },
            {
              path: "GoldenPackage",
              name: "GoldenPackage",
              component: GoldenPackage
            },
            {
              path: "CdkeyManage",
              name: "CdkeyManage",
              component: CdkeyManage
            },
            {
              path:'offlineFansActivity',
              name:'offlineFansActivity',
              component:OfflineFansActivity
            } ,
            {
              path:'BackerActivity',
              name:'backerActivity',
              component:BackerActivity
            },
            {
              path: 'advertiseSubsidy',
              name:'advertiseSubsidy',
              component:AdvertiseSubsidy
            },
            {
              path: 'lookUpAdActive',
              name:'lookUpAdActive',
              component:LookUpAdActive
            },
            {
              path: 'commissionRatioSetting',
              name:'commissionRatioSetting',
              component:CommissionRatioSetting
            },
            {
              path: 'upgradeOrder',
              name:'upgradeOrder',
              component:upgradeOrder
            },
            {
              path: 'integralAvatarOrder',
              name:'integralAvatarOrder',
              component:IntegralAvatarOrder
            },
            // // 广告币兑换
            // {
            //   path: '/1083351402008424448',
            //   name: '1083351402008424448',
            //   component: PcRender
            // },
            // // 任务管理
            // {
            //   path: '/1087386237680099328',
            //   name: '1087386237680099328',
            //   component: PcRender
            // },
            //   // 广告公众号管理
            // {
            //   path: '/1087386378977812480',
            //   name: '1087386378977812480',
            //   component: PcRender
            // }
          ]
        },
        // ====================会员增值================
        ...customerIncrementValue,
        // =====================客服中心========================
        {
          path: '/importManagement',
          name: 'ImportManagement',
          component: ImportManagement
        },
        {
          path: '/changeMerchantNameRecord',
          name: 'ChangeMerchantNameRecord',
          component: ChangeMerchantNameRecord
        },
        {
          path: '/replaceMerchantNumberRecord',
          name: 'ReplaceMerchantNumberRecord',
          component: ReplaceMerchantNumberRecord
        },
        {
          path: "/CustomerQuery",
          name: "CustomerQuery",
          component: CustomerQuery
        },
        {
          path: "/CustomerModule",
          name: "CustomerModule",
          component: CustomerModule
        },
        {
          path: "/pageRecords",
          name: "pageRecords",
          component: PageRecords
        },
        {
          path: "/userLogout",
          name: "UserLogout",
          component: UserLogout
        },

        // 业务模块 start
        {
          // 优惠券活动统计
          path: "/coupon",
          name: "coupon",
          component: Coupon,
          redirect: "/coupon/list",
          children: [
            {
              path: "list",
              name: "list",
              component: CouponList,
              meta: { keepAlive: true }
            },
            {
              path: "edit",
              name: "edit",
              component: CouponEdit
            }
          ]
        },
        {
          // 超值卡活动
          path: "/superCard",
          name: "superCard",
          component: SuperCard,
          redirect: "/superCard/list",
          children: [
            {
              path: "list",
              name: "list",
              component: SuperCardList,
              meta: { keepAlive: true }
            },
            {
              path: "edit",
              name: "edit",
              component: SuperCardEdit
            }
          ]
        },
        {
          // 会员卡页面
          path: "/memberCard",
          name: "memberCard",
          component: MemberCard
        },
        {
          // 增值产品归集展示
          path: "/increaseIncomeProduct",
          name: "increaseIncomeProduct",
          component: IncreaseIncomeProduct,
        },
        {
          // 自动分账-插件费统计页面
          path: "/automaticAccountSplitting",
          name: "automaticAccountSplitting",
          component: AutomaticAccountSplitting
        },
        {
          // 拼团购币页面
          path: "/groupBuying",
          name: "groupBuying",
          component: GroupBuying
        },
        {
          //二清结算配置页
          path: "/clearDeploy",
          component: ClearDeploy,
          children: [
            {
              path: "",
              name: "clearDeployList",
              component: ClearDeployList
            },
            {
              path: "edit",
              name: "clearDeployEdit",
              component: ClearDeployEdit
            }
          ]
        },
        {
          // 主账号修改登陆账号记录
          path: "/merchantAccount",
          name: "merchantAccount",
          component: MerchantAccount
        },
        // ...huokeyiActive, // 获客易活动
        // 业务模块 end


        // 交易记录查询
        {
          path: "/transactionRecord",
          name: "transactionRecord",
          component: TransactionRecord
        },
        // 功能介绍中心 start
        // B端后台品类消息推送
        {
          path: "/merchantMessage",
          name: "merchantMessage",
          component: MerchantMessage
        },
        // 教程内容管理
        {
          path: "/tutorial",
          name: "tutorial",
          component: Tutorial,
          redirect: "/tutorial/list",
          children: [
            {
              path: "list",
              name: "list",
              component: TutorialIndex,
              meta: { keepAlive: true }
            },
            {
              path: "edit",
              name: "edit",
              component: TutorialEdit
            }
          ]
        },
        // 常见问题管理
        {
          path: "/problem",
          name: "problem",
          component: Problem
        },
        // 系统消息推送
        {
          path: "/systemMessage",
          name: "systemMessage",
          component: SystemMessage
        },
        // 首页banner管理
        {
          path: "/homeBanner",
          name: "homeBanner",
          component: HomeBanner
        },
        // 产品推文
        {
          path: "/productTweets",
          name: "productTweets",
          component: ProductTweetsRouter,
          redirect: "/productTweets/list",
          children: [
            {
              path: "list",
              name: "list",
              component: ProductTweetsIndex,
              meta: { keepAlive: true }
            },
            {
              path: "edit",
              name: "edit",
              component: ProductTweetsEdit
            }
          ]
        },
        // 功能介绍中心 end
        // 服务套餐配置
        {
          path: "/serviceConfig",
          name: "serviceConfig",
          component: ServiceConfigIndex,
          redirect: "/serviceConfig/list",
          children: [
            {
              path: "list",
              name: "list",
              component: ServiceConfigList,
              meta: { keepAlive: true }
            },
            {
              path: "edit",
              name: "edit",
              component: ServiceConfigEdit
            }
          ]
        },
        // 服务套餐配置-洗水类
        {
          path: "/serviceConfigWash",
          name: "serviceConfigWash",
          component: ServiceConfigWashIndex,
          redirect: "/serviceConfigWash/list",
          children: [
            {
              path: "list",
              name: "list",
              component: ServiceConfigWashList,
              meta: { keepAlive: true }
            },
            {
              path: "edit",
              name: "edit",
              component: ServiceConfigWashEdit
            }
          ]
        },


        // 余币查询
        {
          path: "/userBalance",
          name: "userBalance",
          component: UserBalance
        },
        // 微信客服审核
        {
          path: "/wechatCustomService",
          name: "wechatCustomService",
          component: WechatCustomService
        },
        // 自主广告上线审核
        {
          path: "/autoAdvertie",
          name: "autoAdvertie",
          component: AutoAdvertie
        },
        // 商家调整余额余币记录
        {
          path: "/merchantAdjustmentCoins",
          name: "merchantAdjustmentCoins",
          component: merchantAdjustmentCoins
        },
        // 工厂资料审核
        {
          path: "/factoryInfo",
          name: "factoryInfo",
          component: FactoryInfo
        },
        // 账号注销
        {
          path: "/accountCancellation",
          name: "accountCancellation",
          component: AccountCancellation
        },
        // 消防对接设备
        {
          path: '/fireEquipment',
          name: 'fireEquipment',
          component: FireEquipment
        },
        // 设备交易分析
        {
          path: '/deviceAnalysisSettings',
          name: 'deviceAnalysisSettings',
          component: DeviceAnalysisSettings
        },

        // 代理商客户设备同步记录
        {
          path: '/agentKdEquipment',
          name: 'agentKdEquipment',
          component: agentKdEquipment
        },
        // 客服中心-提现审核
        {
          path: '/withdraw',
          name: 'withdraw',
          component: withdraw
        },
        //===============刷脸业务=============
        {
          path: '/faceBrush',
          name: 'faceBrush',
          component: faceBrushIndex,
          children:[
            {
              path: 'payMinus',
              name: 'payMinus',
              component: faceBrushPayMinus
            },
            {
              path: 'deviceDataList',
              name: 'deviceDataList',
              component: faceBrushDeviceDataList
            },
            {
              path: 'scanPermissionSetting',
              name: 'scanPermissionSetting',
              component: scanPermissionSetting
            }
          ]
        },
        //===============设备管理=============
        {
          path: '/equipmentManage',
          name: 'equipmentManage',
          component:EquipmentManageIndex,
          children:[
            {
              path: 'equipmentQRcodeManage',
              name: 'equipmentQRcodeManage',
              component: equipmentQRcodeManage
            }
          ]
        },
        {
          path: '/equipmentManage/equipmentOfflineSituation',
          name: 'equipmentOfflineSituation',
          component: equipmentOfflineSituation
        },
        // 充电桩升购
        {
          path: '/chargeUpgrade',
          name: 'chargeUpgrade',
          component: ChargeUpgrade,
          redirect: "/chargeUpgrade/onlineListManage",
          children: [
            {
              path: 'onlineListManage',
              name: 'onlineListManage',
              component: OnlineListManage,
            }, {
              path: 'orderList',
              name: 'orderList',
              component: UpgradeOrderList
            }, {
              path: 'hierarchyManage',
              name: 'hierarchyManage',
              component: HierarchyManage,
            }
          ],
        },
        // 充电桩保险加购
        {
          path: '/chargeInsurance',
          name: 'chargeInsurance',
          component: ChargeInsurance,
          // redirect: "/chargeInsurance/onlineListManage",
          children: [
            {
              path: 'onlineListManage',
              name: 'onlineListManage',
              component: OnlineListManageInsurance
            },
            {
              path: 'orderList',
              name: 'orderList',
              component: InsuranceOrderList
            },
            {
              path: 'onlineListManageMonthCard',
              name: 'onlineListManageMonthCard',
              component: OnlineListManageInsuranceMonthCard
            },
            {
              path: 'orderListMonthCard',
              name: 'orderListMonthCard',
              component: InsuranceOrderListMonthCard
            },
            {
              path: 'onlineListManage3',
              name: 'onlineListManage3',
              component: OnlineListManageInsurance3
            },
            {
              path: 'saasOnlineListManage',
              name: 'saasOnlineListManage',
              component: SaasOnlineListManageInsurance,
            },
            {
              path: 'forcedOpeningList',
              name: 'forcedOpeningList',
              component: ForcedOpeningList
            }
          ]
        },
        // 充电桩商创
        {
          path: 'chargeAdvertisement',
          name: 'chargeAdvertisement',
          component: ChargeAdvertisement,
          children: [
            {
              path: 'lvyuanAdvertisement',
              name: 'lvyuanAdvertisement',
              component: LvYuanAdvertisement,
            },
            {
              path: 'flowMiniprogram',
              name: 'flowMiniprogram',
              component: FlowMiniprogram,
            },
            {
              path: 'advertActivityConfig',
              name: 'advertActivityConfig',
              component: AdvertActivityConfig,
            }
          ],
        },
        // {
        //   // 业务服务费收款规则
        //   path: '/1098972575324696576',
        //   name: '1098972575324696576',
        //   component: PcRender
        // },
        // {
        //   // 业务服务费扣款范围设置
        //   path: '/1098980652409155584',
        //   name: '1098980652409155584',
        //   component: PcRender
        // },
        // 娱乐引流
        ...entertainmentDrainage,
        ...cloudCodeRouter,
        ...integralMall, // 积分商城
        ...assembleFansRouter, // 功能性涨粉
        ...infoMatchPlatformRouter, // 信息撮合平台
        ...activitiesQuery, // 增值活动查询
        ...operationAndMaintenance, // 运维操作
        // ...customerService, // 客户服务
        // ...chargingRules, // 收费管理
        // ...subsidy, // 补贴管理
        ...cpcAdvert, // cpc广告
        ...memberManagement, // 会员管理
        // ...preTask, // 前置关注
        // ...dataStatistics // 数据统计
        ...smallVenue, // 多金宝小场地
        ...pageSetting,
        // 智能体应用
        {
          path: '/aiAgent',
          name: 'AiAgent',
          component: AiAgent,
          meta: {
            title: '智能体应用'
          }
        }
      ]
    },

    // { path: '/index', name: 'index', component: Index},
    { path: "/login", name: "login", component: Login, alias: ["/"] }
    // { path: '/login', name: 'login', component: Login},
  ]
});

export default router;
