/*
 * @Description: 基础管理路由
 * @Author: yang<PERSON><PERSON>
 * @Date: 2020-07-07 10:07:34
 * @LastEditors: yangnanfeng
 * @LastEditTime: 2020-07-07 10:07:34
 */


const agreementConfig = resolve => {
  require(["@/views/admin/mainBoardConfig/index"], resolve);
};
// 修改设备型号
const deviceList = resolve => {
  require(["@/views/admin/device/deviceList"], resolve);
};
// 物料上传页面
const MaterielUpload = resolve => {
  require(["@/views/materielUpload/materielUpload"], resolve);
};
// 设备号段规则页面
const EquipNumberRule = resolve => {
  require(["@/views/equipNumberRule/equipNumberRule"], resolve);
};
// 控制功能（按摩手法等）配置
const ArmchairsControlIndex = resolve => {
  require(["@/views/professionalWork/armchairsControl/index.vue"], resolve);
};
// 控制功能（按摩手法等）配置列表
const ArmchairsControlList = resolve => {
  require(["@/views/professionalWork/armchairsControl/armchairsControl.vue"], resolve);
};
// 控制功能（按摩手法等）配置编辑
const ArmchairsControlEdit = resolve => {
  require(["@/views/professionalWork/armchairsControl/armchairsControlSub.vue"], resolve);
};
const ArmchairsControlMonitor=resolve =>{
  require(["@/views/professionalWork/armchairsControl/healthMonitor.vue"], resolve);
};
// 短信标签配置
const SmsTag = resolve => {
  require(["@views/baseManager/smsTag/smsTag.vue"], resolve);
};
// 短信标签配置列表
const SmsTagIndex = resolve => {
  require(["@views/baseManager/smsTag/smsTagIndex.vue"], resolve);
};
// 短信标签配置编辑
const SmsTagSub = resolve => {
  require(["@views/baseManager/smsTag/smsTagSub.vue"], resolve);
};
// 设备解绑记录
const Unbind = resolve => {
  require(["@views/baseManager/unbind/unbind.vue"], resolve);
};
// 单蓝牙设备
const SingleBluetooth = resolve => {
  require(["@views/baseManager/singleBluetooth/singleBluetooth.vue"], resolve);
};
// 有屏设备电子二维码
const deviceQRCode = resolve => {
  require(["@views/deviceQRCode"], resolve);
};
// 代理商客户编码
const AgentClientCode = resolve => {
  require(["@/views/baseManager/agentClientManager/agentClientCode.vue"], resolve)
};
// 设备解绑子路由页面
const EquipmentUnbundling = resolve => {
  require(["@/views/baseManager/equipmentUnbundling/equipmentUnbundling.vue"], resolve)
};
// 设备解绑操作页面
const EquipmentUnbundlingOperation = resolve => {
  require(["@/views/baseManager/equipmentUnbundling/equipmentUnbundlingOperation.vue"], resolve)
};
// 设备解绑历史页面
const EquipmentUnbundlingRecord = resolve => {
  require(["@/views/baseManager/equipmentUnbundling/equipmentUnbundlingRecord.vue"], resolve)
};
// 设备解绑记录页面
const EquipmentUnbundlingDetail = resolve => {
  require(["@/views/baseManager/equipmentUnbundling/equipmentUnbundlingDetail.vue"], resolve)
};
// 设备解绑扫码枪
const EquipmentUnbundlingScan = resolve => {
  require(["@/views/baseManager/equipmentUnbundling/equipmentUnbundlingScan.vue"], resolve)
};
// 通用商品管理
const GeneralMaterialManagement = resolve => {
  require(["@/views/baseManager/generalMaterialManagement/materialList.vue"], resolve)
};
// 脉冲时长设置
const PulseDurationConversion = resolve => {
  require(["@/views/baseManager/pulseDurationConversion/pulseUpload.vue"], resolve)
};
// 第三方系统会员余额
const ThirdpartyMemberBalance = resolve => {
  require(["@/views/baseManager/thirdpartyMemberBalance/pulseUpload.vue"], resolve)
}
// 设备升级管理
const EquipmentUpgradeManagement = resolve => {
  require(["@/views/baseManager/equipmentUpgradeManagement/index.vue"], resolve)
}
// 设备升级管理列表页面
const EquipmentUpgradeManagementList = resolve => {
  require(["@/views/baseManager/equipmentUpgradeManagement/list.vue"], resolve)
}
// 设备升级管理导入页面
const EquipmentUpgradeManagementImport = resolve => {
  require(["@/views/baseManager/equipmentUpgradeManagement/pulseUpload.vue"], resolve)
}
// life自动提现审核，运营
const autoWithDrawOperation = resolve => {
  require(["@/views/finance/autoWithDraw/operationAudit.vue"], resolve)
}
// life自动提现审核，财务
const autoWithDrawFinance = resolve => {
  require(["@/views/finance/autoWithDraw/financeAudit.vue"], resolve)
}
// life自动提现付款记录
const autoWithDrawRecord = resolve => {
  require(["@/views/finance/autoWithDraw/record.vue"], resolve)
}

export const BaseManageRouter = [
  // 主板协议配置
  {
    path: "/agreementConfig",
    name: "agreementConfig",
    component: agreementConfig
  },
  // 修改设备型号
  {
    path: '/deviceList',
    name: 'deviceList',
    component: deviceList
  },
  // 物料上传
  {
    path: "/materielUpload",
    name: "materielUpload",
    component: MaterielUpload
  },
  // 设备号段规则
  {
    path: "/equipNumberRule",
    name: "equipNumberRule",
    component: EquipNumberRule
  },
  // 控制功能（按摩手法等）配置
  {
    path: "/armchairsControl",
    name: "armchairsControl",
    component: ArmchairsControlIndex,
    redirect: "/armchairsControl/list",
    children: [
      // 控制功能（按摩手法等）配置列表
      {
        path: "list",
        name: "list",
        component: ArmchairsControlList,
        meta: {
          keepAlive: true
        }
      },
      // 控制功能（按摩手法等）配置编辑
      {
        path: "edit",
        name: "edit",
        component: ArmchairsControlEdit
      },
      //控制功能（按摩手法等）健康监测
      {
        path: "monitor",
        name: "monitor",
        component: ArmchairsControlMonitor
      }
    ]
  },
  // 短信标签配置
  {
    path: "/smsTag",
    name: "smsTag",
    component: SmsTagIndex,
    redirect: "/smsTag/list",
    children: [
      // 短信标签配置列表
      {
        path: "list",
        name: "list",
        component: SmsTag
      },
      // 短信标签配置编辑
      {
        path: "edit",
        name: "edit",
        component: SmsTagSub
      }
    ]
  },
  // 设备解绑记录
  {
    path: "/unbind",
    name: "unbind",
    component: Unbind
  },
  // 单蓝牙设备
  {
    path: "/singleBluetooth",
    name: "singleBluetooth",
    component: SingleBluetooth
  },
  // 有屏设备电子二维码
  {
    path: "/deviceQRCode",
    name: "deviceQRCode",
    component: deviceQRCode
  },
  // 代理商客户编码
  {
    path: '/agentClientCode',
    name: 'agentClientCode',
    component: AgentClientCode
  },
  // 设备解绑
  {
    path: '/equipmentUnbundling',
    name: 'equipmentUnbundling',
    component: EquipmentUnbundling,
    redirect: "/equipmentUnbundling/operation",
    children: [
      // 设备解绑
      {
        path: "operation",
        name: "operation",
        component: EquipmentUnbundlingOperation
      },
      // 设备解绑历史
      {
        path: "record",
        name: "equipmentUnbundlingRecord",
        component: EquipmentUnbundlingRecord
      },
      // 设备解绑记录
      {
        path: "detail",
        name: "equipmentUnbundlingDetail",
        component: EquipmentUnbundlingDetail
      },
      // 设备解绑扫码枪
      {
        path: "scan",
        name: "scan",
        component: EquipmentUnbundlingScan
      }
    ]
  },
  // 通用商品管理
  {
    path: '/generalMaterialManagement',
    name: 'generalMaterialManagement',
    component: GeneralMaterialManagement
  },
  // 脉冲时长设置
  {
    path: '/pulseDurationConversion',
    name: 'PulseDurationConversion',
    component: PulseDurationConversion
  },
  // 第三方系统会员余额
  {
    path: '/thirdpartyMemberBalance',
    name: 'ThirdpartyMemberBalance',
    component: ThirdpartyMemberBalance
  },
  // 设备升级管理
  {
    path: '/equipmentUpgradeManagement',
    name: 'equipmentUpgradeManagement',
    component: EquipmentUpgradeManagement,
    redirect: "/equipmentUpgradeManagement/list",
    children: [
      {
        path: 'list',
        name: 'equipmentUpgradeManagementList',
        component: EquipmentUpgradeManagementList,
      },
      // 设备升级管理导入
      {
        path: 'import',
        name: 'equipmentUpgradeManagementImport',
        component: EquipmentUpgradeManagementImport
      },
    ]
  },
  // life自动提现付款记录
  {
    path: '/autoWithDrawRecord',
    name: 'autoWithDrawRecord',
    component: autoWithDrawRecord
  },
  // life自动提现审核，运营
  {
    path: '/autoWithDrawOperation',
    name: 'autoWithDrawOperation',
    component: autoWithDrawOperation
  },
  // life自动提现审核，财务
  {
    path: '/autoWithDrawFinance',
    name: 'autoWithDrawFinance',
    component: autoWithDrawFinance
  },
]
