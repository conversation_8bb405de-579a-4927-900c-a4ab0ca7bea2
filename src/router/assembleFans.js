/**
 * @Description: 功能性涨粉
 * @Author: wutao
 * @Date: 2022/5/9 17:34
 * @LastEditors: wutao
 * @LastEditTime: 2022/5/9 17:34
 */

const assembleFansRouter =  [
  {
    path: "/assembleFans",
    name: "assembleFans",
    component: () => import(/* webpackChunkName: "AssembleFansIndex" */ "@/views/assembleFans/index.vue"),
    redirect: "/assembleFans/merchantList",
    children:[
      { // 商家名单管理
        path: "merchantList",
        name: "merchantList",
        component: () => import(/* webpackChunkName: "AssembleFansMerchantList" */ "@/views/assembleFans/children/merchantList.vue")
      },
      { // 账号自定义配置
        path: "accountConfiguration",
        name: "accountConfiguration",
        component: () => import(/* webpackChunkName: "AssembleFansAccountConfiguration" */ "@/views/assembleFans/children/accountConfiguration.vue")
      },
      { // 账号组详情
        path: "accountDetail",
        name: "accountDetail",
        component: () => import(/* webpackChunkName: "AssembleFansAccountDetail" */ "@/views/assembleFans/children/accountDetail.vue")
      },
      { // 组标签列表
        path: "groupTagList",
        name: "groupTagList",
        component: () => import(/* webpackChunkName: "AssembleFansGroupTagList" */ "@/views/assembleFans/children/groupTagList.vue")
      },
      { // 公众号管理
        path: "officialAccountManage",
        name: "officialAccountManage",
        component: () => import(/* webpackChunkName: "AssembleFansOfficialAccountManage" */ "@/views/assembleFans/children/officialAccountManage.vue")
      },
    ]
  }
]

export default assembleFansRouter
