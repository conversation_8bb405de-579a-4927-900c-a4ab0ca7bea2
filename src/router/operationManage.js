/*
* @Description: 运营管理中心路由
* @Author: ya<PERSON><PERSON><PERSON>
* @Date: 2020-07-07 10:07:34
* @LastEditors: yang<PERSON><PERSON>
* @LastEditTime: 2020-07-07 10:07:34
*/
// const PcRender = () => import("@/components/PcRender/index")

const WhiteListIndex = resolve => {
  require(["@/views/OperationManage/whiteList/index"], resolve);
}; // 白名单管理首页
const FunctionList = resolve => {
  require(["@/views/OperationManage/whiteList/children/functionList"], resolve);
}; // 功能白名单
const SensitiveList = resolve => {
  require(["@/views/OperationManage/whiteList/children/sensitiveList"], resolve);
}; // 广告敏感商家列表
const QuicklyList = resolve => {
  require(["@/views/OperationManage/whiteList/children/quicklyList"], resolve);
}; // 广告快捷加名单
const StoreList = resolve => {
  require(["@/views/OperationManage/whiteList/children/storeList"], resolve);
}; // 商户白名单
const BlackList = resolve => {
  require(["@/views/OperationManage/blackList/index"], resolve);
}; // 黑名单管理
const UnlimitCouponList = resolve => {
  require(["@/views/OperationManage/unlimitCouponList/index"], resolve);
}; // 无限制优惠券列表
const JdCouponLimit = resolve => {
  require(["@/views/OperationManage/JdCouponLimit/index"], resolve);
}; // 京东限额提醒
const CouponStatisticsIndex = resolve => {
  require(["@/views/OperationManage/couponStatistics/index"], resolve);
}; // 优惠券数据统计首页
const DataList = resolve => {
  require(["@/views/OperationManage/couponStatistics/children/dataList"], resolve);
}; // 数据统计
const CouponQueryList = resolve => {
  require(["@/views/OperationManage/couponStatistics/children/couponList"], resolve);
}; // 优惠券查询
const SaasDistributor = resolve => {
  require(['@/views/OperationManage/saas/saasDistributor/saasDistributor.vue'], resolve)
}; // b端商家使用saas版本详情
const SaasStrategy = resolve => {
  require(['@/views/OperationManage/saas/saasStrategy/saasStrategy.vue'], resolve)
}; // b端saas策略
const SaasStrategySub = resolve => {
  require(['@/views/OperationManage/saas/saasStrategy/saasStrategySub.vue'], resolve)
}; // b端saas策略创建/编辑
const SaasVersion = resolve => {
  require(['@/views/OperationManage/saas/saasVersion/saasVersion.vue'], resolve)
}; // b端saas版本
const SaasVersionSub = resolve => {
  require(['@views/OperationManage/saas/saasVersion/saasVersionSub.vue'], resolve)
}; // b端saas版本创建/编辑
// 微信先享卡
const WechatEnjoyFirstCard = resolve => {require(["@/views/OperationManage/wechatEnjoyFirstCard/wechatEnjoyFirstCard.vue"],resolve)};
// 设备充值率活动
const EquipmentRechargeActivity = resolve => {require(["@/views/OperationManage/equipmentRechargeActivity/equipmentRechargeActivity.vue"],resolve)};
// 运营一分购
const OneCentBuy = resolve => {require(["@/views/OperationManage/oneCentBuy/oneCentBuy.vue"],resolve)};
// openPay接入
const OpenPay = resolve => {require(["@/views/OperationManage/openPay/openPay.vue"],resolve)};
// 机台定向券活动
const DirectionalCoupon = resolve => {require(["@/views/OperationManage/directionalCoupon/directionalCoupon.vue"],resolve)};
// 红包数据统计
const RedPacketStatistics = resolve => {require(["@/views/OperationManage/redPacketStatistics/redPacketStatistics"],resolve)};
// 美团/抖音功能入口配置
const entryConfig = resolve => {require(["@/views/superAbility/entryConfig/index.vue"],resolve)};
// 美团点评新用户活动
const MTCommentCoupon = resolve => {require(["@/views/OperationManage/MTCommentCoupon/MTCommentTabs"],resolve)};
// 美团活动配置
const MTActivity = resolve => {require(["@/views/OperationManage/meituanActivityManage/index.vue"],resolve)};
// 抖音功能
const tiktok = resolve => {require(["@/views/superAbility/tiktok/index.vue"],resolve)};
// 抖音核销
const tiktokVerification = resolve => {require(["@/views/superAbility/tiktok/verification/index.vue"],resolve)};
// 抖音爆店码
const tiktokBoomCode = resolve => { require(["@/views/superAbility/tiktok/boomCode/index.vue"], resolve) };
// 快手核销
// const kuaishouVerification = resolve => { require(["@/views/superAbility/kuaishou/index.vue"], resolve) };
// 京东一分购
const jdDiscountBuyIndex = resolve => {require(["@views/OperationManage/jdDiscountBuy/jdDiscountBuyIndex.vue"],resolve)};
// 商家提现提醒记录
const merchantWithdrawRemindRecord = resolve => {require(["@views/OperationManage/merchantWithdrawRemindRecord/merchantWithdrawRemindRecord.vue"],resolve)};
// 交易风控操作日志
const transactionLog = resolve => {require(["@views/OperationManage/merchant/transactionLog.vue"],resolve)};
// 商户经营信息
const merchantBusinessInfo = resolve => {require(["@views/OperationManage/merchant/businessInfo.vue"],resolve)};
// 交易风控管理
const transactionRisk = resolve => {require(["@views/OperationManage/merchant/transactionRisk.vue"],resolve)};
// 离线下载日志
const offlineLog = resolve => {require(["@views/OperationManage/merchant/offlineLog.vue"],resolve)};
// 商户详情
const merchantData = resolve => {require(["@views/OperationManage/merchant/merchantData.vue"],resolve)};
// 渠道切换记录
const channelSwitchRecord = resolve => {require(["@views/finance/channelSwitch/channelSwitchRecord.vue"],resolve)};
// 筛选目标商户
const channelSwitchInfo = resolve => {require(["@views/finance/channelSwitch/channelSwitchInfo.vue"],resolve)};
//  支付分绑定记录
const paymentCodeBindingRecord = resolve => {require(['@/views/OperationManage/paymentCodeBindingRecord/index'], resolve)};

// 商家分账对账单
const statementOfAccount = resolve => {require(["@views/payment/statementOfAccount.vue"],resolve)};
// 商家分账设置
const statementSet = resolve => {require(["@views/payment/statementSet/index.vue"],resolve)};
// 分账业务对账数据
const statementData = resolve => {require(["@views/payment/statementData.vue"],resolve)};
// 个性化推荐管理
const PersonalizedRecommendationManagement = resolve => {require(['@/views/PersonalizedRecommendationManagement/PersonalizedRecommendationManagement'], resolve)};
// 支付分设备类型设置
const equipmentTypeSetting = resolve => {require(["@views/OperationManage/equipmentTypeSetting/index"], resolve)};
// 用户余额余币查询
const userQuery = resolve => {require(["@views/OperationManage/member/userQuery/userQuery.vue"],resolve)};
// 支付分商家路由
const payscoreAdOrg = resolve => {require(["@views/OperationManage/payscoreAdOrg/index"],resolve)};

export const OperationManageRouter = [
  {
    path: "/WhiteListIndex",
    name: "WhiteListIndex",
    component: WhiteListIndex,
    redirect: "/WhiteListIndex/FunctionList",
    children: [
      {
        path: "FunctionList",
        name: "FunctionList",
        component: FunctionList
      },
      {
        path: "StoreList",
        name: "StoreList",
        component: StoreList
      },
      {
        path: "SensitiveList",
        name: "SensitiveList",
        component: SensitiveList
      },
      {
        path: 'QuicklyList',
        name: 'QuicklyList',
        component: QuicklyList
      }
    ]
  },
  {
    path: "/blackList",
    name: "BlackList",
    component: BlackList
  },
  { path: "/UnlimitCouponList", name: "UnlimitCouponList", component: UnlimitCouponList },
  { path: "/JdCouponLimit", name: "JdCouponLimit", component: JdCouponLimit },
  {
    path: "/CouponStatisticsIndex",
    name: "CouponStatisticsIndex",
    component: CouponStatisticsIndex,
    redirect: "/CouponStatisticsIndex/DataList",
    children: [
      {
        path: "DataList",
        name: "DataList",
        component: DataList
      },
      {
        path: "CouponQueryList",
        name: "CouponQueryList",
        component: CouponQueryList
      }
    ]
  },
  { // b端商家使用saas版本详情
    path: '/saasDistributor',
    name: 'SaasDistributor',
    component: SaasDistributor,
  },
  { // b端saas策略
    path: '/saasStrategy',
    name: 'saasStrategy',
    component: SaasStrategy,
  },
  { // b端saas策略创建/编辑
    path: '/saasStrategySub',
    name: 'saasStrategySub',
    component: SaasStrategySub,
  },
  { // b端saas版本
    path: '/saasVersion',
    name: 'SaasVersion',
    component: SaasVersion,
  },
  { // b端saas版本创建/编辑
    path: '/saasVersionSub',
    name: 'SaasVersionSub',
    component: SaasVersionSub,
  },
  { path: "/wechatEnjoyFirstCard", name: "wechatEnjoyFirstCard", component: WechatEnjoyFirstCard }, // 微信先享卡
  { path: "/equipmentRechargeActivity", name: "equipmentRechargeActivity", component: EquipmentRechargeActivity }, // 设备充值率活动
  { path: "/oneCentBuy", name: "oneCentBuy", component: OneCentBuy }, // 运营一分购
  { path: "/openPay", name: "openPay", component: OpenPay }, // openPay接入
  { path: "/directionalCoupon", name: "directionalCoupon", component: DirectionalCoupon }, // 机台定向券活动
  { path: "/redPacketStatistics", name: "redPacketStatistics", component: RedPacketStatistics }, // 红包数据统计
  { path: "/entryConfig", name: "entryConfig", component: entryConfig }, // 美团/抖音功能入口配置
  { path: "/MTCommentCoupon", name: "MTCommentCoupon", component: MTCommentCoupon }, // 美团点评新用户活动
  { path: "/MTActivity", name: "MTActivity", component: MTActivity }, // 美团活动配置
  // // 美团商家入驻信息
  // {
  //   path: '/1093903725851185152',
  //   name: '1093903725851185152',
  //   component: PcRender,
  // },
  // // 渠道路由操作记录
  // {
  //   path: '/1105464130434838528',
  //   name: '1105464130434838528',
  //   component: PcRender,
  // },
  { path: "/tiktok", name: "tiktok", component: tiktok, }, // 抖音功能
  { path: "/tiktokVerification", name: "tiktokVerification", component: tiktokVerification, }, // 抖音核销
  { path: "/tiktokBoomCode", name: "tiktokBoomCode", component: tiktokBoomCode, }, // 抖音核销
  // { path: "/kuaishouVerification", name: "kuaishouVerification", component: kuaishouVerification, }, // 快手核销
  { path: "/jdDiscountBuy", name: "jdDiscountBuy", component: jdDiscountBuyIndex }, // 京东一分购
  { path: "/merchantWithdrawRemindRecord", name: "merchantWithdrawRemindRecord", component: merchantWithdrawRemindRecord }, // 商家提现提醒记录
  { path: "/transactionLog", name: "transactionLog", component: transactionLog }, // 交易风控操作日志
  { path: "/merchantBusinessInfo", name: "merchantBusinessInfo", component: merchantBusinessInfo }, // 商户经营信息
  { path: "/transactionRisk", name: "transactionRisk", component: transactionRisk }, // 交易风控管理
  { path: "/offlineLog", name: "offlineLog", component: offlineLog }, // 离线下载日志
  { path: "/merchantData", name: "merchantData", component: merchantData }, // 商户详情
  { path: "/channelSwitchRecord", name: "channelSwitchRecord", component: channelSwitchRecord }, // 渠道切换记录
  { path: "/channelSwitchInfo", name: "channelSwitchInfo", component: channelSwitchInfo }, // 筛选目标商户
  { path: "/statementOfAccount", name: "statementOfAccount", component: statementOfAccount }, // 商家分账对账单
  { path: "/statementSet", name: "statementSet", component: statementSet }, // 商家分账设置
  { path: "/statementData", name: "statementData", component: statementData }, // 分账业务对账数据
  { path: '/paymentCodeBindingRecord', name: 'PaymentCodeBindingRecord', component: paymentCodeBindingRecord }, //  支付分绑定记录
  { path: '/PersonalizedRecommendationManagement', name: 'PersonalizedRecommendationManagement', component: PersonalizedRecommendationManagement }, // 个性化推荐管理
  { path: "/equipmentTypeSetting", name: "equipmentTypeSetting", component: equipmentTypeSetting }, // 支付分设备类型设置
  { path: "/userQuery", name: "userQuery", component: userQuery }, // 用户余额余币查询
  { path: "/payscoreAdOrgRoute", name: "payscoreAdOrg", component: payscoreAdOrg }, // 用户余额余币查询
];
