// const PcRender = () => import("@/components/PcRender/index")
// export default [
//   // 分账查询
//   {
//     path: '/1077250072853135360',
//     component: () => import('@/components/PcRender/index.vue'),
//     name: 'TradeDetailList',
//   },
//   // 交易明细
//   {
//     path: '/1077250027810504704',
//     component: () => import('@/components/PcRender/index.vue'),
//     name: 'PaymentTradeManagement',
//   },
//   // 分账单详情
//   {
//     path: '/1077591247787180032',
//     component: () => import('@/components/PcRender/index.vue'),
//     name: 'DivideApplyRecordList',
//   },
//   // 商户关联绑定
//   {
//     path: '/1083472984534036480',
//     name: '1083472984534036480',
//     component: <PERSON><PERSON><PERSON><PERSON>,
//   }
// ]
