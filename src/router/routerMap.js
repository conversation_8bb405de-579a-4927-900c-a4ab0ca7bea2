export const routerMap = [
  {
    value: 'w_payscoreBindRecordList',
    name: '支付分绑定记录',
    html_templet: '/paymentCodeBindingRecord',
  },
  {
    value: 'upgrade4g_device',
    name: '设备升级管理',
    html_templet: '/equipmentUpgradeManagement/list',
  },
  {
    value: 'userServiceSwitchApplyList',
    name: '个性化推荐管理',
    html_templet: '/PersonalizedRecommendationManagement',
  },
  { value: 'LogManage', name: '日志管理', html_templet: '' },
  {
    value: 'w_payscoreEquipmentTypeSetting',
    name: '支付分设备配置',
    html_templet: '/equipmentTypeSetting',
  },
  { value: 'channelSwitchRecord', name: '渠道切换记录', html_templet: '/channelSwitchRecord' },
  { value: 'channelRouteManage', name: '渠道路由管理', html_templet: '/channelSwitchInfo' },
  { value: 'ClientUserQuery', name: 'C端用户查询', html_templet: '/userQuery' },
  {
    value: 'w_scanPermissionSetting',
    name: '扫码支付权限设置',
    html_templet: '/faceBrush/scanPermissionSetting',
  },
  { value: 'MerchantDivideInstall', name: '商户分账设置', html_templet: '/statementSet' },
  {
    value: 'HistoryPayChannelData',
    name: '历史交易汇总',
    html_templet: '/HistoryPayChannelIndex/HistoryPayChannelData',
  },
  { value: 'DivideOrderBill', name: '商家分账对账单', html_templet: '/statementOfAccount' },
  {
    value: 'DrainageShopBrandConfig',
    name: '商家品牌配置',
    html_templet: '/entertainmentDrainage/shopBrandConfig',
  },
  {
    value: 'DrainageLifeShopSwitch',
    name: '生活设备导流开关设置',
    html_templet: '/entertainmentDrainage/lifeShopSwitch',
  },
  { value: 'LookUpAdActive', name: '活动条件查询', html_templet: '/advertisement/lookUpAdActive' },
  { value: 'MerchantVersionQuery', name: '商户版本查询', html_templet: '/saasDistributor' },
  {
    value: 'pulse_duration_conversion',
    name: '脉冲时长设置',
    html_templet: '/pulseDurationConversion',
  },
  {
    value: 'SaasExtFunctionTemplate',
    name: 'B端设备扩展功能配置',
    html_templet: '/SaasDeviceActionAm',
  },
  {
    value: 'CloudCodeExchangeSetting',
    name: '兑换设置',
    html_templet: '/cloudCode/exchangeSetting',
  },
  { value: 'synthesizeImportManagement', name: '综合进件管理', html_templet: '/importManagement' },
  { value: 'redPacketStatistics', name: '红包数据统计', html_templet: '/redPacketStatistics' },
  { value: 'CustomerSearch', name: '客服查询', html_templet: '/CustomerQuery' },
  { value: 'MerchantLogoutRecord', name: '商户注销记录查询', html_templet: '/accountCancellation' },
  {
    value: 'onlineNameManage',
    name: '上线名单管理',
    html_templet: '/chargeInsurance/onlineListManageMonthCard',
  },
  { value: 'MerchantAccountModify', name: '修改手机号记录', html_templet: '/merchantAccount' },
  { value: 'deletedNdjPosition', name: '删除仓位-扭蛋机', html_templet: '/delPosition' },
  {
    value: 'SingleDayPayChannelData',
    name: '单日交易汇总',
    html_templet: '/SingleDayPayChannelIndex/SingleDayPayChannelData',
  },
  {
    value: 'DrainageTemplate',
    name: '活动模板配置',
    html_templet: '/entertainmentDrainage/templateConfig',
  },
  {
    value: 'distributorChargeDetail',
    name: '商家设备收费明细',
    html_templet: '/commercialize/deveiceFreeDetail',
  },
  { value: 'SaasOnlineReport', name: '上线数据统计', html_templet: '/saasReport' },
  {
    value: 'FaceEquipmentDataDetails',
    name: '刷脸设备数据详情',
    html_templet: '/faceBrush/deviceDataList',
  },
  { value: 'BackerActivity', name: '金主套餐B', html_templet: '/advertisement/BackerActivity' },
  {
    value: 'meituanPackageApply',
    name: '商家团购设置申请',
    html_templet: '/GroupCheckIndex/GroupCheckList',
  },
  { value: 'SaasShipmentReport', name: '工厂出货统计', html_templet: '/SaasSalesStatistics' },
  { value: 'SignHistory', name: '签署历史记录', html_templet: '/protocol/protocolHistory' },
  { value: 'AdRaffle', name: '兑多多翻倍', html_templet: '/advertisement/dbjDoubleCoins' },
  { value: 'aWaitPay', name: '待支付商家', html_templet: '/MeiTuan/PayList' },
  {
    value: 'ProducerOwnPaymentChannels',
    name: '生产商自有支付渠道',
    html_templet: '/merchantPayChannel',
  },
  { value: 'AgentCustomerCode', name: '代理商客户编码', html_templet: '/agentClientCode' },
  { value: 'AgentKdEquipment', name: '代理商设备同步记录', html_templet: '/agentKdEquipment' },
  {
    value: 'whiteListManagement',
    name: '白名单管理列表',
    html_templet: '/WhiteListIndex/FunctionList',
  },
  { value: 'FirstDiscountMenu', name: '新用户首单优惠统计', html_templet: '/newUserOptimization' },
  { value: 'ResourcesManage', name: '资源管理', html_templet: '' },
  {
    value: 'CommonMaterialManagement',
    name: '通用商品管理',
    html_templet: '/generalMaterialManagement',
  },
  { value: 'SingleBluetoothEquipment', name: '单蓝牙设备', html_templet: '/singleBluetooth' },
  { value: 'AdvertCloudCode', name: '云码活动记录', html_templet: '/advertisement/cloudCode' },
  {
    value: 'OfflineFansActivity',
    name: '离场涨粉产品',
    html_templet: '/advertisement/offlineFansActivity',
  },
  {
    value: 'SaasStatistic',
    name: 'B端设备数据统计模板',
    html_templet: '/SaasStatisticsTemSetIndex/SaasStatisticsTemList',
  },
  {
    value: 'onlineListManage',
    name: '上线名单管理',
    html_templet: '/chargeUpgrade/onlineListManage',
  },
  { value: 'fireEquipment', name: '消防对接设备', html_templet: '/fireEquipment' },
  {
    value: 'thirdparty_member_balance',
    name: '第三方系统会员余额（币）',
    html_templet: '/thirdpartyMemberBalance',
  },
  {
    value: 'EquipmentQRcodeManage',
    name: '设备二维码管理',
    html_templet: '/equipmentManage/equipmentQRcodeManage',
  },
  { value: 'noticePublish', name: '公告发布', html_templet: '/announcement/publish' },
  { value: 'SaasTemplate', name: 'C端界面模板配置', html_templet: '/SaasTempList' },
  { value: 'ProductPush', name: '产品推文', html_templet: '/productTweets/list' },
  { value: 'HomePageBanner', name: '首页banner管理', html_templet: '/homeBanner' },
  {
    value: 'LyyCouponStatistics',
    name: '优惠券统计',
    html_templet: '/CouponStatisticsIndex/DataList',
  },
  { value: 'AdvertCombineCoins', name: '加粉拼币', html_templet: '/advertisement/combineCoins' },
  { value: 'customerServiceModel', name: '客服模块管理', html_templet: '/CustomerModule' },
  {
    value: 'SysAutoWithdrawAudit',
    name: 'life自动提现付款记录',
    html_templet: '/autoWithDrawRecord',
  },
  { value: 'CwCard', name: '畅玩卡设置', html_templet: '/customerAddValue/cWcard' },
  { value: 'EquipmentErrorLog', name: '设备离线明细', html_templet: '/deviceOffline' },
  {
    value: 'SaasTemplateRule',
    name: '设备注册与优惠规则',
    html_templet: '/SaasRegRuleIndex/SaasRegRuleList',
  },
  {
    value: 'EquipmentUnbindingRecords',
    name: '设备解绑',
    html_templet: '/equipmentUnbundling/operation',
  },
  {
    value: 'ControlMassageServicePackage',
    name: '控制功能（按摩手法等）配置',
    html_templet: '/armchairsControl/list',
  },
  { value: 'Materiel', name: '物料上传', html_templet: '/materielUpload' },
  { value: 'SaasVersionConfig', name: 'SaaS版本配置', html_templet: '/saasVersion' },
  {
    value: 'w_merchantBalanceCharge',
    name: '商户充值',
    html_templet: '/integralMall/merchantBalance',
  },
  {
    value: 'DrainageEntertainmentShopSwitch',
    name: '商家引流功能开关设置',
    html_templet: '/entertainmentDrainage/entertainmentShopSwitch',
  },
  { value: 'SaasStrategyConfig', name: 'SaaS策略配置', html_templet: '/saasStrategy' },
  {
    value: 'UpgradeOrderManage',
    name: '娃娃机升单查询',
    html_templet: '/advertisement/upgradeOrder',
  },
  { value: 'withdrawAudit', name: '提现审核', html_templet: '/withdraw' },
  {
    value: 'deviceBillingSettings',
    name: '设备收费账单设置',
    html_templet: '/commercialize/theBillChargeSeting',
  },
  {
    value: 'AssembleFansDistributor',
    name: '商家名单管理',
    html_templet: '/assembleFans/merchantList',
  },
  {
    value: 'AssembleFansGroup',
    name: '账号自定义配置',
    html_templet: '/assembleFans/accountConfiguration',
  },
  {
    value: 'AdvertiseSubsidy',
    name: '广告商家分成补贴',
    html_templet: '/advertisement/advertiseSubsidy',
  },
  {
    value: 'AccountCheckImport',
    name: '对账数据导入',
    html_templet: '/FinanceFileRecordIndex/ImportData',
  },
  {
    value: 'WithdrawFinancialAudit',
    name: 'life自动提现审核（财务）',
    html_templet: '/autoWithDrawFinance',
  },
  { value: 'CloudCodeLottery', name: '抽奖设置', html_templet: '/cloudCode/lotterySetting' },
  {
    value: 'WithdrawOperateAudit',
    name: 'life自动提现审核（运营）',
    html_templet: '/autoWithDrawOperation',
  },
  {
    value: 'PayChannelChangeManage',
    name: '系统操作记录',
    html_templet: '/riskManagement/payChannelLog',
  },
  { value: 'tradeActivity', name: '设备充值率活动', html_templet: '/equipmentRechargeActivity' },
  {
    value: 'PaymentWithdrawalStatistis',
    name: '报税数据导出',
    html_templet: '/riskManagement/taxExport',
  },
  { value: 'MemberCard', name: '会员卡', html_templet: '/memberCard' },
  { value: 'protocolTypeList', name: '协议类型管理', html_templet: '/protocol/protocolTypeList' },
  { value: 'AdRaffleDouble', name: '兑多多翻倍', html_templet: '/advertisement/dbjLottery' },
  { value: 'platformOperate', name: '京东一分购币', html_templet: '/jdDiscountBuy' },
  {
    value: 'PayChannelManage',
    name: '支付渠道管理',
    html_templet: '/PaymentChannelIndex/PaymentChannelList',
  },
  { value: 'CommonQuestion', name: '常见问题管理', html_templet: '/problem' },
  { value: 'CloudCodeTask', name: '能量任务', html_templet: '/cloudCode/adTask' },
  {
    value: 'ScreenElectronicQRcodeEquipment',
    name: '有屏设备电子二维码',
    html_templet: '/deviceQRCode',
  },
  { value: 'SuperValueCard', name: '超值卡活动', html_templet: '/superCard/list' },
  {
    value: 'DividePluginCharge',
    name: '自动分账-插件费统计',
    html_templet: '/automaticAccountSplitting',
  },
  { value: 'lalLoanDeduction', name: '还款抵扣(合作方)', html_templet: '/lakala' },
  {
    value: 'PayAccountCheck',
    name: '支付金额对账',
    html_templet: '/FinanceCheckListIndex/CheckList',
  },
  { value: 'insuranceOrderList', name: '订单列表', html_templet: '/chargeInsurance/orderList' },
  { value: 'DrainageGift', name: '礼品兑换', html_templet: '/entertainmentDrainage/drainageGift' },
  { value: 'LcwDrainage', name: '"乐潮玩"引流', html_templet: '/lechaowan' },
  {
    value: 'CouponStatistics',
    name: '优惠券数据统计',
    html_templet: '/CouponStatisticsIndex/DataList',
  },
  { value: 'JDCouponAmountLimit', name: '京东优惠券每日限额', html_templet: '/JdCouponLimit' },
  { value: 'EquipmentNumberRule', name: '设备号段规则', html_templet: '/equipNumberRule' },
  { value: 'CourseContent', name: '教程内容管理', html_templet: '/tutorial/list' },
  { value: 'AdvertDbjLottery', name: '兑多多抽奖', html_templet: '/advertisement/dbjLottery' },
  { value: 'SystemMessagePush', name: '系统消息推送', html_templet: '/systemMessage' },
  {
    value: 'insuranceCardList',
    name: '订单列表',
    html_templet: '/chargeInsurance/orderListMonthCard',
  },
  { value: 'agreementList', name: '签署协议列表', html_templet: '/protocol/protocolList' },
  { value: 'SaasPluginConfig', name: '插件配置', html_templet: '/SaasPlugInIndex/SaasPlueInList' },
  { value: 'SmsTagConfig', name: '短信标签配置', html_templet: '/smsTag/list' },
  { value: 'UserCoins', name: '余币查询', html_templet: '/userBalance' },
  { value: 'AdvertVipFans', name: '加粉VIP', html_templet: '/advertisement/vipTask' },
  {
    value: 'MerchantBalanceSettlement',
    name: '余额变更记录查询',
    html_templet: '/riskManagement/balanceChangeRecord',
  },
  {
    value: 'PlatformBalanceSettlement',
    name: '余额对账',
    html_templet: '/riskManagement/balanceCheck',
  },
  {
    value: 'EquipmentOfflineSituation',
    name: '设备离线跟踪',
    html_templet: '/equipmentManage/equipmentOfflineSituation',
  },
  { value: 'CustomerServiceAudit', name: '微信客服审核', html_templet: '/wechatCustomService' },
  { value: 'FactoryDataAudit', name: '工厂资料审核', html_templet: '/factoryInfo' },
  { value: 'UserManage', name: '用户管理', html_templet: '' },
  { value: 'cycleCoupon', name: '无限制优惠券', html_templet: '/UnlimitCouponList' },
  { value: 'equipmentAnalysis', name: '设备分析功能设置', html_templet: '/deviceAnalysisSettings' },
  { value: 'operateDiscount', name: '运营一分购', html_templet: '/onecentbuy' },
  { value: 'IccidManagement', name: '物联卡管理', html_templet: '/simCard' },
  { value: 'meituanMerchant', name: '美团商家入驻信息', html_templet: '/1093903725851185152' },
  { value: 'cc-b_manageEquipment', name: '汽充管理-设备管理', html_templet: '' },
  { value: 'w_payscoreAdOrgRoute', name: '支付分商家路由', html_templet: '/payscoreAdOrgRoute' },
  {
    value: 'CommonEquipmentManagement',
    name: '设备信息',
    html_templet: '/equipment/equipmentInfo',
  },
  {
    value: 'nameListManage3',
    name: '上线名单管理-3.0',
    html_templet: '/chargeInsurance/onlineListManage3',
  },
  // {
  //   value: 'EquipmentOnlineDataReport',
  //   name: '设备在线报表',
  //   html_templet: '/dataReports/onlineReportsDevices',
  // },
  { value: 'PromotionActivityQuery', name: '增值活动查询', html_templet: '/activitiesQuery' },
  { value: 'Idle_Coins', name: '闲时币', html_templet: '/1087386573320888320' },
  {
    value: 'ExperienceMerchantActivate',
    name: '体验期开通',
    html_templet: '/paymentManagement/experiencePeriod',
  },
  { value: 'GoldenPackage', name: '金主套餐', html_templet: '/advertisement/goldenPackage' },
  { value: 'paymentRisk', name: '支付风控管理', html_templet: '/transactionRisk' },
  { value: 'equipmentHandleLog', name: '设备操作记录', html_templet: '/transactionLog' },
  { value: 'offlineExportLog', name: '离线下载记录', html_templet: '/offlineLog' },
  { value: 'MerchantAuth', name: '商户实名列表', html_templet: '/paymentManagement/merchantAuth' },
  {
    value: 'IndependentAdvertisingOnline',
    name: '自主广告上线审核',
    html_templet: '/autoAdvertie',
  },
  { value: 'DealRecordSearch', name: '交易记录查询', html_templet: '/transactionRecord' },
  { value: 'protocolFunction', name: '主板功能配置', html_templet: '/agreementConfig' },
  { value: 'signRecord', name: '注册签约记录', html_templet: '/signRecordList' },
  {
    value: 'userPriceSetting',
    name: '用户价格分层设置',
    html_templet: '/chargeUpgrade/hierarchyManage',
  },
  { value: 'MessageTemplate', name: '消息模板', html_templet: '/1085925735376494592' },
  {
    value: 'UpgradeOrder',
    name: '娃娃机升单配置',
    html_templet: '/advertisement/commissionRatioSetting',
  },
  { value: 'AssembleFansScene', name: '组类型名单', html_templet: '/assembleFans/groupTagList' },
  {
    value: 'PendingEntryLog',
    name: '待处理进件',
    html_templet: '/paymentManagement/pendingEntryLog',
  },
  { value: 'SaasBTemplate', name: 'B端界面模板配置', html_templet: '/saasBTempList' },
  {
    value: 'PayMerchantList',
    name: '支付商户列表',
    html_templet: '/paymentManagement/payMerchantList',
  },
  {
    value: 'MerchantPayAbility',
    name: '单商户支付能力查询',
    html_templet: '/paymentManagement/merchantPayAbility',
  },
  {
    value: 'EntryHandlerLog',
    name: '进件处理记录',
    html_templet: '/paymentManagement/entryHandlerLog',
  },
  {
    value: 'TerminalBackCategoryPush',
    name: 'B端后台品类消息推送',
    html_templet: '/merchantMessage',
  },
  {
    value: 'CloudCodeExchangeList',
    name: '用户兑换列表',
    html_templet: '/cloudCode/userExchangeList',
  },
  { value: 'DivideWayConfig', name: '应付款结算配置', html_templet: '/clearDeploy' },
  { value: 'CdkeyManage', name: '金主卡密管理', html_templet: '/advertisement/cdkeyManage' },
  {
    value: 'UserHeadImgDecorate',
    name: '虚拟头像产品',
    html_templet: '/advertisement/integralAvatarOrder',
  },
  {
    value: 'IncreaseIncomeProduct',
    name: '增值产品归集展示',
    html_templet: '/increaseIncomeProduct',
  },
  { value: 'MaterialVerify', name: '媒体素材审核', html_templet: '/advertisement/materialVerify' },
  { value: 'OperationSwitch', name: '热开关管理', html_templet: '/1075082707580682240' },
  { value: 'GroupBuying', name: '拼团购币', html_templet: '/groupBuying' },
  {
    value: 'SensitiveWordManage',
    name: '简称敏感词管理',
    html_templet: '/paymentManagement/sensitiveWordManage',
  },
  {
    value: 'PromotionGuestRateSet',
    name: '升单系列抽佣比例配置',
    html_templet: '/customerAddValue/newGuestRateSet',
  },
  {
    value: 'AssembleFansMp',
    name: '公众号管理',
    html_templet: '/assembleFans/officialAccountManage',
  },
  {
    value: 'saasOnlineListManage',
    name: 'saas上线名单',
    html_templet: '/chargeInsurance/saasOnlineListManage',
  },
  {
    value: 'nameListManage',
    name: '上线名单管理-1.0',
    html_templet: '/chargeInsurance/onlineListManage',
  },
  {
    value: 'PromotionGuestCustom',
    name: '升单宝自定义配置',
    html_templet: '/customerAddValue/Customization',
  },
  { value: 'upgradeOrderList', name: '订单列表', html_templet: '/chargeUpgrade/orderList' },
  {
    value: 'UpgradeGuestStatistics',
    name: '升单系列统计查询',
    html_templet: '/customerAddValue/upgradeDailyStatistics',
  },
  {
    value: 'MchBalanceNotifyLog',
    name: '商户提现提醒记录',
    html_templet: '/merchantWithdrawRemindRecord',
  },
  {
    value: 'subsidyDetail',
    name: '商户每日补贴详情查询',
    html_templet: '/riskManagement/subsidyDetail',
  },
  { value: 'DouYinActivity', name: '抖音功能', html_templet: '/tiktok' },
  { value: 'machineOrientation', name: '机台定向券', html_templet: '/directionalCoupon' },
  { value: 'PurchaseDeductRandom', name: '支付立减', html_templet: '/faceBrush/payMinus' },
  { value: 'factoryTrafficCardIncome', name: '工厂流量卡收入', html_templet: '/trafficIncome' },
  { value: 'w_vendingOrderManage', name: '新零售订单管理', html_templet: '/vending/orderManage' },
  {
    value: 'officialAccounts',
    name: '生成公众号带参二维码',
    html_templet: '/officialAccountQrcode',
  },
  { value: 'RoleManage', name: '角色管理', html_templet: '' },
  {
    value: 'CostAccountCheck',
    name: '应付对账',
    html_templet: '/DealWithCheckIndex/DealWithCheckMonthlyList',
  },
  {
    value: 'AdLocationConfig',
    name: '产品展示配置',
    html_templet: '/advertisement/AdLocationConfig',
  },
  { value: 'SaasServicePackage', name: '服务套餐配置', html_templet: '/serviceConfig/list' },
  { value: 'ServiceConfigWash', name: '服务套餐配置洗水类', html_templet: '/serviceConfigWash/list' },
  {
    value: 'riskManageAndStrategy',
    name: '风控项目以及风控策略配置',
    html_templet: '/riskManagement/riskStrategy',
  },
  { value: 'riskSubsidy', name: '项目补贴金额管控', html_templet: '/riskManagement/riskSubsidy' },
  { value: 'AgentOperateLog', name: '代理商操作日志', html_templet: '/operationLog' },
  {
    value: 'adjustBalanceRecord',
    name: '商家调整余币余额记录',
    html_templet: '/merchantAdjustmentCoins',
  },
  { value: 'RiskManagement', name: '商户黑名单', html_templet: '/blackList' },
  { value: 'PaymentRecordManage', name: '实收对账', html_templet: '/riskManagement/realIncome' },
  { value: 'SystemManage', name: '系统管理', html_templet: '' },
  { value: 'loanDeduction', name: '还款抵扣', html_templet: '/lakalaAdmin' },
  { value: 'wxDiscountCard', name: '微信先享卡', html_templet: '/wechatEnjoyFirstCard' },
  {
    value: 'IntegratedServiceSign',
    name: '签约商户配置',
    html_templet: '/customerAddValue/signMerchantConfig',
  },
  {
    value: 'w_vendingEquipmentManage',
    name: '新零售设备管理',
    html_templet: '/vending/equipmentManage',
  },
  { value: 'w_abnormalOrderList', name: '新零售异常订单审核', html_templet: '/abnormalOrderAudit' },
  {
    value: 'w_payscoreIdentityFeeManage',
    name: '新零售识别费管理',
    html_templet: '/identityFeeManage',
  },
  { value: 'CouponCensus', name: '优惠券活动统计', html_templet: '/coupon/list' },
  {
    value: 'modifyBillExpTime',
    name: '设备账单过期时间设置',
    html_templet: '/commercialize/modifyBillExpTime',
  },
  {
    value: 'SwiftpassBaseBankInfo',
    name: '银行基础信息管理',
    html_templet: '/1078734179555401728',
  },
  {
    value: 'MerchantEntryAutoRejectConfig',
    name: '自动化审核配置',
    html_templet: '/1079825770783744000',
  },
  {
    value: 'MerchantPayChannelConfig',
    name: '商户渠道配置',
    html_templet: '/paymentManagement/merchantChannelConfig',
  },
  {
    value: 'DealRecordSearchNew',
    name: '交易记录查询（新）',
    html_templet: '/1080157379302875136',
  },
  { value: 'Advert_coin_exchange', name: '广告币兑换码', html_templet: '/1083351402008424448' },
  { value: 'TradeDetailList', name: '交易明细', html_templet: '/1077250027810504704' },
  { value: 'cc-m_family', name: '汽充管理-家充', html_templet: '' },
  { value: 'DivideApplyRecordList', name: '分账单查询', html_templet: '/1077250072853135360' },
  { value: 'Coupon_manager', name: '优惠券管理', html_templet: '/1080484881507618816' },
  { value: 'Cpc_task', name: 'CPC广告管理', html_templet: '/1078310375227768832' },
  {
    value: 'w_vendingDistributorManage',
    name: '新零售商户管理',
    html_templet: '/vending/distributorManage',
  },
  { value: 'Cpc_position', name: 'CPC广告位管理', html_templet: '/1078377251042148352' },
  { value: 'cc-b_business', name: '汽充管理-运营端', html_templet: '' },
  {
    value: 'greenSourceList',
    name: '绿源广告',
    html_templet: '/chargeAdvertisement/lvyuanAdvertisement',
  },
  { value: 'PromotionCommission_List', name: '名单配置', html_templet: '/1082308676151291904' },
  { value: 'DataDashboard', name: '数据看板', html_templet: '/1085990410227163136' },
  { value: 'PromotionCommission_Manager', name: '佣金管理', html_templet: '/1082264764560846848' },
  {
    value: 'AdvertTaskDeliverStatistics',
    name: '广告任务分发统计',
    html_templet: '/1085990917431762944',
  },
  { value: 'cc-b_hlht', name: '汽充管理-互联互通', html_templet: '' },
  { value: 'UserActivationLog', name: 'C端用户注销查询', html_templet: '/userLogout' },
  { value: 'AdvertIncomeStatistics', name: '广告收益统计', html_templet: '/1085990986939768832' },
  { value: 'FansStatistics', name: '粉丝统计', html_templet: '/1085991033504931840' },
  { value: 'Task_Manager', name: '任务管理', html_templet: '/1087386237680099328' },
  {
    value: 'equipment-status-list',
    name: '设备服务状态查询',
    html_templet: '/1062408339274801152',
  },
  {
    value: 'Advert_Official_Account_Manager',
    name: '广告公众号管理',
    html_templet: '/1087386378977812480',
  },
  {
    value: 'RouteChangeGlobal',
    name: '商户支付渠道全局配置',
    html_templet: '/1080182955212800000',
  },
  { value: 'RouteChangeMchNo', name: '商户支付渠道切换', html_templet: '/1081243683917754368' },
  {
    value: 'ThirdPaymentStatistics',
    name: '商户实际到账情况查询',
    html_templet: '/1085966790272622592',
  },
  { value: 'Deliver_coupon_manager', name: '派发券活动', html_templet: '/1085525709596794880' },
  {
    value: 'flowMiniNameList',
    name: '流量小程序',
    html_templet: '/chargeAdvertisement/flowMiniprogram',
  },
  { value: 'server-order-list', name: '设备服务账单查询', html_templet: '/1062675903306051584' },
  {
    value: 'Pre_Official_Account_Task',
    name: '前置关注公众号任务',
    html_templet: '/1085979584418160640',
  },
  { value: 'receipt-order-list', name: '设备收款账单查询', html_templet: '/1062746837459185664' },
  {
    value: 'w_vendingOrderListManage',
    name: '复购易订单查询',
    html_templet: '/vending/repurchaseEasyOrder',
  },
  {
    value: 'Pre_Official_Account_List',
    name: '前置关注公众号列表',
    html_templet: '/1085979753784156160',
  },
  {
    value: 'Pre_Official_Account_Config',
    name: '前置号页面配置',
    html_templet: '/1085979677754007552',
  },
  {
    value: 'merchant-payment-status-list',
    name: '商家缴费情况查询',
    html_templet: '/1074739354183860224',
  },
  {
    value: 'w_vendingDistributorListManage',
    name: '复购易商家名单配置',
    html_templet: '/vending/repurchaseEasyMerchantSearch',
  },
  { value: 'MerchantChannelRelevance', name: '商户号关联', html_templet: '/1083472984534036480' },
  { value: 'entry_config', name: '功能入口配置', html_templet: '/entryConfig' },
  { value: 'MeituanActivity', name: '美团类型配置', html_templet: '/MTActivity' },
  { value: 'meituanCoupon', name: '美团合作券', html_templet: '/MTCommentCoupon' },
  { value: 'rule-manager', name: '收费规则管理', html_templet: '/1075498245051691008' },
  {
    value: 'electricVehicleMerchant',
    name: '运营商管理',
    html_templet: '/electricVehicle/merchantManage',
  },
  {
    value: 'electricVehicleFactory',
    name: '车辆厂家管理',
    html_templet: '/electricVehicle/factoryManage',
  },
  { value: 'cc-m_platformOrder', name: '汽充管理-平台订单', html_templet: '' },
  {
    value: 'electricVehicleEquipmentCode',
    name: '中控码管理',
    html_templet: '/electricVehicle/equipmentCodeManage',
  },
  { value: 'electricVehicleCode', name: '车辆码管理', html_templet: '/electricVehicle/codeManage' },
  { value: 'InforDirectory', name: '文章栏目', html_templet: '/infoMatchPlatform/directory' },
  { value: 'InforTopic', name: '专题类型', html_templet: '/infoMatchPlatform/topic' },
  { value: 'InforArticle', name: '文章记录', html_templet: '/infoMatchPlatform/article' },
  { value: 'SaasWhiteList', name: '商家白名单', html_templet: '/whiteList' },
  {
    value: 'merchantStoreConfigV1',
    name: '按摩引流活动小程序商家店铺设置',
    html_templet: '/1089152031791788032',
  },
  {
    value: 'DivideManagement',
    name: '分成方管理',
    html_templet: '/paymentManagement/divideManagement',
  },
  {
    value: 'PayChannelAreaMapping',
    name: '地区映射表',
    html_templet: '/paymentManagement/payChannelAreaMapping',
  },
  {
    value: 'MchLogConfig',
    name: '商户号属性配置',
    html_templet: '/paymentManagement/mchLogConfig',
  },
  {
    value: 'SwiftpassBankInfo',
    name: '银行信息管理',
    html_templet: '/paymentManagement/swiftpassBankInfo',
  },
  {
    value: 'forcedOpeningList',
    name: '强开上线名单',
    html_templet: '/chargeInsurance/forcedOpeningList',
  },
  {
    value: 'ApplyAutoDivideMerchant',
    name: '申请开通自动分账商家',
    html_templet: '/1085501033223958528',
  },
  { value: 'ProductMatrix', name: '导购商家配置', html_templet: '/1088063588705087488' },
  { value: 'GrantCoins', name: '派币派余额查询', html_templet: '/pageRecords' },
  { value: 'editEquipmentProtocol', name: '修改设备型号', html_templet: '/deviceList' },
  { value: 'SaasPartnerPayment', name: '商户支付渠道配置', html_templet: '' },
  { value: 'redeemCodeStatistics', name: '第三方券码核销统计', html_templet: '/appliedStatistics' },
  {
    value: 'DrainageActivityConfigV1',
    name: '跨品类引流活动配置',
    html_templet: '/1089135925542793216',
  },
  {
    value: 'EquipmentOnlineDataReport2',
    name: '设备在线报表',
    html_templet: '/1107681194104926208',
  },
  /*
    EquipmentOnlineDataReport3，使用低码平台重做此页面
    对应低码平台页面：数据报表 -> 设备在线报表-新
  */
  {
    value: 'EquipmentOnlineDataReport3',
    name: '设备在线报表',
    html_templet: '/1309222114022649856',
  },
  { value: 'free-equipment-manager', name: '免费期设备管理', html_templet: '/1095648111094009856' },
  {
    value: 'ProductMatrixSubsidyConfig',
    name: '导购补贴名单',
    html_templet: '/1105820791469060096',
  },
  {
    value: 'ProductMatrixSubsidyDetail',
    name: '导购补贴明细',
    html_templet: '/1105820844875132928',
  },
  {
    value: 'SwiftpassAreaBaseInfo',
    name: '地区基础信息管理',
    html_templet: '/1106232443930750976',
  },
  {
    value: 'platform_biz_charge_rule',
    name: '业务服务费规则',
    html_templet: '/1098972575324696576',
  },
  {
    value: 'PendingEntryLogNew',
    name: '待处理进件',
    html_templet: '/paymentManagement/pendingEntryLog',
  },
  {
    value: 'PayMerchantListNew',
    name: '支付商户列表',
    html_templet: '/paymentManagement/payMerchantList',
  },
  {
    value: 'EntryHandlerLogNew',
    name: '进件处理记录',
    html_templet: '/paymentManagement/entryHandlerLog',
  },
  {
    value: 'MerchantPayAbilityNew',
    name: '单商户支付能力查询',
    html_templet: '/paymentManagement/merchantPayAbility',
  },
  {
    value: 'MchLogConfigNew',
    name: '商户号属性配置',
    html_templet: '/paymentManagement/mchLogConfig',
  },
  {
    value: 'MerchantAuthNew',
    name: '商户实名列表',
    html_templet: '/paymentManagement/merchantAuth',
  },
  {
    value: 'synthesizeImportManagementNew',
    name: '综合进件管理',
    html_templet: '/importManagement',
  },
  {
    value: 'MerchantEntryAutoRejectConfigNew',
    name: '自动化审核配置',
    html_templet: '/1079825770783744000',
  },
  {
    value: 'MerchantChannelRelevanceNew',
    name: '商户号关联',
    html_templet: '/1083472984534036480',
  },
  {
    value: 'ThirdPaymentStatisticsNew',
    name: '商户实际到账情况查询',
    html_templet: '/1085966790272622592',
  },
  { value: 'DivideOrderBillNew', name: '商家分账对账单', html_templet: '/statementOfAccount' },
  {
    value: 'DivideManagementNew',
    name: '分成方管理',
    html_templet: '/paymentManagement/divideManagement',
  },
  { value: 'MerchantDivideInstallNew', name: '商户分账设置', html_templet: '/statementSet' },
  {
    value: 'ApplyAutoDivideMerchantNew',
    name: '申请开通自动分账商家',
    html_templet: '/1085501033223958528',
  },
  {
    value: 'RouteChangeGlobalNew',
    name: '商户支付渠道全局配置',
    html_templet: '/1080182955212800000',
  },
  {
    value: 'MerchantPayChannelConfigNew',
    name: '商户渠道配置',
    html_templet: '/paymentManagement/merchantChannelConfig',
  },
  { value: 'channelRouteManageNew', name: '渠道养号配置', html_templet: '/channelSwitchInfo' },
  { value: 'RouteChangeMchNoNew', name: '商户支付渠道切换', html_templet: '/1081243683917754368' },
  {
    value: 'SwiftpassBaseBankInfoNew',
    name: '银行基础信息管理',
    html_templet: '/1078734179555401728',
  },
  {
    value: 'SensitiveWordManageNew',
    name: '简称敏感词管理',
    html_templet: '/paymentManagement/sensitiveWordManage',
  },
  {
    value: 'SwiftpassAreaBaseInfoNew',
    name: '地区基础信息管理',
    html_templet: '/1106232443930750976',
  },
  { value: 'MessageList', name: '消息列表', html_templet: '/1113513748804702208' },
  {
    value: 'ExperienceMerchantActivateNew',
    name: '体验期开通',
    html_templet: '/paymentManagement/experiencePeriod',
  },
  { value: 'payscoreAdOrgRouteNew', name: '支付分商家路由', html_templet: '/payscoreAdOrgRoute' },
  {
    value: 'payscoreEquipmentTypeSettingNew',
    name: '支付分设备配置',
    html_templet: '/equipmentTypeSetting',
  },
  {
    value: 'payscoreBindRecordListNew',
    name: '支付分绑定记录',
    html_templet: '/paymentCodeBindingRecord',
  },
  {
    value: 'equipment_configuration_list',
    name: '设备配置列表',
    html_templet: '/1103710870522900480',
  },
  { value: 'equipment_type_list', name: '设备类型列表', html_templet: '/1103708053292789760' },
  { value: 'openProtocolConfig', name: '协议配置列表', html_templet: '/1109162379528187904' },
  {
    value: 'ProductMatrixApply',
    name: '导购试用报名',
    html_templet: '/1122474947044614144',
  },
  {
    value: 'ThirdEquipmentMigrationInit',
    name: '第三方运营商数据迁入',
    html_templet: '/1227563715072757760',
  },
  {
    value: 'smallVenueMemberDetail',
    name: '会员信息',
    html_templet: '/smallVenueMemberDetail',
  },
  {
    value: 'smallVenuePrepaidDeposit',
    name: '渠道核销预存款',
    html_templet: '/smallVenuePrepaidDeposit',
  },
  {
    value: 'smallVenueBrandManagement',
    name: '品牌商管理',
    html_templet: '/smallVenueBrandManagement',
  },
  {
    value: 'smallVenueBrandStoreManagement',
    name: '品牌商门店管理',
    html_templet: '/smallVenueBrandStoreManagement',
  },
  {
    value: 'PopupManage',
    name: '弹窗管理',
    html_templet: '/PopupManage'
  },
  {
    value: 'BannerManage',
    name: 'banner管理',
    html_templet: '/BannerManage'
  },
  {
    value: 'cdz_remote_start',
    name: '远程启动',
    html_templet: '/chargeCSOperate/remoteStart'
  },
  {
    value: 'AiAgentManage',
    name: '智能体应用管理',
    html_templet: '/aiAgent'
  }
];
