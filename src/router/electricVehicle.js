/**
 *两轮车路由
 **/
export const electricVehicle = [{
  path: "/electricVehicle",
  name: "electricVehicle",
  component: resolve => {
    require(["@/views/electricVehicle/index.vue"], resolve)
  },
  redirect: "/electricVehicle/merchantManage",
  children: [
    // 车辆编码注册管理
    {
      path: "/electricVehicle/codeManage",
      name: "vehicleCode",
      component: () => import("@/views/electricVehicle/codeManage.vue")
    },
    // 车辆厂家管理
    {
      path: "/electricVehicle/factoryManage",
      name: "vehicleFactory",
      component: () => import("@/views/electricVehicle/factoryManage.vue")
    },
    // 中控码注册管理
    {
      path: "/electricVehicle/equipmentCodeManage",
      name: "centralControlCode",
      component: () => import("@/views/electricVehicle/equipmentCodeManage.vue")
    },
    // 运营商管理
    {
      path: "/electricVehicle/merchantManage",
      name: "electricVehicleMerchant",
      component: () => import("@/views/electricVehicle/merchantManage.vue")
    },
  ]
}]
