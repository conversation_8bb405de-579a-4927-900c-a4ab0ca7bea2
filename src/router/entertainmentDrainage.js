/**
 * @description: 娱乐引流
 * @author: lian<PERSON><PERSON>
 * @date: 2021-06-03 09:42:12
 */
// const PcRender = () => import("@/components/PcRender/index")
const entertainmentDrainage = [
  /**
   * @description: 活动模板配置
   * @author: lian<PERSON><PERSON>
   * @date: 2021-06-03 09:54:30
   */
  {
    path: '/entertainmentDrainage/templateConfig',
    name: 'entertainmentDrainageTemplateConfig',
    component: resolve => { require(['@/views/entertainmentDrainage/templateConfig/index'], resolve) }
  },
  /**
   * @description: 商户品牌配置
   * @author: lianghua
   * @date: 2021-07-03 09:25:40
   */
  {
    path: '/entertainmentDrainage/shopBrandConfig',
    name: 'entertainmentDrainageShopBrandConfig',
    component: resolve => { require(['@/views/entertainmentDrainage/shopBrandConfig/index'], resolve) }
  },
  /**
   * @description: 商家引流功能开关设置
   * @author: liangh<PERSON>
   * @date: 2021-07-09 11:49:27
   */
  {
    path: '/entertainmentDrainage/entertainmentShopSwitch',
    name: 'entertainmentDrainageShopSwitch',
    component: resolve => { require(['@/views/entertainmentDrainage/entertainmentShopSwitch/index'], resolve) }
  },
  /**
   * @description: 生活设备导流开关设置
   * @author: lianghua
   * @date: 2021-07-09 11:50:36
   */
  {
    path: '/entertainmentDrainage/lifeShopSwitch',
    name: 'entertainmentDrainageLifeShopSwitch',
    component: resolve => { require(['@/views/entertainmentDrainage/lifeShopSwitch/index'], resolve) }
  },
  /**
   * @description: 引流礼品兑换
   * @author: lianghua
   * @date: 2021-8-28
   */
  {
    path: '/entertainmentDrainage/drainageGift',
    name: 'entertainmentDrainageDrainageGift',
    component: resolve => { require(['@/views/entertainmentDrainage/DrainageGift/index'], resolve) }
  },
  // // 按摩引流活动配置
  // {
  //   path: '/1089135925542793216',
  //   name: '1089135925542793216',
  //   component: PcRender,
  // },
  // // 按摩引流活动小程序商家店铺设置
  // {
  //   path: '/1089152031791788032',
  //   name: '1089152031791788032',
  //   component: PcRender,
  // },
  // // 按摩引流活动参与记录
  // {
  //   path: '/1089148346906980352',
  //   name: '1089148346906980352',
  //   component: PcRender,
  // },
]
export default entertainmentDrainage