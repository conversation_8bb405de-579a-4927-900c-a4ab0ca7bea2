/** 
 * 多平台router列表，B端跟C端
 */
export default [

  // B端列表
  {
    path: "multiplatform/merchantList",
    name: "merchantList",
    component: resolve => {
      // require(["@/views/multiplatform/merchantList/merchantList"], resolve)
      require(["@/views/multiplatform/template/saasBTempList"], resolve)
    }
  },
  // B端模版
  {
    path: "multiplatform/merchantTemplate",
    name: "merchantTemplate",
    component: resolve => {
      // require(["@/views/multiplatform/merchantTemplate/merchantTemplate"], resolve)
      require(["@/views/multiplatform/template/saasBEditTemp"], resolve)
    }
  },
  // C端列表
  {
    path: "multiplatform/customerList",
    name: "customerList",
    component: resolve => {
      // require(["@/views/multiplatform/customerList/customerList"], resolve)
      require(["@/views/multiplatform/template/saasTempList"], resolve)
    }
  },
  // C端模板
  {
    path: "multiplatform/customerTemplate",
    name: "customerTemplate",
    component: resolve => {
      // require(["@/views/multiplatform/customerTemplate/customerTemplate"], resolve)
      require(["@/views/multiplatform/template/saasEditTemp"], resolve)
    }
  },

]