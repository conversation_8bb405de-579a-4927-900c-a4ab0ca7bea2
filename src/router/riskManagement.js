// 路由推荐使用ES的import组件懒加载方法

const RiskStrategy = () => import("@/views/riskManagement/riskStrategy");//风控项目以及风控策略配置
const RiskSubsidy = () => import("@/views/riskManagement/riskSubsidy");//项目补贴金额管控
const SubsidyDetail = () => import("@/views/riskManagement/subsidyDetail");//商户每日补贴详情查询
const DetailList = () => import("@/views/riskManagement/detailList");//详情列表
const BalanceChangeRecord = () => import("@/views/riskManagement/balanceChangeRecord");//财务-风控-余额变更记录查询
const BalanceCheck = () => import("@/views/riskManagement/balanceCheck");//财务-风控-余额对账
const RealIncome = () => import("@/views/riskManagement/realIncome");//财务-风控-实收金额对账
const RealIncomeDetail = () => import("@/views/riskManagement/realIncomeDetail");//财务-风控-实收对账查看/实收金额对账
const TaxExport = () => import("@/views/riskManagement/taxExport");//财务-报税数据导出
const PayChannelLog = () => import("@/views/riskManagement/payChannelLog");//财务-系统操作记录
export const riskManagement = [
    {
      path: "/riskManagement/riskStrategy",
      name: "riskStrategy",
      // component: resolve => {require(["@/views/riskManagement/riskStrategy"], resolve)}
      component: RiskStrategy,
    },
    {
      path: "/riskManagement/riskSubsidy",
      name: "riskSubsidy",
      component: RiskSubsidy,
      meta: {
        keepAlive: true
      }
    },
    {
      path: "/riskManagement/subsidyDetail",
      name: "subsidyDetail",
      component: SubsidyDetail
    },
    {
      path: "/riskManagement/detailList",
      name: "detailList",
      component: DetailList
    },
    {
      path: "/riskManagement/balanceChangeRecord",
      name: "balanceChangeRecord",
      component: BalanceChangeRecord
    },
    {
      path: "/riskManagement/balanceCheck",
      name: "balanceCheck",
      component: BalanceCheck
    },
    {
      path: "/riskManagement/realIncome",
      name: "realIncome",
      component: RealIncome
    },
    {
      path: "/riskManagement/realIncomeDetail",
      name: "realIncomeDetail",
      component: RealIncomeDetail
    },
    {
      path: "/riskManagement/taxExport",
      name: "taxExport",
      component: TaxExport
    },
    {
      path: "/riskManagement/payChannelLog",
      name: "payChannelLog",
      component: PayChannelLog
    },


];
