/**
 * @Description: 信息撮合平台
 * @Author: wutao
 * @Date: 2022/5/19 17:34
 * @LastEditors: wutao
 * @LastEditTime: 2022/5/19 17:34
 */

const infoMatchPlatformRouter =  [
  {
    path: "/infoMatchPlatform",
    name: "infoMatchPlatform",
    component: () => import(/* webpackChunkName: "InfoMatchPlatformIndex" */ "@/views/infoMatchPlatform/index.vue"),
    redirect: "/infoMatchPlatform/directory",
    children:[
      { // 文章栏目
        path: "directory",
        name: "directory",
        component: () => import(/* webpackChunkName: "infoMatchPlatformDirectory" */ "@/views/infoMatchPlatform/directory/index.vue")
      },
      { // 专题类型
        path: "topic",
        name: "topic",
        component: () => import(/* webpackChunkName: "infoMatchPlatformTopic" */ "@/views/infoMatchPlatform/topic/index.vue")
      },
      { // 撰写文章
        path: "article",
        name: "article",
        component: () => import(/* webpackChunkName: "infoMatchPlatformArticle" */ "@/views/infoMatchPlatform/article/index.vue")
      },
      { // 创建编辑文章
        path: "createEditArticle",
        name: "createEditArticle",
        component: () => import(/* webpackChunkName: "infoMatchPlatformArticle" */ "@/views/infoMatchPlatform/article/createEdit.vue")
      },
    ]
  }
]

export default infoMatchPlatformRouter
