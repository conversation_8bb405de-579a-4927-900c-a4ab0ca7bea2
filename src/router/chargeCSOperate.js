const remoteStart = [
  {
    path: 'remoteStart',
    name: 'ChargeCSOperate-RemoteStart',
    meta: {
      keepAlive: true
    },
    component: () =>
      import(
        /* webpackChunkName: "chargeCSOperate-remoteStart" */ "@views/chargeCSOperate/remoteStart/remoteStart.vue"
      )
  }
]


export const chargeCSRouter = [
  {
    // 充电桩客服操作
    path: "/chargeCSOperate",
    name: "chargeCSOperate",
    component: () => 
      import(/* webpackChunkName: "chargeCSOperate-index" */ "@views/chargeCSOperate/index.vue"),
    children: [
      ...remoteStart, // 远程启动
    ]
  },
]