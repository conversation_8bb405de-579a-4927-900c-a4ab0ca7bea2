// const PcRender = () => import("@/components/PcRender/index")
const distributor = [
  // 支付商户列表
  {
    // 支付商户列表
    path: "payMerchantList",
    name: "paymentManagement-payMerchantList",
    meta: {
      keepAlive: true
    },
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-payMerchantList" */ "@views/payment/distributorList/distributorList.vue"
      )
  },
  {
    // 渠道申请单详情
    path: "distributorApplyChannel",
    name: "paymentManagement-distributorApplyChannel",
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-distributorApplyChannel" */ "@views/payment/distributorList/distributorApplyChannel.vue"
      )
  },
  {
    // 资料详情
    path: "distributorDetail",
    name: "paymentManagement-distributorDetail",
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-distributorDetail" */ "@views/payment/distributorList/distributorDetail.vue"
      )
  },
  {
    // 进件快照
    path: "distributorSnapshot",
    name: "paymentManagement-distributorSnapshot",
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-distributorSnapshot" */ "@views/payment/distributorList/distributor-snapshot.vue"
      )
  }
];
const handle = [
  // 待处理进件
  {
    // 待处理进件
    path: "pendingEntryLog",
    name: "paymentManagement-pendingEntryLog",
    meta: {
      keepAlive: true
    },
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-pendingEntryLog" */ "@views/payment/applicationData/applicationData.vue"
      )
  },
  {
    // 客服待办
    path: "applicationDataWaitHandle",
    name: "paymentManagement-applicationDataWaitHandle",
    meta: {
      keepAlive: true
    },
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-applicationDataWaitHandle" */ "@views/payment/applicationData/applicationDataWaitHandle.vue"
      )
  },
  {
    // 客服待办详情
    path: "applicationDataDistributorDetail",
    name: "paymentManagement-applicationDataDistributorDetail",
    meta: {
      keepAlive: false
    },
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-applicationDataDistributorDetail" */ "@views/payment/applicationData/applicationDataDistributorDetail.vue"
      )
  }
];
const entryHandlerLog = [
  // 进件变更记录
  {
    // 进件变更记录
    path: "entryHandlerLog",
    name: "paymentManagement-entryHandlerLog",
    meta: {
      keepAlive: false
    },
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-entryHandlerLog" */ "@views/payment/applicationRecord/applicationRecord.vue"
      )
  },
  {
    // 变更申请明细
    path: "applicationRecordChangeRecordDetail",
    name: "paymentManagement-applicationRecordChangeRecordDetail",
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-applicationRecordChangeRecordDetail" */ "@views/payment/applicationRecord/applicationRecordChangeRecordDetail.vue"
      )
  }
];
const merchantPayAbility = [
  // 单商户支付能力查询
  {
    // 单商户支付能力查询
    path: "merchantPayAbility",
    name: "paymentManagement-merchantPayAbility",
    meta: {
      keepAlive: true
    },
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-merchantPayAbility" */ "@views/payment/distributorPaymentData/distributorPaymentData.vue"
      )
  },
  {
    // 单商户支付能力查询
    path: "distributorPaymentDataReject",
    name: "paymentManagement-distributorPaymentDataReject",
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-distributorPaymentDataReject" */ "@views/payment/distributorPaymentData/distributorPaymentDataReject.vue"
      )
  }
];
const MerchantIdAttributeConfiguration = [
  // 商户号属性配置页面
  {
    // 商户号属性配置页面
    path: "mchLogConfig",
    name: "paymentManagement-merchant-id-attribute-configuration",
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-merchant-id-attribute-configuration" */ "@views/payment/merchant-id-attribute-configuration/merchant-id-attribute-configuration.vue"
      )
  }
];
const BankInformationManagement = [
  // 银行信息管理页面
  {
    // 银行信息管理页面
    path: "swiftpassBankInfo",
    name: "paymentManagement-bank-information-management",
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-bank-information-management" */ "@views/payment/bank-information-management/bank-information-management.vue"
      )
  }
];
const RegionMappingTable = [
  // 地区映射表页面
  {
    // 地区映射表页面
    path: "payChannelAreaMapping",
    name: "paymentManagement-region-mapping-table",
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-region-mapping-table" */ "@views/payment/region-mapping-table/region-mapping-table.vue"
      )
  }
];
const WechantRealName = [
  // 微信实名页面
  {
    // 微信实名页面
    path: "merchantAuth",
    name: "paymentManagement-wechant-real-name",
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-wechant-real-name" */ "@views/payment/wechant-real-name/wechant-real-name.vue"
      )
  }
];
const SplitPartyManagement = [
  // 分成方管理页面
  {
    // 分成方管理页面
    path: "divideManagement",
    name: "paymentManagement-split-party-management",
    component: () =>
      import(
        /* webpackChunkName: "paymentManagement-split-party-management" */ "@views/payment/split-party-management/split-party-management.vue"
      )
  }
];
const SensitiveWordManage = [
  // 敏感词管理
  {
    // 分成方管理页面
    path: "sensitiveWordManage",
    name: "sensitiveWordManage",
    component: () =>
      import(
        /* webpackChunkName: "sensitiveWordManage" */ "@views/payment/sensitive-word-manage/index.vue"
      )
  },
  {
    // 分成方管理页面
    path: "sensitiveWordRecord",
    name: "sensitiveWordRecord",
    component: () =>
      import(
        /* webpackChunkName: "sensitiveWordManage" */ "@views/payment/sensitive-word-manage/record.vue"
      )
  }
]
const MerchantChannelConfig = [
// 商户渠道配置
  {
    // 分成方管理页面
    path: "merchantChannelConfig",
    name: "merchantChannelConfig",
    component: () =>
      import(
        /* webpackChunkName: "merchantChannelConfig" */ "@views/payment/merchant-channel-config/index.vue"
      )
  },
]
// // 组件库页面
// const payChanelChange = [
//    // 申请开通自动分账商家
//    {
//     path: '/1085501033223958528',
//     name: '1085501033223958528',
//     component: PcRender,
//   },
//   // 商家实际到账情况查询
//   {
//       path: '/1085966790272622592',
//       name: '1085966790272622592',
//       component: PcRender,
//   },
//    // 商户支付渠道全局配置
//   {
//     path: '/1081243683917754368',
//     name: '1081243683917754368',
//     component: PcRender,
//   },
//   // 商户支付渠道切换
//   {
//     path: '/1080182955212800000',
//     name: '1080182955212800000',
//     component: PcRender,
//   },
//   // 自动化审核配置
// {
//     path: '/1079825770783744000',
//     name: '1079825770783744000',
//     component: PcRender,
//   },
//   // 自动化审核操作记录
//   {
//     path: '/1080121097341669376',
//     name: '1080121097341669376',
//     component: PcRender,
//   },
//   // 银行基础信息管理
//   {
//     path: '/1078734179555401728',
//     name: '1078734179555401728',
//     component: PcRender,
//   },
//   // 地区基础信息管理
//   {
//     path: '/1106232443930750976',
//     name: '1106232443930750976',
//     component: PcRender,
//   },
//   // 交易记录查询
//   {
//     path: '/1080157379302875136',
//     name: '1080157379302875136',
//     component: PcRender,
//   },
// ]
const experiencePeriodConfig = [
  // 支付体验期
  {
    path: "experiencePeriod",
    name: "experiencePeriod",
    component: () => import("@views/payment/experiencePeriod/index.vue")
  }
]
export const paymentRouter = [
  {
    path: "/paymentManagement",
    name: "paymentManagement",
    component: () =>
      import(/* webpackChunkName: "paymentManagement-index" */ "@views/payment/payment.vue"),
    children: [
      // ...payChanelChange,//支付渠道切换页面
      ...distributor, // 支付商户列表
      ...handle, // 待处理进件
      ...entryHandlerLog, // 进件变更记录
      ...merchantPayAbility, // 单商户支付能力查询
      ...MerchantIdAttributeConfiguration, // 商户号属性配置页面
      ...BankInformationManagement, // 银行信息管理页面
      ...RegionMappingTable, // 地区映射表页面
      ...WechantRealName, // 微信实名页面
      ...SplitPartyManagement, // 分成方管理页面
      ...SensitiveWordManage, //敏感词管理
      ...MerchantChannelConfig, //商户渠道配置
      ...experiencePeriodConfig //支付体验期
    ]
  }
];
