/** 
 * sass2.0的router列表  
 */
export default [
  // 商户支付渠道配置
  {
    path: "/SassMerchantPayment",
    name: "SassMerchantPayment",
    component: resolve => {require(["@/views/saas2/SassMerchantPayment/SassMerchantPayment"], resolve)}
  },
  // 编辑模板
  { 
    path: "/SaasEditTemp", 
    name: "SaasEditTemp", 
    component: resolve => { require(["@/views/saas2/template/saasEditTemp"], resolve)}
  },
  // 模板列表
  { 
    path: "/SaasTempList", 
    name: "SaasTempList", 
    component: resolve => {require(["@/views/saas2/template/saasTempList"], resolve)} 
  },
  // 编辑B端模板
  { 
    path: "/SaasBEditTemp", 
    name: "SaasBEditTemp", 
    component: resolve => {require(["@/views/saas2/template/saasBEditTemp"], resolve)} 
  },
  // B端模板列表
  { 
    path: "/SaasBTempList", 
    name: "SaasBTempList", 
    component: resolve => {require(["@/views/saas2/template/saasBTempList"], resolve)} 
  },
  // 模板预览
  { 
    path: "/SaasPreview", 
    name: "SaasPreview", 
    component: resolve => {require(["@/views/saas2/template/saasPreview"], resolve)} 
  },
  // 设备类型列表
  // { 
  //   path: "/SaasDeviceTypeList", 
  //   name: "SaasDeviceTypeList", 
  //   component: resolve => {require(["@/views/saas2/deviceType/SaasDeviceTypeList"], resolve)} 
  // },
  // 设备注册规则
  {
    path: "/SaasRegRuleIndex",
    name: "SaasRegRuleIndex",
    component: resolve => {require(["@/views/saas2/deviceReg/SaasRegRuleIndex"], resolve)},
    redirect: "/SaasRegRuleIndex/SaasRegRuleList",
    children: [
      {
        path: "SaasRegRuleList",
        name: "SaasRegRuleList",
        component: resolve => {require(["@/views/saas2/deviceReg/SaasRegRuleList"], resolve)}
      },
      {
        path: "SaasSetRegRule",
        name: "SaasSetRegRule",
        component: resolve => {require(["@/views/saas2/deviceReg/SaasSetRegRule"], resolve)}
      }
    ]
  },
  // 设备统计数据设置
  {
    path: "/SaasStatisticsTemSetIndex",
    name: "SaasStatisticsTemSetIndex",
    component: resolve => {require(["@/views/saas2/statisticsTemSet/SaasStatisticsTemSetIndex"], resolve)},
    redirect: "/SaasStatisticsTemSetIndex/SaasStatisticsTemList",
    children: [
      {
        path: "SaasStatisticsTemList",
        name: "SaasStatisticsTemList",
        component: resolve => {require(["@/views/saas2/statisticsTemSet/SaasStatisticsTemList"], resolve)}
      },
      {
        path: "SaasStatisticsTemSet",
        name: "SaasStatisticsTemSet",
        component: resolve => {require(["@/views/saas2/statisticsTemSet/SaasStatisticsTemSet"], resolve)}
      }
    ]
  },
  // 插件配置
  {
    path: "/SaasPlugInIndex",
    name: "SaasPlugInIndex",
    component: resolve => {require(["@/views/saas2/plugIn/SaasPlugInIndex"], resolve)},
    redirect: "/SaasPlugInIndex/SaasPlueInList",
    children: [
      {
        path: "SaasPlueInList",
        name: "SaasPlueInList",
        component: resolve => {require(["@/views/saas2/plugIn/SaasPlueInList"], resolve)}
      },
      {
        path: "SaasSetPlugIn",
        name: "SaasSetPlugIn",
        component: resolve => {require(["@/views/saas2/plugIn/SaasSetPlugIn"], resolve)}
      }
    ]
  },
  // 设备扩展功能配置
  { 
    path: "/SaasDeviceActionAm", 
    name: "SaasDeviceActionAm", 
    component: resolve => {require(["@/views/saas2/SaasDeviceActionAm/SaasDeviceActionAm"], resolve)} 
  },
  // 2.0上线数据统计
  { 
    path: "/SaasReport", 
    name: "SaasReport", 
    component: resolve => {require(["@/views/saas2/SaasReport/SaasReport"], resolve)} 
  },
  // 2.0工厂出货统计
  { 
    path: "/SaasSalesStatistics", 
    name: "SaasSalesStatistics", 
    component: resolve => {require(["@/views/saas2/SaasReport/SaasSalesStatistics"], resolve)} 
  },
  // 2.0商户白名单
  { 
    path: "/whiteList", 
    name: "whiteList", 
    component: resolve => {require(["@/views/saas2/whiteList/whiteList"], resolve)} 
  }
]