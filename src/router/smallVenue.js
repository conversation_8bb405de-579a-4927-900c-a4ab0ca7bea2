/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-03-26 16:40:23
 * @Description: 多金宝小场地页面路由
 */
export default [
  // 会员信息
  {
    path: 'smallVenueMemberDetail',
    name: 'memberDetail',
    component: (resolve) => {
      require(['@/views/smallVenue/member-detail/index.vue'], resolve);
    },
  },
  // 渠道核销预存款
  {
    path: 'smallVenuePrepaidDeposit',
    name: 'prepaidDeposit',
    component: (resolve) => {
      require(['@/views/smallVenue/prepaid-deposit/index.vue'], resolve);
    },
  },
  // 品牌商管理
  {
    path: 'smallVenueBrandManagement',
    name: 'brandManagement',
    component: (resolve) => {
      require(['@/views/smallVenue/brand-management/index.vue'], resolve);
    },
  },
  // 品牌商门店管理
  {
    path: 'smallVenueBrandStoreManagement',
    name: 'brandStoreManagement',
    component: (resolve) => {
      require(['@/views/smallVenue/brand-store-management/index.vue'], resolve);
    },
  },
];
