/**
*商业化路由
**/
export const commercialize =  [
{
  path: "/commercialize",
  name: "commercialize",
  component: resolve => {require(["@/views/commercialize/index"], resolve)},
  redirect: "/commercialize/deveiceFreeDetail",
  children:[
    {
      path: "/commercialize/deveiceFreeDetail",
      name: "deveiceFreeDetail",
      component: resolve => {
        // require(["@/views/multiplatform/merchantList/merchantList"], resolve)
        require(["@/views/commercialize/deveiceFreeDetail"], resolve)
      }
    },
    {
      path: "/commercialize/theBillChargeSeting",
      name: "theBillChargeSeting",
      component: resolve => {
        // require(["@/views/multiplatform/merchantList/merchantList"], resolve)
        require(["@/views/commercialize/theBillChargeSeting"], resolve)
      }
    },
    {
      path: "/commercialize/theBillChargeSetingForm",
      name: "theBillChargeSetingForm",
      component: resolve => {
        // require(["@/views/multiplatform/merchantList/merchantList"], resolve)
        require(["@/views/commercialize/theBillChargeSetingForm"], resolve)
      }
    },
    {
      path:"/commercialize/modifyBillExpTime",
      name:"modifyBillExpTime",
      component: ()=>import("@/views/commercialize/modifyBillExpTime")
    },
    {
      path:"/commercialize/deveiceOptionLog",
      name:"deveiceOptionLog",
      component: ()=>import("@/views/commercialize/deveiceOptionLog")
    },
    {
      path:"/commercialize/deveiceOptionLogDetail",
      name:"deveiceOptionLogDetail",
      component: ()=>import("@/views/commercialize/deveiceOptionLogDetail")
    },
  ]
}]
