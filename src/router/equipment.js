// const PcRender = () => import("@/components/PcRender/index")

const equipmentInfo = [
  // 设备信息
  {
    path: 'equipmentInfo',
    name: 'equipmentManagement-equipment-info',
    meta: {
      keepAlive: true,
    },
    component: () =>
      import(
        /* webpackChunkName: "equipmentManagement-equipment-info" */ '@views/equipment/equipmentInfo/equipmentInfo.vue'
      ),
  },
  // 设备信息详情
  {
    path: 'equipmentInfoDetail',
    name: 'equipmentManagement-equipment-info-detail',
    component: () =>
      import(
        /* webpackChunkName: "equipmentManagement-equipment-info-detail" */ '@views/equipment/equipmentInfo/equipmentInfoDetail.vue'
      ),
  },
  // // 设备类型管理
  // {
  //   path: '/1103708053292789760',
  //   name: '1103708053292789760',
  //   component: PcRender,
  // },
  // // 设备配置管理
  // {
  //   path: '/1103710870522900480',
  //   name: '1103710870522900480',
  //   component: PcRender,
  // },
  // // 新增/编辑设备配置
  // {
  //   path: '/1103717827237457920',
  //   name: '1103717827237457920',
  //   component: PcRender,
  // },
  // // 协议配置
  // {
  //   path: '/1109162379528187904',
  //   name: '1109162379528187904',
  //   component: PcRender,
  // }
];

export const equipmentRouter = [
  {
    path: '/equipment',
    name: 'equipment',
    component: () =>
      import(/* webpackChunkName: "equipmentManagement" */ '@views/equipment/equipment.vue'),
    children: [
      ...equipmentInfo, // 设备信息
    ],
  },
];
