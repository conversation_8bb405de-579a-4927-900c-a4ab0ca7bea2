/**
 * @description: C端增值业务
 * @author: wuenyou
 * @date: 2021-10-19 11:10:12
 */

const cWcard = () => import(/* webpackChunkName: "cWcard" */ "@/views/customerAddValue/children/cWcard");
const Customization = () => import(/* webpackChunkName: "Customization" */ "@/views/customerAddValue/children/customization");
const CustomerAddValue = () => import(/* webpackChunkName: "CustomerAddValue" */ "@/views/customerAddValue/index");
const dataSearch = () => import(/* webpackChunkName: "dataSearch" */"@/views/customerAddValue/children/dataSearch.vue"); // 签约商户配置统计查询
const SignMerchantIndex = () => import(/* webpackChunkName: "SignMerchantIndex" */"@/views/customerAddValue/children/signMerchantIndex.vue"); // 签约商户配置
const SignMerchantConfig = () => import(/* webpackChunkName: "SignMerchantConfig" */ "@/views/customerAddValue/children/signMerchantConfig.vue"); // 签约商户配置
const NewGuestRateSet = () => import(/* webpackChunkName: "NewGuestRateSet" */ "@/views/customerAddValue/children/upOrderList/newGuestRateSet.vue"); // 升单宝百分比配置
const upOrderSetRateIndex = () => import(/* webpackChunkName: "upOrderSetRateIndex" */ "@/views/customerAddValue/children/upOrderList/upOrderSetRateIndex.vue"); // 升单系列配置抽佣比例首页
const commissionRatioSetting = () => import(/* webpackChunkName: "commissionRatioSetting" */ "@/views/customerAddValue/children/upOrderList/commissionRatioSetting.vue"); // 升单宝百分比配置
const massageLitreRateSet = () => import(/* webpackChunkName: "massageLitreRateSet" */ "@/views/customerAddValue/children/upOrderList/massageLitreRateSet.vue"); // 按摩椅升单宝百分比配置
const massageTimeCardSet = () => import(/* webpackChunkName: "massageTimeCardSet" */ "@/views/customerAddValue/children/upOrderList/massageTimeCardSet.vue"); // 按摩椅升单宝百分比配置
const upgradeDailyStatistics = () => import(/* webpackChunkName: "upgradeDailyStatistics" */ "@/views/customerAddValue/children/upgradeDailyStatistics.vue"); // 升单系列统计查询
// const PcRender = () => import("@/components/PcRender/index")

const customerIncrementValue = [
  /**
   * @description: C端增值业务
   * @author: wuenyou
   * @date: 2021-10-19 11:10:12
   * **/
  {
    path: "/customerAddValue",
    name: "customerAddValue",
    component: CustomerAddValue,
    redirect: "/customerAddValue/cWcard",
    children: [
      /**
       * @description: C端畅玩卡设置
       * @author: wuenyou
       * @date: 2021-10-19 11:10:12
       */
      {
        path: 'cWcard',
        name: 'cWcard',
        component: cWcard
      },
      /**
       * @description: 升单宝自定义设置
       * @author: wuenyou
       * @date: 2021-10-19 11:10:12
       * **/
      {
        path: 'customization',
        name: 'Customization',
        component: Customization,
      },
      /**
       * @description: 签约商户
       * @author: wuenyou
       * @date: 2021-10-19 11:10:12
       * **/
      {
        path: "signMerchantConfig",
        name: "signMerchantConfig",
        component: SignMerchantIndex,
        children: [
          /**
           * @description: 签约商户
           * @author: wuenyou
           * @date: 2021-10-19 11:10:12
           * **/
          {
            path: "/",
            name: "signMerchantConfig",
            component: SignMerchantConfig
          },
          /**
           * @description: 签约商户
           * @author: wuenyou
           * @date: 2021-10-19 11:10:12
           * **/
          {
            path: "dataSearch",
            name: "dataSearch",
            component: dataSearch
          },
        ]
      },
      /**
       * @description: 升单系列
       * @author: wuenyou
       * @date: 2021-10-19 11:10:12
       * **/
      {
        path: "newGuestRateSet",
        name: "newGuestRateSet",
        component: upOrderSetRateIndex,
        children: [
          /**
           * @description: 升单宝设置抽佣比例
           * @author: wuenyou
           * @date: 2021-10-19 11:10:12
           * **/
          {
            path: "/",
            name: "newGuestRateSet",
            component: NewGuestRateSet
          },
          /**
           * @description: 娃娃机兑币机升单设置抽佣比例
           * @author: wuenyou
           * @date: 2021-10-19 11:10:12
           * **/
          {
            path: "commissionRatioSetting",
            name: "commissionRatioSetting",
            component: commissionRatioSetting
          },
          /**
           * @description: 娃娃机兑币机升单设置抽佣比例
           * @author: wuenyou
           * @date: 2021-10-19 11:10:12
           * **/
          {
            path: "massageLitreRateSet",
            name: "massageLitreRateSet",
            component: massageLitreRateSet
          },
          /**
           * @description: 按摩椅次卡设置抽佣比例
           * @author: caizibin
           * @date: 2024-10-29 15:11:12
           * **/
          {
            path: "massageTimeCardSet",
            name: "massageTimeCardSet",
            component: massageTimeCardSet
          },
        ]
      },
      /**
       * @description: 升单系列统计查询
       * @author: wuenyou
       * @date: 2021-11-11 11:10:12
       * **/
      {
        path: "upgradeDailyStatistics",
        name: "upgradeDailyStatistics",
        component: upgradeDailyStatistics
      },
      // /**
      //  * @description: 智能导购
      //  * @author: zhouzijun
      //  * @date: 2021-11-11 11:10:12
      //  * **/
      // {
      //     path: '/1088063588705087488',
      //     name: '1088063588705087488',
      //     component: PcRender,
      // },
      // // 商家默认抽佣配置
      // {
      //     path: '/1088408387249352704',
      //     name: '1088408387249352704',
      //     component: PcRender,
      // },
      // // 活动配置
      // {
      //     path: '/1088408863630012416',
      //     name: '1088408863630012416',
      //     component: PcRender,
      // },
      // // 测试报告明细
      // {
      //     path: '/1092505331874271232',
      //     name: '1092505331874271232',
      //     component: PcRender,
      // },
      // // 导购补贴明细
      // {
      //   path: '/1105820844875132928',
      //   name: '1105820844875132928',
      //   component: PcRender,
      // },
      // // 导购补贴名单
      // {
      //   path: '/1105820791469060096',
      //   name: '1105820791469060096',
      //   component: PcRender,
      // }
    ]
  },
];
export default customerIncrementValue;
