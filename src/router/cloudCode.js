/**
 * @Description: 聚合拉新路由
 * @Author: wutao
 * @Date: 2021/6/28 17:34
 * @LastEditors: wutao
 * @LastEditTime: 2021/6/28 17:34
 */

const cloudCodeRouter =  [
  {
    path: "/cloudCode",
    name: "cloudCode",
    component: resolve => {require(["@/views/cloudCode/index.vue"],resolve)},
    redirect: "/cloudCode/adTask",
    children:[
      {
        path: "adTask",
        name: "adTask",
        component: resolve => { // 能量任务
          require(["@/views/cloudCode/children/adTask.vue"],resolve)
        }
      },
      {
        path: "lotterySetting",
        name: "lotterySetting",
        component: resolve => { // 抽奖设置
          require(["@/views/cloudCode/children/lotterySetting.vue"],resolve)
        }
      },
      {
        path: "exchangeSetting",
        name: "exchangeSetting",
        component: resolve => { // 兑换设置
          require(["@/views/cloudCode/children/exchangeSetting.vue"],resolve)
        }
      },
      {
        path: "userExchangeList",
        name: "userExchangeList",
        component: resolve => { // 用户兑换列表
          require(["@/views/cloudCode/children/userExchangeList.vue"],resolve)
        }
      },
    ]
  }
]

export default cloudCodeRouter
