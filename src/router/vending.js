// 零售异常订单审核
const abnormalOrderAudit = resolve => {require(['@/views/vending/abnormalOrderAudit/index'], resolve)}
// 新零售识别费管理
const identityFeeManage = resolve => {require(['@/views/vending/IdentityFeeManage/index'], resolve)};
const merchantSearch = resolve => {require(['@/views/vending/IdentityFeeManage/merchantSearch.vue'], resolve)}; // 商家查询
const identityOrderSearch = resolve => {require(['@/views/vending/IdentityFeeManage/identityOrderSearch.vue'], resolve)}; // 订单查询
const monthBilling = resolve => {require(['@/views/vending/IdentityFeeManage/monthBilling.vue'], resolve)}; // 月账单
// 新零售设备管理
const equipmentManage = resolve => {require(['@/views/vending/equipmentManage/index'], resolve)};
// 新零售订单管理
const vendingOrder = resolve => {require(['@/views/vending/orderManage/index'], resolve)};
const orderManage = resolve => {require(['@/views/vending/orderManage/list'], resolve)};
// 新零售订单管理订单详情
const vendingOrderDetail = resolve => {require(['@/views/vending/orderManage/detail'], resolve)};
// 零售复购易订单
const repurchaseEasyOrderManage = resolve => {require(['@/views/vending/repurchaseEasyManage/order/index'], resolve)};
// 复购易订单列表
const repurchaseEasyOrderList = resolve => {require(['@/views/vending/repurchaseEasyManage/order/orderList'], resolve)};
// 复购易订单详情
const repurchaseEasyOrderDetail = resolve => {require(['@/views/vending/repurchaseEasyManage/order/orderDetail'], resolve)};
// 复购易商户查询
const repurchaseEasyMerchantSearch = resolve => {require(['@/views/vending/repurchaseEasyManage/merchantManage/index'], resolve)};
export const VendingRouter = [
  { path: '/abnormalOrderAudit', name: 'AbnormalOrderAudit', component: abnormalOrderAudit }, // 零售异常订单管理
  { path: "/identityFeeManage", name: 'identityFeeManage', component: identityFeeManage }, // 新零售识别费管理
  { path: '/vending/distributorManage', name: 'distributorManage', component: merchantSearch }, // 新零售识别费管理-商家查询
  { path: '/identityOrderSearch', name: 'identityOrderSearch', component: identityOrderSearch }, // 新零售识别费管理-订单查询
  { path: '/monthBilling', name: 'monthBilling', component: monthBilling }, // 新零售识别费管理-月账单
  { path: '/vending/equipmentManage', name: 'vendingEquipmentManage', component: equipmentManage }, // 新零售设备管理
  {
    path: '/vendingOrderList',
    name: 'vendingOrderList',
    component: vendingOrder,
    redirect: '/vending/orderManage',
    children: [
      {
        path: '/vending/orderManage',
        name: 'vendingOrderManage',
        component: orderManage,
        meta: { keepAlive: true }
      }, // 新零售订单管理
      { path: '/vending/orderDetail',
        name: 'vendingOrderDetail',
        component: vendingOrderDetail
      }, // 新零售订单管理 订单详情
    ]
  },
  {
    path: '/vendingRepurchaseEasyOrderManage',
    name: 'vendingRepurchaseEasyOrderManage',
    component: repurchaseEasyOrderManage,
    redirect: '/vending/repurchaseEasyOrder',
    children: [
      {
        path: '/vending/repurchaseEasyOrder',
        name: 'vendingRepurchaseEasyOrder',
        component: repurchaseEasyOrderList,
        meta: { keepAlive: true }
      },
      {
        path: '/vending/repurchaseEasyOrderDetail',
        name: 'vendingRepurchaseEasyOrderDetail',
        component: repurchaseEasyOrderDetail,
      }
    ]
  },
  { path: '/vending/repurchaseEasyMerchantSearch', name: 'vendingRepurchaseEasyMerchantSearch', component: repurchaseEasyMerchantSearch } // 复购易商户查询
];
