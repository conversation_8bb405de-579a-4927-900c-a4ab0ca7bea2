/**
 *  签署协议路由
 *  @export
 *  @param
 *  @returns
 */
export const protocol = [
  {
    path: "/protocol/protocolList",
    name: "protocolList",
    component: () => import(/* webpackChunkName: "protocolList" */ '@views/protocol/protocolList'),
  },
  {
    path: "/protocol/protocolDetail",
    name: "protocolDetail",
    component: () => import(/* webpackChunkName: "protocolDetail" */ '@views/protocol/protocolDetail'),
  },
  {
    path: "/protocol/protocolTypeList",
    name: "protocolTypeList",
    component: () => import(/* webpackChunkName: "protocolTypeList" */ '@views/protocol/protocolTypeList'),
  },
  {
    path: "/protocol/protocolHistory",
    name: "protocolHistory",
    component: () => import(/* webpackChunkName: "protocolHistory" */ '@views/protocol/protocolHistory'),
  },
  {
    path: "/protocol/protocolLog",
    name: "protocolLog",
    component: () => import(/* webpackChunkName: "protocolLog" */ '@views/protocol/protocolLog'),
  },
]
