// 菜单测试工具
// 用于验证智能体应用菜单是否正常工作

/**
 * 检查菜单项是否存在
 * @param {Array} menuList - 菜单列表
 * @param {string} menuValue - 菜单值
 * @returns {boolean} - 是否存在
 */
export function checkMenuExists(menuList, menuValue = 'AiAgentManage') {
  if (!Array.isArray(menuList)) {
    console.warn('菜单列表不是数组:', menuList)
    return false
  }

  for (const menu of menuList) {
    // 检查当前菜单项
    if (menu.value === menuValue) {
      console.log('找到智能体应用菜单:', menu)
      return true
    }

    // 递归检查子菜单
    if (menu.children && Array.isArray(menu.children)) {
      if (checkMenuExists(menu.children, menuValue)) {
        return true
      }
    }
  }

  return false
}

/**
 * 打印菜单结构（用于调试）
 * @param {Array} menuList - 菜单列表
 * @param {number} level - 层级
 */
export function printMenuStructure(menuList, level = 0) {
  if (!Array.isArray(menuList)) {
    console.warn('菜单列表不是数组:', menuList)
    return
  }

  const indent = '  '.repeat(level)

  menuList.forEach(menu => {
    console.log(`${indent}- ${menu.name} (${menu.value}) [${menu.path || 'no-path'}]`)

    if (menu.children && Array.isArray(menu.children)) {
      printMenuStructure(menu.children, level + 1)
    }
  })
}

/**
 * 验证路由映射是否正确
 * @param {string} menuValue - 菜单值
 * @returns {boolean} - 是否正确
 */
export function validateRouterMapping(menuValue = 'AiAgentManage') {
  try {
    // 动态导入路由映射
    import('@/router/routerMap').then(({ routerMap }) => {
      const mapping = routerMap.find(item => item.value === menuValue)

      if (mapping) {
        console.log('路由映射正确:', mapping)
        return true
      } else {
        console.error('未找到路由映射:', menuValue)
        return false
      }
    })
  } catch (error) {
    console.error('验证路由映射失败:', error)
    return false
  }
}

/**
 * 测试页面组件是否可以正常加载
 * @returns {Promise<boolean>} - 是否可以加载
 */
export async function testComponentLoad() {
  try {
    const component = await import('@/views/aiAgent/index.vue')
    console.log('智能体应用组件加载成功:', component.default.name)
    return true
  } catch (error) {
    console.error('智能体应用组件加载失败:', error)
    return false
  }
}

/**
 * 完整的菜单测试
 * @param {Array} menuList - 菜单列表
 */
export function runMenuTest(menuList) {
  console.group('🔍 智能体应用菜单测试')

  // 1. 检查菜单是否存在
  console.log('1. 检查菜单项是否存在...')
  const menuExists = checkMenuExists(menuList)
  console.log(menuExists ? '✅ 菜单项存在' : '❌ 菜单项不存在')

  // 2. 验证路由映射
  console.log('2. 验证路由映射...')
  validateRouterMapping()

  // 3. 测试组件加载
  console.log('3. 测试组件加载...')
  testComponentLoad().then(success => {
    console.log(success ? '✅ 组件加载成功' : '❌ 组件加载失败')
  })

  // 4. 打印菜单结构（可选）
  if (process.env.NODE_ENV === 'development') {
    console.log('4. 当前菜单结构:')
    printMenuStructure(menuList)
  }

  console.groupEnd()

  return {
    menuExists,
    message: menuExists ? '菜单配置正常' : '菜单配置异常，请检查权限配置'
  }
}

/**
 * 在控制台中添加测试命令
 */
export function addTestCommands() {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    // 添加全局测试方法
    window.testAiAgentMenu = function() {
      console.log('🔍 开始查找菜单组件...')

      // 获取当前菜单列表
      const app = document.querySelector('#app').__vue__
      console.log('App实例:', app)

      if (app && app.$children && app.$children[0]) {
        const layout = app.$children[0]
        console.log('Layout组件:', layout)
        console.log('Layout子组件:', layout.$children)

        // 查找side-menu组件 (通过组件引用名称)
        let sideMenu = null

        // 方法1: 通过$refs查找
        if (layout.$refs && layout.$refs.sideMenu) {
          sideMenu = layout.$refs.sideMenu
          console.log('通过$refs找到菜单组件:', sideMenu)
        }

        // 方法2: 通过子组件查找
        if (!sideMenu) {
          sideMenu = layout.$children.find(child => {
            // 检查组件文件名或标签名
            return child.$options._componentTag === 'side-menu' ||
                   child.$vnode?.tag?.includes('side-menu') ||
                   (child.$options.__file && child.$options.__file.includes('side_menu'))
          })
          console.log('通过子组件查找找到菜单组件:', sideMenu)
        }

        // 方法3: 遍历所有子组件查找menuList属性
        if (!sideMenu) {
          function findMenuComponent(component) {
            if (component.menuList && Array.isArray(component.menuList)) {
              return component
            }
            if (component.$children) {
              for (const child of component.$children) {
                const found = findMenuComponent(child)
                if (found) return found
              }
            }
            return null
          }
          sideMenu = findMenuComponent(layout)
          console.log('通过递归查找找到菜单组件:', sideMenu)
        }

        if (sideMenu && sideMenu.menuList) {
          console.log('✅ 找到菜单组件，菜单列表:', sideMenu.menuList)
          return runMenuTest(sideMenu.menuList)
        } else {
          console.error('❌ 未找到菜单组件或菜单列表')
          console.log('Layout子组件详情:', layout.$children.map(child => ({
            name: child.$options.name,
            tag: child.$options._componentTag,
            file: child.$options.__file,
            hasMenuList: !!child.menuList
          })))
          return { menuExists: false, message: '未找到菜单组件' }
        }
      } else {
        console.error('❌ 未找到应用实例')
        return { menuExists: false, message: '未找到应用实例' }
      }
    }

    // 添加强制添加菜单的方法
    window.forceAddAiAgentMenu = function() {
      console.log('🔧 强制添加智能体应用菜单...')

      const app = document.querySelector('#app').__vue__
      if (app && app.$children && app.$children[0]) {
        const layout = app.$children[0]

        // 查找菜单组件
        function findMenuComponent(component) {
          if (component.menuList !== undefined) {
            return component
          }
          if (component.$children) {
            for (const child of component.$children) {
              const found = findMenuComponent(child)
              if (found) return found
            }
          }
          return null
        }

        const sideMenu = findMenuComponent(layout)

        if (sideMenu) {
          // 确保menuList是数组
          if (!Array.isArray(sideMenu.menuList)) {
            sideMenu.menuList = []
          }

          // 检查是否已存在
          const exists = sideMenu.menuList.some(menu =>
            menu.value === 'AiAgentManage' || menu.path === '/aiAgent'
          )

          if (!exists) {
            const aiAgentMenu = {
              id: 'ai-agent-force-' + Date.now(),
              authMenuId: 'ai-agent-force-' + Date.now(),
              name: '智能体应用',
              value: 'AiAgentManage',
              path: '/aiAgent',
              icon: 'el-icon-cpu',
              hide: 'N',
              children: null
            }

            sideMenu.menuList.push(aiAgentMenu)
            console.log('✅ 强制添加菜单成功:', aiAgentMenu)
            console.log('📋 当前菜单列表:', sideMenu.menuList)

            // 强制更新组件
            sideMenu.$forceUpdate()

            return { success: true, message: '菜单添加成功' }
          } else {
            console.log('ℹ️ 菜单已存在')
            return { success: true, message: '菜单已存在' }
          }
        } else {
          console.error('❌ 未找到菜单组件')
          return { success: false, message: '未找到菜单组件' }
        }
      } else {
        console.error('❌ 未找到应用实例')
        return { success: false, message: '未找到应用实例' }
      }
    }

    console.log('🛠️ 菜单测试命令已添加:')
    console.log('  - testAiAgentMenu() : 测试菜单配置')
    console.log('  - forceAddAiAgentMenu() : 强制添加菜单')
  }
}

// 自动添加测试命令
addTestCommands()
