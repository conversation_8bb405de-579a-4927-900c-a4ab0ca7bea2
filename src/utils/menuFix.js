// 菜单修复工具
// 用于修复菜单显示问题

/**
 * 修复现有菜单项的children属性
 */
export function fixMenuChildren() {
  console.log('🔧 开始修复菜单children属性...')
  
  const app = document.querySelector('#app').__vue__
  if (app && app.$children && app.$children[0]) {
    const layout = app.$children[0]
    
    // 查找菜单组件
    function findMenuComponent(component) {
      if (component.menuList !== undefined) {
        return component
      }
      if (component.$children) {
        for (const child of component.$children) {
          const found = findMenuComponent(child)
          if (found) return found
        }
      }
      return null
    }
    
    const sideMenu = findMenuComponent(layout)
    
    if (sideMenu && sideMenu.menuList) {
      let fixedCount = 0
      
      sideMenu.menuList.forEach(menu => {
        if (menu.children === null) {
          menu.children = []
          fixedCount++
          console.log(`✅ 修复菜单项: ${menu.name}`)
        }
      })
      
      if (fixedCount > 0) {
        sideMenu.$forceUpdate()
        console.log(`🎉 修复完成，共修复 ${fixedCount} 个菜单项`)
        return { success: true, fixedCount }
      } else {
        console.log('ℹ️ 没有需要修复的菜单项')
        return { success: true, fixedCount: 0 }
      }
    } else {
      console.error('❌ 未找到菜单组件')
      return { success: false, message: '未找到菜单组件' }
    }
  } else {
    console.error('❌ 未找到应用实例')
    return { success: false, message: '未找到应用实例' }
  }
}

/**
 * 强制刷新菜单显示
 */
export function forceRefreshMenu() {
  console.log('🔄 强制刷新菜单显示...')
  
  const app = document.querySelector('#app').__vue__
  if (app && app.$children && app.$children[0]) {
    const layout = app.$children[0]
    
    function findMenuComponent(component) {
      if (component.menuList !== undefined) {
        return component
      }
      if (component.$children) {
        for (const child of component.$children) {
          const found = findMenuComponent(child)
          if (found) return found
        }
      }
      return null
    }
    
    const sideMenu = findMenuComponent(layout)
    
    if (sideMenu) {
      // 强制更新组件
      sideMenu.$forceUpdate()
      
      // 如果有子组件，也强制更新
      if (sideMenu.$children) {
        sideMenu.$children.forEach(child => {
          child.$forceUpdate()
        })
      }
      
      console.log('✅ 菜单刷新完成')
      return { success: true }
    } else {
      console.error('❌ 未找到菜单组件')
      return { success: false, message: '未找到菜单组件' }
    }
  } else {
    console.error('❌ 未找到应用实例')
    return { success: false, message: '未找到应用实例' }
  }
}

/**
 * 检查菜单项的渲染条件
 */
export function checkMenuRenderConditions() {
  console.log('🔍 检查菜单项渲染条件...')
  
  const app = document.querySelector('#app').__vue__
  if (app && app.$children && app.$children[0]) {
    const layout = app.$children[0]
    
    function findMenuComponent(component) {
      if (component.menuList !== undefined) {
        return component
      }
      if (component.$children) {
        for (const child of component.$children) {
          const found = findMenuComponent(child)
          if (found) return found
        }
      }
      return null
    }
    
    const sideMenu = findMenuComponent(layout)
    
    if (sideMenu && sideMenu.menuList) {
      console.log('📋 菜单项渲染条件检查:')
      
      sideMenu.menuList.forEach((menu, index) => {
        const conditions = {
          name: menu.name,
          hasChildren: menu.children !== null && menu.children !== undefined,
          childrenType: Array.isArray(menu.children) ? 'array' : typeof menu.children,
          childrenLength: Array.isArray(menu.children) ? menu.children.length : 'N/A',
          hide: menu.hide,
          path: menu.path,
          willRender: menu.children !== null && menu.children !== undefined && menu.hide !== 'Y'
        }
        
        console.log(`${index + 1}. ${conditions.name}:`, conditions)
        
        if (!conditions.willRender) {
          console.warn(`⚠️ 菜单项 "${conditions.name}" 可能不会渲染`)
        }
      })
      
      return { success: true, menuList: sideMenu.menuList }
    } else {
      console.error('❌ 未找到菜单组件')
      return { success: false, message: '未找到菜单组件' }
    }
  } else {
    console.error('❌ 未找到应用实例')
    return { success: false, message: '未找到应用实例' }
  }
}

/**
 * 一键修复菜单显示问题
 */
export function fixMenuDisplay() {
  console.group('🛠️ 一键修复菜单显示问题')
  
  // 1. 检查渲染条件
  console.log('步骤1: 检查菜单渲染条件')
  const checkResult = checkMenuRenderConditions()
  
  if (!checkResult.success) {
    console.groupEnd()
    return checkResult
  }
  
  // 2. 修复children属性
  console.log('步骤2: 修复children属性')
  const fixResult = fixMenuChildren()
  
  // 3. 强制刷新
  console.log('步骤3: 强制刷新菜单')
  const refreshResult = forceRefreshMenu()
  
  // 4. 再次检查
  console.log('步骤4: 验证修复结果')
  const finalCheck = checkMenuRenderConditions()
  
  console.groupEnd()
  
  return {
    success: fixResult.success && refreshResult.success,
    fixedCount: fixResult.fixedCount || 0,
    message: '菜单修复完成'
  }
}

// 在开发环境下添加全局方法
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.fixMenuChildren = fixMenuChildren
  window.forceRefreshMenu = forceRefreshMenu
  window.checkMenuRenderConditions = checkMenuRenderConditions
  window.fixMenuDisplay = fixMenuDisplay
  
  console.log('🛠️ 菜单修复工具已加载，可用命令:')
  console.log('  - fixMenuDisplay() : 一键修复菜单显示问题')
  console.log('  - fixMenuChildren() : 修复children属性')
  console.log('  - forceRefreshMenu() : 强制刷新菜单')
  console.log('  - checkMenuRenderConditions() : 检查渲染条件')
}
