import router from '@/router/index';
import { exportFile } from '@/utils';
import request from '@/utils/request';
import { Message } from 'element-ui';
const PcRender = () => import('@/components/PcRender/index');

// authSystemId固定常量
export const AUTHSYSTEMID = '1060551741250347008';
const ramToken = 'ramToken';
export const setRamToken = (token) => {
  localStorage.setItem(ramToken, token);
  sessionStorage.setItem('token', token);
};
export const getRamToken = () => {
  return localStorage.getItem(ramToken);
};
export const clearRamToken = () => {
  sessionStorage.removeItem('token');
  return localStorage.removeItem(ramToken);
};

// 菜单权限键名
export const addRouteDynamic = 'addRouteDynamic';
// 缓存菜单权限
export const setRoute = (val) => {
  if (typeof val === 'undefined' || val === null) {
    val = '';
  }
  val = typeof val === 'string' ? val : JSON.stringify(val);
  localStorage.setItem(addRouteDynamic, val);
};
// 获取菜单权限
export const getRoute = () => {
  let val = localStorage.getItem(addRouteDynamic);
  return val ? JSON.parse(val) : undefined;
};
// 清除菜单权限
export const clearRoute = () => {
  localStorage.removeItem(addRouteDynamic);
};
// 比较菜单和路由，判断出需要动态添加的菜单
// 比较菜单和路由，判断出需要动态添加的菜单
export const needAddRouteDynamic = (menuList) => {
  const arr = [];
  if (Array.isArray(menuList) && menuList.length > 0) {
    const copyMenuList = flatten(menuList);
    copyMenuList.forEach((item) => {
      // 过滤出接口返回的页面但是没有在路由中声明的长度大于等于19的组件库页面路径
      if (item.path && new RegExp(/^\/\d{19,}$/g).test(item.path)) {
        arr.push(item);
      }
    });
  }
  return arr;
};
// 将菜单扁平化
const flatten = (menuList) => {
  return menuList.reduce((result, item) => {
    return result.concat(item, Array.isArray(item.children) ? flatten(item.children) : []);
  }, []);
};

// 动态添加路由
export const addRoute = (list = []) => {
  list.forEach((item) => {
    if (item) {
      const route = {
        path: item.path,
        name: item.path,
        component: PcRender,
        meta: {
          queryButtonAuthCode: item.queryButtonAuthCode,
          queryFieldAuthCode: item.queryFieldAuthCode,
        },
      };
      router.addRoute('root', route);
    }
  });
};
let _headerRamToken = {
  'Ram-token': getRamToken(),
  'Ram-system': AUTHSYSTEMID,
};
// 登陆之后需要更新一下，因为存储的值还是null
export const resetHeaderRamToken = () => {
  _headerRamToken['Ram-token'] = getRamToken();
};
// 接口header带token
export const headerRamToken = _headerRamToken;

// 导出文件-带token
export const downloadFileRamToken = async (url) => {
  const res = await request({
    url: url,
    method: 'get',
    responseType: 'blob',
  });
  if (res.data && res.data.type && res.data.type === 'application/json') {
    Message({
      message: '下载失败',
      type: 'error',
      duration: 3000,
    });
    return;
  }
  if (res?.data) {
    console.log(res)
    try {
      const blob = new Blob([res.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const disposition = decodeURI(res.headers['content-disposition']);
      const fileName = disposition.substring(
        disposition.indexOf('filename=') + 9,
        disposition.length
      );
      const decodeFileName = decodeURIComponent(escape(fileName));
      exportFile(window.URL.createObjectURL(blob), decodeFileName);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error(err);
    }
  }
};
