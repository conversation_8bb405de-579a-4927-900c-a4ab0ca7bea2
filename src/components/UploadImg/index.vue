<template>
  <div class="upload-img">
    <el-upload
      :class="{ uploadDisabled: uploadDisabled }"
      :action="uploadApi"
      list-type="picture-card"
      ref="upload"
      with-credentials
      accept="image/jpeg,image/jpg,image/png"
      :limit="limit"
      :multiple="limit > 1"
      :on-success="handleSuccess"
      :file-list="fileList"
      :headers="getHeaders"
      :on-preview="handlePictureCardPreview"
      :on-exceed="handleExceed"
      :before-remove="beforeRemove"
      :on-remove="handleRemove"
      :before-upload="beforeUpload"
      :on-change="onChange"
    >
      <i class="el-icon-plus"></i>
    </el-upload>

    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="预览图片"
      width="800px"
      :visible.sync="dialogVisible"
      append-to-body
    >
      <el-image :src="src" alt="" style="max-width: 100%" :preview-src-list="[src]"></el-image>
    </el-dialog>
  </div>
</template>

<script>
import config from '@/assets/js/config';
import { headerRamToken } from '@/utils/menu';
const prefix = config.aliyunImgBaseUrl;
export default {
  name: 'upload-img',
  props: {
    limit: {
      type: Number,
      default: 1,
    },
    file: {
      type: [String, Array],
      default: '',
    },
    maxWidth: {
      type: Number,
      default: 0,
    },
    maxSize: {
      // 传入kb
      type: Number,
      default: 0,
    },
    uploadApi: {
      type: String,
      default: '',
    },
    imgDir: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      temp: [],
      fileList: [],
      src: '',
      dialogVisible: false,
    };
  },
  model: {
    prop: 'file',
    event: 'update',
  },
  computed: {
    uploadDisabled: function () {
      return this.temp.length >= this.limit;
    },
    getHeaders(){
      const headers = this.headers || {};
      return {
        ...headerRamToken,
        ...headers,
      };
    },
  },
  created() {
    this.init();
  },
  watch: {
    file: function () {
      // console.log('file', this.file)
      if (this.limit === 1) {
        this.init();
      } else {
        if (this.file.length === 0) {
          this.init();
        }
      }
    },
  },
  methods: {
    init: function () {
      // console.log('created', this.file)
      if (this.file) {
        if (this.limit > 1) {
          this.fileList = [
            ...this.file.map((url) => {
              return {
                name: '',
                para: this.imgDir + url,
                url: prefix + this.imgDir + url,
              };
            }),
          ];
        } else {
          this.fileList = this.file
            ? [
                {
                  name: '',
                  para: this.file,
                  url: prefix + this.file,
                },
              ]
            : [];
        }
        this.temp = [...this.fileList];
      } else {
        this.temp = [];
        this.fileList = [];
      }
    },
    handleUpdateFile: function () {
      return this.limit > 1
        ? [...this.temp.map((item) => item.para)]
        : this.temp.length === 0
        ? ''
        : this.temp[0].para;
    },
    beforeUpload: function (file) {
      if (this.maxSize > 0 && file.size > this.maxSize * 1000) {
        this.$message.warning(`上传图片限制${this.maxSize}kb, 请合理压缩图片大小再上传`);
        return false;
      }
      if (this.maxWidth > 0) {
        let imgWidth = '';
        let imgHeight = '';
        let _this = this;
        const limitSize = new Promise((resolve, reject) => {
          let _URL = window.URL || window.webkitURL;
          let img = new Image();
          img.src = _URL.createObjectURL(file);
          img.onload = function () {
            imgWidth = img.width;
            imgHeight = img.height;
            let valid = img.width <= _this.maxWidth;
            valid ? resolve() : reject();
          };
        }).then(
          () => {
            return file;
          },
          () => {
            _this.$message.warning({
              message: `上传文件的图片大小不合符标准,宽建议为${_this.maxWidth}px,当前上传图片的宽度为${imgWidth}px`,
              btn: false,
            });
            return Promise.reject();
          }
        );
        return limitSize;
      }
      return true;
    },
    handleSuccess: function (res, file, file_list) {
      if (res && res.result === 1) {
        if (this.limit > 1) {
          this.temp.push({
            name: res.para,
            para: this.imgDir + res.para,
            url: prefix + this.imgDir + res.para,
          });
        } else {
          this.temp = [
            {
              name: res.para,
              para: this.imgDir + res.para,
              url: prefix + this.imgDir + res.para,
            },
          ];
        }
        this.$emit('update', this.handleUpdateFile());
      } else {
        this.handleError(res, file, file_list);
      }
    },
    handlePictureCardPreview: function (file, file_list) {
      this.src = file.url;
      this.dialogVisible = true;
    },
    handleError: function (err, file, file_list) {
      this.fileList = [...this.fileList];
      this.temp = [...this.fileList];
      this.$message.warning(err.data || '上传失败，请刷新重试');
      this.$emit('update', this.handleUpdateFile());
    },
    beforeRemove: async function () {
      await this.$confirm('是否删除该图片?', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
    },
    handleRemove: function (file, file_list) {
      let _files = file_list.map((item) => {
        return {
          name: '',
          para: item.response ? this.imgDir + item.response.para : item.url,
          url: item.response ? prefix + this.imgDir + item.response.para : item.url,
        };
      });
      this.fileList = [..._files];
      this.temp = [...this.fileList];
      this.$emit('update', this.handleUpdateFile());
    },
    handleExceed: function (file, file_list) {
      this.$message.warning('超出可上传数量');
    },
    onChange: function (file, fileList) {
      // console.log('change', fileList)
    },
  },
  beforeDestroy() {
    this.fileList = [];
    this.temp = [];
    this.$refs.upload.clearFiles();
    this.$emit('update', this.handleUpdateFile());
  },
};
</script>

<style scoped lang="less">
.uploadDisabled {
  /deep/ .el-upload {
    display: none;
  }
}
</style>
