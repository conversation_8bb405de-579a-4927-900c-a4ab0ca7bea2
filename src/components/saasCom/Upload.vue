<!-- 上传图片 -->
<template>
  <div class="upload-box">
    <label><input type="file" style="display:none;" @change="fileChange($event)" accept="image/*" title=""/></label>
    <slot></slot>
  </div>
</template>

<script>
import Vue from 'vue';
import { 
  uploadFile
} from '@/api/saas2/api';
export default {
  name:'Upload',
  props:{
    options:null
  },
  data () {
    return {
    };
  },

  created(){},

  mounted(){},

  methods: {
    fileChange(e){
      if (!e.target.files[0].size) return;//判断是否有文件数量
      let a = e.target.files[0];
      let fd = new FormData();
      fd.append('file', a);
      let imgSize = 10;
      if (a.size && a.size / 1000 / 1000 > imgSize) {
        return;
      };
      uploadFile(fd).then(res => {
        if (res.result === 0) {
          this.$emit("img",{data:this.options,img:res.data.pictureUrl});
        };
      });
      e.target.value = ''//清空val值，以便可以重复添加一张图片
    }
  },
  watch: {
  },
  components: {},
}
</script>
<style scoped lang='less'>
.upload-box{
  position: relative;
  label{
    position: absolute;
    width:100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    opacity: 0;
    cursor: pointer;
  }
}
</style>