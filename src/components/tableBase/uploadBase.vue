<template>
  <el-upload
    ref="upload"
    :accept="accept"
    class="upload"
    :action="uploadUrl"
    :show-file-list="false"
    :data="uploadObj"
    :on-change="beforeUpload"
    :on-success="upload"
    :auto-upload="false"
    :headers="headerRamToken"
  >
    <slot></slot>
  </el-upload>
</template>

<script>
import {
  isEqual,
  isNotEmptyString
} from "@/components/tableBase/utilsTableBase.js";
import { headerRamToken } from '@/utils/menu'

export default {
  name: "uploadBase",

  props: {},

  components: {},

  mixins: [],

  data() {
    return {
      headerRamToken,
      accept: "text/plain",
      uploadObj: {},
      uploadUrl: ""
    };
  },

  beforeCreate() {},

  created() {},

  beforeMount() {},

  mounted() {},

  beforeUpdate() {},

  updated() {},

  activated() {},

  deactivated() {},

  beforeDestroy() {},

  destroyed() {},

  errorCaptured() {},

  methods: {
    beforeUpload(file) {
      this.$emit("beforeUpload", file);
    },
    upload(response, file, fileList) {
      this.$emit("upload", response, file, fileList);
    },
    setAcceptSecurityFun(accept) {
      const text = "text";
      const img = "img";
      const excel = "excel";
      if (isEqual(accept, text)) {
        this.accept = "text/plain";
      } else if (isEqual(accept, img)) {
        this.accept = "image/jpeg, image/png";
      } else if (isEqual(accept, excel)) {
        this.accept = ".excel,.xls,.xlsx";
      }
    },
    setUploadObjFun(obj) {
      this.uploadObj = obj;
    },
    setUploadUrlSecurityFun(url) {
      if (isNotEmptyString(url)) {
        this.uploadUrl = url;
      }
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    clearFiles() {
      this.$refs.upload.clearFiles();
    }
  },

  computed: {},

  watch: {}
};
</script>
<style lang='css' scoped>
.upload {
  display: inline-block;
  margin-left: 10px;
}
</style>
