<template>
  <div @click="callBackFun()">
    <div v-if="isEqual(kind, typeObj.success.name)">
      <el-button :type="typeObj.success.type" :icon="typeObj.success.icon">
        <slot></slot>
      </el-button>
    </div>
    <div v-else-if="isEqual(kind, typeObj.text.name)">
      <el-button :type="typeObj.text.type">
        <slot></slot>
      </el-button>
    </div>
    <div v-else-if="isEqual(kind, typeObj.textDanger.name)">
      <el-button :type="typeObj.textDanger.type" class="textDanger">
        <slot></slot>
      </el-button>
    </div>
    <div v-else-if="isEqual(kind, typeObj.export.name)">
      <el-button :type="typeObj.export.type" icon="el-icon-download">
        <slot></slot>
      </el-button>
    </div>
  </div>
</template>

<script>
import { isEqual } from "@/components/tableBase/utilsTableBase.js";

export default {
  name: "buttonBase",

  props: {
    kind: {
      type: String,
      default: "primary"
    }
  },

  components: {},

  mixins: [],

  data() {
    return {
      typeObj: {
        success: {
          name: "success",
          type: "success",
          icon: "el-icon-circle-plus-outline"
        },
        text: {
          name: "text",
          type: "text"
        },
        textDanger: {
          name: "textDanger",
          type: "text"
        },
        export: {
          name: "export",
          type: "success"
        }
      },
      isEqual: isEqual
    };
  },

  beforeCreate() {},

  created() {},

  beforeMount() {},

  mounted() {},

  beforeUpdate() {},

  updated() {},

  activated() {},

  deactivated() {},

  beforeDestroy() {},

  destroyed() {},

  errorCaptured() {},

  methods: {
      callBackFun() {
          this.$emit('click');
      }
  },

  computed: {},

  watch: {}
};
</script>
<style lang='css' scoped>
div {
  display: inline-block;
  user-select: none;
}
.textDanger {
  color: red;
}
</style>
