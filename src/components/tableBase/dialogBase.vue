<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      v-if="visible"
      :width="width"
      :before-close="handleClose"
      v-loading="loading"
    >
      <template v-if="!title">
        <div slot="title">
          <slot name="title"></slot>
        </div>
      </template>
      <slot></slot>
      <span v-if="isShowDefaultFooter" slot="footer" class="dialog-footer">
        <el-button
          v-if="isShowDefaultFooterCancelBtn"
          :type="defaultFooterCancelBtnType"
          @click="handleClose"
          >{{ defaultFooterCancelBtnText }}</el-button
        >
        <el-button
          v-if="isShowDefaultFooterSaveBtn"
          :type="defaultFooterSaveBtnType"
          :loading="defaultFooterSaveBtnLoading"
          @click="handleSave"
          >{{ defaultFooterSaveBtnText }}</el-button
        >
        <span v-if="isShowCustomFooter">
          <slot name="customFooter"></slot>
        </span>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "dialogBase",

  props: {},

  components: {},

  mixins: [],

  data() {
    return {
      visible: false,
      title: "",
      width: "30%",
      isShowDefaultFooter: true,
      isShowCustomFooter: false,
      isShowDefaultFooterCancelBtn: true,
      isShowDefaultFooterSaveBtn: true,
      defaultFooterCancelBtnText: "取 消",
      defaultFooterCancelBtnType: "default",
      defaultFooterSaveBtnText: "保 存",
      defaultFooterSaveBtnType: "primary",
      defaultFooterSaveBtnLoading: false,
      loading: false
    };
  },

  beforeCreate() {},

  created() {},

  beforeMount() {},

  mounted() {},

  beforeUpdate() {},

  updated() {},

  activated() {},

  deactivated() {},

  beforeDestroy() {},

  destroyed() {},

  errorCaptured() {},

  methods: {
    // 打开弹窗
    openFun() {
      this.visible = true;
    },
    // 关闭弹窗
    closeFun() {
      this.visible = false;
    },
    // 关闭按钮事件
    handleClose() {
      this.closeFun();
      this.$emit("close");
    },
    // 保存按钮事件
    handleSave() {
      this.$emit("confirm");
    },
    // 设置title
    setTitleFun(title) {
      this.title = title;
    },
    // 显示默认按钮
    showDefaultFooterFun() {
      this.isShowDefaultFooter = true;
    },
    // 隐藏默认按钮
    highDefaultFooterFun() {
      this.isShowDefaultFooter = false;
    },
    // 显示自定义底部
    showCustomFooterFun() {
      this.isShowCustomFooter = true;
    },
    // 隐藏自定义底部
    highCustomFooterFun() {
      this.isShowCustomFooter = false;
    },
    // 显示默认取消按钮
    showDefaultFooterCancelBtnFun() {
      this.isShowDefaultFooterCancelBtn = true;
    },
    // 隐藏默认取消按钮
    highDefaultFooterCancelBtnFun() {
      this.isShowDefaultFooterCancelBtn = false;
    },
    // 显示默认保存按钮
    showDefaultFooterSaveBtnFun() {
      this.isShowDefaultFooterSaveBtn = true;
    },
    // 隐藏默认保存按钮
    highDefaultFooterSaveBtnFun() {
      this.isShowDefaultFooterSaveBtn = false;
    },
    //
    showDefaultFooterSaveBtnLoadingFun() {
      this.defaultFooterSaveBtnLoading = true;
    },
    //
    highDefaultFooterSaveBtnLoadingFun() {
      this.defaultFooterSaveBtnLoading = false;
    },
    // 设置默认取消按钮文字
    setDefaultFooterCancelBtnTextFun(text) {
      this.defaultFooterCancelBtnText = text;
    },
    // 设置默认保存按钮文字
    setDefaultFooterSaveBtnTextFun(text) {
      this.defaultFooterSaveBtnText = text;
    },
    // 设置默认取消按钮类型
    setDefaultFooterCancelBtnTypeFun(type) {
      this.defaultFooterCancelBtnType = type;
    },
    // 设置默认保存按钮类型
    setDefaultFooterSaveBtnTypeFun(type) {
      this.defaultFooterSaveBtnType = type;
    },
    //
    setWidthFun(width) {
      this.width = width;
    },
    // 弹窗加载
    setLoading(loading) {
      this.loading = !!loading;
    }
  },

  computed: {},

  watch: {}
};
</script>
<style lang="css" scoped></style>
