import _ from "lodash";
import qs from "qs";
import Clipboard from "clipboard";
import { Message } from "element-ui";

export function isNotEmptyObject(obj) {
  let flag = false;
  if (isObject(obj) && Object.keys(obj).length > 0) {
    flag = true;
  }
  return flag;
}

export function isObject(obj) {
  let flag = false;
  if (obj && isEqual(typeof obj, "object") && !Array.isArray(obj)) {
    flag = true;
  }
  return flag;
}

export function isFunction(fun) {
  let flag = false;
  if (fun && isEqual(typeof fun, "function")) {
    flag = true;
  }
  return flag;
}

export function isNumber(num) {
  let flag = false;
  if (isEqual(typeof num, "number") && (num || isEqual(num, 0)) && !isNaN(num)) {
    flag = true;
  }
  return flag;
}

export function isEqual(value, other) {
  let flag = false;
  if (value === other) {
    flag = true;
  }
  return flag;
}

export function isNotEmptyArray(arr) {
  let flag = false;
  if (arr && Array.isArray(arr) && arr.length > 0) {
    flag = true;
  }
  return flag;
}

export function isNotEmptyString(str) {
  let flag = false;
  if (isString(str) && str.length > 0) {
    flag = true;
  }
  return flag;
}

export function isString(str) {
  let flag = false;
  if ((str || isEqual(str, "")) && isEqual(typeof str, "string")) {
    flag = true;
  }
  return flag;
}

export function hasOwnPorpery(obj, property) {
  return Object.prototype.hasOwnProperty.call(obj, property);
}

export function getDataByPath(value, path) {
  const pathArr = handleDataPath(path).split(".");
  let data = value;
  pathArr.forEach(item => {
    if (data && (data[item] || hasOwnPorpery(data, item))) {
      data = data[item];
    }
  });
  return data;
}

export function handleDataPath(path) {
  let flag = false;
  if (!isNotEmptyString(path)) {
    flag = false;
  } else {
    flag = true;
    if (!isEqual(path.indexOf("["), -1)) {
      path = path.replace(/\[/g, ".");
      path = path.replace(/\]/g, "");
    }
  }
  return flag ? path : "";
}

// 本地返回true，不是返回false
function isLocal() {
  let flag = false;
  if (/^(http:\/\/(127.0.0.1|192.168)+)/.test(window.location.origin)) {
    flag = true;
  }
  return flag;
}

// 模拟接口返回
function getRes(obj) {
  return new Promise(resolve => {
    let copyObj = {
      resultTime: 200
    };
    if (_.isObject(obj)) {
      copyObj = Object.assign({}, copyObj, obj);
    }
    setTimeout(() => {
      resolve({});
    }, copyObj.resultTime);
  });
}

function dayMilliseconds() {
  return 1 * 24 * 60 * 60 * 1000;
}
// 深拷贝
const deepClone = value => {
  let copyValue = value;
  if (_.isArray(value) || _.isObject(value)) {
    copyValue = JSON.parse(JSON.stringify(value));
  }
  return copyValue;
};
// lodash
const lodash = _;

// 导出
const exportFun = (url, params = {}) => {
  window.open(encodeURI(`${url}?${qs.stringify(params)}`), true);
};

// 复制到剪贴板
const copyFun = (text, sign = "copy-sign") => {
  if (_.isString(text) || _.isNumber(text)) {
    const clipboard = new Clipboard(`.${sign}`, {
      text: function() {
        return text;
      }
    });
    clipboard.on("success", e => {
      Message.success("复制成功!");
      // 释放内存
      clipboard.destroy();
    });
    clipboard.on("error", e => {
      // 不支持复制
      Message.error("该浏览器不支持自动复制!");
      // 释放内存
      clipboard.destroy();
    });
  }
};

// 判断是否正常字符串
const normalString = str => {
  let normal = false;
  if (
    lodash.isString(str) &&
    !lodash.isEmpty(str) &&
    ["undefined", "NaN", "null"].indexOf(str) === -1 &&
    !lodash.isEmpty(str)
  ) {
    normal = true;
  }
  return normal;
};

export {
  isLocal,
  getRes,
  dayMilliseconds,
  // 深拷贝
  deepClone,
  // lodash
  lodash,
  // 导出
  exportFun,
  // 复制到剪贴板
  copyFun,
  // 判断是否正常字符串
  normalString
};
