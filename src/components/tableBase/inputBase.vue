<template>
  <div>
    <div v-if="isEqual(kind, typeObj.success.name)">
      <el-button :type="typeObj.success.type" :icon="typeObj.success.icon">
        <slot></slot>
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "inputBase",

  props: {
    placeholder: {
      type: String,
      default: ""
    }
  },

  components: {},

  mixins: [],

  data() {
    return {};
  },

  beforeCreate() {},

  created() {},

  beforeMount() {},

  mounted() {},

  beforeUpdate() {},

  updated() {},

  activated() {},

  deactivated() {},

  beforeDestroy() {},

  destroyed() {},

  errorCaptured() {},

  methods: {},

  computed: {},

  watch: {}
};
</script>
<style lang='css' scoped>
</style>
