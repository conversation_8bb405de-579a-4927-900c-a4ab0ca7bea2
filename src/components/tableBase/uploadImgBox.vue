<template>
  <div v-loading="loading" class="upload-img-box">
    <div v-if="url">
      <img :src="prefix + url" ref="imgRef" class="avatar" />
    </div>
    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
  </div>
</template>

<script>
export default {
  name: "uploadImgBox",

  props: {},

  components: {},

  mixins: [],

  data() {
    return {
      loading: false,
      url: "",
      prefix: "http://lyy-public.oss-cn-shenzhen.aliyuncs.com/"
    };
  },

  beforeCreate() {},

  created() {},

  beforeMount() {},

  mounted() {},

  beforeUpdate() {},

  updated() {},

  activated() {},

  deactivated() {},

  beforeDestroy() {},

  destroyed() {},

  errorCaptured() {},

  methods: {
    setImgUrlFun(url) {
      this.url = url;
    },
    reSetImgUrlFun() {
      this.url = "";
    },
    showLoading() {
      this.loading = true;
    },
    highLoading() {
      this.loading = false;
    }
  },

  computed: {},

  watch: {}
};
</script>
<style lang='less' scoped>
.upload-img-box {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  &:hover {
    border-color: #409eff;
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 154px;
  height: 154px;
  line-height: 154px;
  text-align: center;
}

.avatar {
  width: 154px;
  height: 154px;
  display: block;
}

.upload-box {
  /deep/ .el-upload-dragger {
    width: 154px;
    height: 154px;
  }
}

.img-close {
  position: absolute;
  top: 0;
  left: 120px;
  z-index: 100;
  font-size: 30px;
  transform: translate(50%, -50%);
  cursor: pointer;
}
</style>
