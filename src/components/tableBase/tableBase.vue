<template>
  <div>
    <el-table
      ref="table"
      :data="pageInfo.list"
      border
      @highlight-current-row="setHighlightCurrentRow"
      style="width: 100%;margin-bottom: 20px;"
      v-loading="pageInfo.loading"
      @filter-change="filterHandlerRange"
      @selection-change="handleSelectionChange"
      @select="handleSelection"
      @sort-change="handleSortChange"
      :row-class-name="setRowClassName"
      :span-method="spanMethod"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <template v-for="(item, index) in pageInfo.columns">
        <!-- 多选框 -->
        <el-table-column
          v-if="item.isSelection"
          type="selection"
          width="55"
          :key="index"
          :selectable="selectable"
        ></el-table-column>
        <!-- 下拉筛选 -->
        <el-table-column
          v-else-if="item.isNeedFilters"
          :prop="item.key"
          :label="item.label"
          align="center"
          :filters="item.filters"
          :filter-method="filterTag"
          :column-key="item.key"
          :filter-multiple="false"
          :key="index"
        >
          <template slot-scope="scope">
            <div v-if="item.slot">
              <slot :name="item.key" :row="scope.row"></slot>
            </div>
            <div v-else>{{scope.row[item.key]}}</div>
          </template>
        </el-table-column>
        <!-- 排序 -->
        <el-table-column v-else-if="item.isNeedSort" :prop="item.key" :label="item.label" align="center" :key="index" sortable>
          <template slot-scope="scope">
            <div v-if="item.slot">
              <slot :name="item.key" :row="scope.row"></slot>
            </div>
            <div v-else>{{scope.row[item.key]}}</div>
          </template>
        </el-table-column>
        <!--自定义表头-->
        <el-table-column v-else-if="item.isCustomHeader" :prop="item.key" :label="item.label" align="center" :key="index" :width="item.width">
          <template slot="header" slot-scope="scope">
            <slot :name="`header-${item.key}`" :row="scope.row">
              {{item.label}}
            </slot>
          </template>
          <template slot-scope="scope">
            <div>
              <slot :name="item.key" :row="scope.row">
                {{scope.row[item.key]}}
              </slot>
            </div>
          </template>
        </el-table-column>
        <!-- 默认 -->
        <el-table-column v-else :prop="item.key" :label="item.label" align="center" :key="index" :width="item.width" v-bind="bindProps(item.bindProps)">
          <template slot-scope="scope">
            <div v-if="item.slot">
              <slot :name="item.key" :row="scope.row"></slot>
            </div>
            <div v-else>{{scope.row[item.key]}}</div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <el-pagination
      :page-sizes="pageInfo.pageSizeArr"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      :current-page="pageInfo.pageIndex"
      background
      layout="total, prev, pager, next, sizes, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      v-if="pageInfo.isShowPagination"
    />
  </div>
</template>

<script>
import TableBaseMixins from "@/components/tableBase/tableBaseMixins.js";
import {
  isNumber,
  isNotEmptyArray,
} from "@/components/tableBase/utilsTableBase.js";
import _ from 'lodash';

export default {
  name: "tableBase",

  components: {},

  mixins: [TableBaseMixins],

  data() {
    return {
      pageInfo: {
        columns: [],
        pageSize: 10,
        total: 0,
        pageIndex: 1,
        loading: false,
        list: [],
        pageSizeArr: [10, 20, 30, 40],
        isShowPagination: true,
      }
    };
  },

  beforeCreate() {},

  created() {},

  beforeMount() {},

  mounted() {},

  beforeUpdate() {},

  updated() {},

  activated() {},

  deactivated() {},

  beforeDestroy() {},

  destroyed() {},

  errorCaptured() {},

  methods: {
    // loading
    showLoadingFun() {
      this.pageInfo.loading = true;
    },
    closeLoadingFun() {
      this.pageInfo.loading = false;
    },

    // 每列名称
    setColumnsSecurity(columns) {
      let flag = false;
      if (isNotEmptyArray(columns)) {
        flag = true;
      }
      if (flag) {
        this.setColumnsFun(columns);
      }
    },
    setColumnsFun(columns) {
      this.pageInfo.columns = columns;
    },
    // 可选条数
    setPageSizeArrSecurity(pageSizeArr) {
      let flag = false;
      if (isNotEmptyArray(pageSizeArr)) {
        flag = true;
      }
      if (flag) {
        this.setPageSizeArrFun(pageSizeArr);
      }
    },
    setPageSizeArrFun(pageSizeArr) {
      this.pageInfo.pageSizeArr = pageSizeArr;
    },
    // 列表
    setListSecurity(list) {
      let flag = false;
      if (isNotEmptyArray(list)) {
        flag = true;
      }
      if (flag) {
        this.setListFun(list);
      }
    },
    reSetListFun() {
      this.pageInfo.list = [];
    },
    setListFun(list) {
      this.pageInfo.list = list;
    },
    // 总数
    setTotalSecurity(total) {
      let flag = false;
      if (isNumber(total)) {
        flag = true;
      }
      if (flag) {
        this.setTotalFun(total);
      }
    },
    setTotalFun(total) {
      this.pageInfo.total = total;
    },
    // 页数
    reSetPageIndex() {
      this.pageInfo.pageIndex = 1;
    },
    setPageIndexSecurity(num) {
      if (isNumber(num)) {
        this.setPageIndexFun(num);
      }
    },
    setPageIndexFun(num) {
      this.pageInfo.pageIndex = num;
    },
    // 条数
    setPageSizeSecurity(num) {
      if (isNumber(num)) {
        this.setPageSizeFun(num);
      }
    },
    setPageSizeFun(num) {
      this.pageInfo.pageSize = num;
    },
    // 改变列表每页条数
    handleSizeChange(val) {
      this.setPageIndexSecurity(1); // 改变每页条数应该从第一页重新开始查
      this.setPageSizeSecurity(val);
      this.handleCallBack();
    },
    // 改变页码
    handleCurrentChange(val) {
      this.setPageIndexSecurity(val);
      this.handleCallBack();
    },
    handleCallBack() {
      this.$emit("callBack");
    },
    filterTag(value, row, column) {
      return true;
      // return row.equipmentTypeName === value;
    },
    filterHandlerRange(arr) {
      this.$emit("filter", arr);
    },
    // 点击全选和单行触发
    handleSelectionChange(val) {
      this.$emit("selection", val);
    },
    // 点击全选不会触发
    handleSelection(selection, row) {
      this.$emit("selectionRow", selection, row);
    },
    // 排序
    handleSortChange(column, prop, order) {
      this.$emit("handleSortChange", column, prop, order);
    },
    setIsShowPagination(isShowPagination) {
      this.pageInfo.isShowPagination = isShowPagination;
    },
    selectable(row, index) {
      return true
    },
    // 设置行的class-name
    setRowClassName({row, rowIndex}) {},
    // 是否高亮显示当前行
    setHighlightCurrentRow() {
      return true;
    },
    // 合并行或者列
    spanMethod({ row, column, rowIndex, columnIndex }) {
      return [1, 1];
    },
    // 动态绑定属性
    bindProps(bindProps) {
      let attribute = {};
      if (_.isObject(bindProps)) {
        attribute = Object.assign({}, bindProps);
      }
      return attribute;
    },
  },

  computed: {},

  watch: {}
};
</script>
<style lang='css' scoped>
</style>
