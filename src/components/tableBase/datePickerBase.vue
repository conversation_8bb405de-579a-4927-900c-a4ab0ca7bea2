<template>
   <div class="date-picker-base"></div>
</template>

<script>
export default {
  name: 'datePickerBase',

  props: {},

  components: {},

  mixins: [],

  data() {
    return {};
  },

  beforeCreate() {},

  created() {},

  beforeMount() {},

  mounted() {},

  beforeUpdate() {},

  updated() {},

  activated() {},

  deactivated() {},

  beforeDestroy() {},

  destroyed() {},

  errorCaptured() {},

  methods: {},

  computed: {},

  watch: {},
};

</script>
<style lang='css' scoped>
</style>
