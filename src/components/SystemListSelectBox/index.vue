<!--2019/07/25-->
<template>
  <!-- <div class="select-box"> -->
    <el-select v-model="select_adSystemId" size="mini" clearable filterable placeholder="请选择" @change="selectID">
        <el-option
        v-for="item in systemList"
        :key="item.adSystemId"
        :label="item.adSystemId+'->'+item.name"
        :value="item.adSystemId">
        </el-option>
    </el-select>
  <!-- </div> -->
</template>

<script>
import { getAllSystem } from '@api/systemManage'

  export default {
    name:"select-box",
    props: [],
    components: {},
    data () {
      return {
        select_adSystemId:'',//选中的id
        systemList: [],//获取的列表
      };
    },

    beforeCreate() {},

    created() {},

    beforeMount() {},

    mounted() {
      this.system_list();
    },

    beforeUpdate() {},

    updated() {},

    activated() {},

    deactivated() {},

    beforeDestroy() {},

    destroyed() {},

    errorCaptured() {},

    methods: {
      // 获取所有系统列表
        system_list(){
            getAllSystem().then(res=> {
                if (res.result == 0) {
                    this.systemList = res.data
                }
            })
        },
        // 选中触发请求列表
        selectID(){
          this.$emit('selectID',this.select_adSystemId,'SystemIdSlected');//资源管理-筛选系统ID，需要把选中的id类型传去列表页，方便级联筛选父级ID
        },
        // 设置默认选中的系统id
        setID(adSystemId){
          this.select_adSystemId = adSystemId
        }
    },

    computed: {},

    watch: {}

  }

</script>
<style scoped>

</style>