<!--
 * @Description: 项目补贴金额管控
 * @Author: linjie
 * @Date: 2020/5/18 15:25
 * @LastEditors: linjie
 * @LastEditTime: 2020/5/18 15:25
 -->
<template>
  <el-form :inline="true" v-show="searchList.length>0 || $slots.default">
    <template v-for="item in searchList">
      <el-form-item v-if="item.type == 'date'" :label="item.label">
        <el-date-picker
            v-model="formData[item.valueName]"
            :type="item.dateType? item.dateType:'daterange'"
            range-separator="至"
            :start-placeholder="item.startPlaceholder?item.startPlaceholder:'开始日期'"
            :end-placeholder="item.endPlaceholder?item.endPlaceholder:'结束日期'"
            :value-format="item.valueFormat?item.valueFormat:'yyyy-MM-dd'"
            :picker-options="item.pickerOptions"
            :default-time="item.defaultTime"
            @change="item.changeDataFun?item.changeDataFun:()=>{}"
            :clearable="item.clearable?item.clearable: false"
            :editable="item.editable?item.editable: false"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item v-if="item.type == 'input'" :label="item.label">
        <el-input v-model="formData[item.valueName]" :placeholder="item.placeholder" @blur="item.changeDataFun&&item.changeDataFun(formData[item.valueName])" @keyup.enter.native="searchFun" clearable v-bind="item.others ? item.others: {}"></el-input>
      </el-form-item>
      <el-form-item v-if="item.type == 'select'" @change="item.changeDataFun&&item.changeDataFun(formData[item.valueName])" :label="item.label">
        <el-select v-model="formData[item.valueName]" :placeholder="item.placeholder">
          <el-option v-for="option in item.options" :key="option.value" :label="option.label" :value="option.value"></el-option>
        </el-select>
      </el-form-item>
    </template>
    <el-form-item>
      <el-button type="primary" @click="searchFun">查询</el-button>
    </el-form-item>
    <el-form-item :key="item.label" v-for="item in buttonList">
      <el-button type="primary" @click="item.buttonFun">{{item.label}}</el-button>
    </el-form-item>
    <slot></slot>
  </el-form>
</template>

<script>
import { formatDate } from "@js/utils";
export default {
  name: "searchModule",
  data() {
    return {
      formData: {},
      changeFlag: false
    }
  },
  props: {
    searchList: {
      type: Array,
      default: ()=>{
        return []
      }
    },
    // 默认参数值
    searchFormData: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    unLimit: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    formData:{
      handler(newVal, oldVal){
        this.changeFlag = true;
      },
      deep: true,
    },
    searchFormData: {
      handler(newVal, oldVal){
        this.formData = this.searchFormData;
      },
      deep: true,
    }
  },
  computed: {
    buttonList(){
      return this.searchList.filter(item=>item.type=='button')
    }
  },
  mounted() {
    // 初始化设置默认
    this.initFun();
  },
  methods:{
    initFun() {
      this.searchList.filter(item=>item.type=='date').map(item=>{
        if(item.dateType == 'date'){
          this.formData[item.valueName] = formatDate(new Date(),'yyyy-MM-dd')
        }
      })
      if( JSON.stringify(this.searchFormData) != '{}'){
        this.formData = Object.assign({},this.formData,this.searchFormData)
      }
    },
    // 选择结算日期
    changeDateFun(dates) {
      console.log('日期选择器：', dates)
    },
    // 查询
    searchFun(){
      // 一些页面不需要限制
      if(this.changeFlag || this.unLimit){
        this.changeFlag = false;
        this.$emit('searchFun', {
          formData: this.formData
        })
      }
    },
    buttonFun(){}
  }
}
</script>

<style lang="less" scoped>

</style>
