<!--2019/07/22-->
<template>
  <div class="dialog-box">
    <el-dialog
    :title="titleTxt"
    :visible.sync="dialogVisible"
    width="30%"
    :before-close="handleCancel">
        <slot>提示内容</slot>

        <div slot="footer">
          <template v-if="customFooter">
            <slot name="footer"></slot>
          </template>
          <template v-else>
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm" :disabled="confirmDisabled">确 定</el-button>
          </template>
        </div>
    </el-dialog>
  </div>
</template>

<script>

  export default {
    name:"dialog-box",
    props: ['titleTxt','dialogVisible', 'confirmDisabled', 'customFooter'],
    components: {},
    data () {
      return {
          // dialogVisible: true
      };
    },

    beforeCreate() {},

    created() {},

    beforeMount() {},

    mounted() {},

    beforeUpdate() {},

    updated() {},

    activated() {},

    deactivated() {},

    beforeDestroy() {},

    destroyed() {},

    errorCaptured() {},

    methods: {
      // 确认
      handleConfirm(){
        this.$emit('confirm');
      },
      // 取消
      handleCancel(){
        this.$emit('cancel');
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style scoped>

</style>
