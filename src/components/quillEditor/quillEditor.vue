<template>
  <div class="quill-editor" v-loading="imageLoading">
    <!-- 图片上传组件辅助 -->
    <el-upload
      class="avatar-uploader quill-img quill-img-height-zone"
      ref="uploadRef"
      :action="uploadImgUrl"
      name="file"
      :show-file-list="false"
      :on-success="quillImgSuccess"
      :on-error="uploadError"
      :before-upload="quillImgBefore"
      accept='.jpg,.jpeg,.png,.gif'
      :headers="headerRamToken"
    ></el-upload>
    <quill-editor
      v-model="value2"
      ref="quillEditor"
      :options="editorOption"
      @blur="onEditorBlur($event)" @focus="onEditorFocus($event)"
      @change="onEditorChange($event)">
    </quill-editor>
  </div>
</template>
<script>

import {problemImageUpload} from '@api/functionIntroduction/problem';
import { quillEditor } from "vue-quill-editor"; //调用编辑器
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';
import { headerRamToken } from '@/utils/menu'

// 工具栏配置
const toolbarOptions = [
  ["bold", "italic", "underline", "strike"],       // 加粗 斜体 下划线 删除线
  ["blockquote", "code-block"],                    // 引用  代码块
  [{ list: "ordered" }, { list: "bullet" }],       // 有序、无序列表
  [{ indent: "-1" }, { indent: "+1" }],            // 缩进
  [{ size: ["small", false, "large", "huge"] }],   // 字体大小
  [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题
  [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色
  [{ align: [] }],                                 // 对齐方式
  ["clean"],                                       // 清除文本格式
  ["link", "image", "video"]                       // 链接、图片、视频
];
export default {
  components: {
    quillEditor,
  },
  data() {
    return {
      headerRamToken,
      content: '',
      editorOption: {
        theme: "snow", // or 'bubble'
        placeholder: "请输入内容",
        modules: {
          toolbar: {
            container: toolbarOptions,
            handlers: {
              image: function(value) {
                if (value) {
                  // 触发input框选择图片文件
                  document.querySelector(".quill-img input").click();
                } else {
                  this.quill.format("image", false);
                }
              }
            }
          }
        }
      },
      uploadImgUrl: problemImageUpload(),
      imageLoading: false,
      fileMaxSize: 15, // 图片最大体积
      imageUrlPrefix: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/',

      // 增加提示
      toolbarTips: [
        {Choice:'.ql-bold',title:'加粗'},
        {Choice:'.ql-italic',title:'倾斜'},
        {Choice:'.ql-underline',title:'下划线'},
        {Choice:'.ql-header',title:'段落格式'},
        {Choice:'.ql-strike',title:'删除线'},
        {Choice:'.ql-blockquote',title:'块引用'},
        {Choice:'.ql-code-block',title:'插入代码段'},
        {Choice:'.ql-size',title:'字体大小'},
        {Choice:'.ql-list[value="ordered"]',title:'编号列表'},
        {Choice:'.ql-list[value="bullet"]',title:'项目列表'},
        {Choice:'.ql-header[value="1"]',title:'h1'},
        {Choice:'.ql-header[value="2"]',title:'h2'},
        {Choice:'.ql-align',title:'对齐方式'},
        {Choice:'.ql-color',title:'字体颜色'},
        {Choice:'.ql-background',title:'背景颜色'},
        {Choice:'.ql-image',title:'图像'},
        {Choice:'.ql-video',title:'视频'},
        {Choice:'.ql-link',title:'添加链接'},
        {Choice:'.ql-formula',title:'插入公式'},
        {Choice:'.ql-clean',title:'清除格式'},
        {Choice:'.ql-indent[value="-1"]',title:'向左缩进'},
        {Choice:'.ql-indent[value="+1"]',title:'向右缩进'},
        {Choice:'.ql-header .ql-picker-label',title:'标题大小'},
        {Choice:'.ql-header .ql-picker-item[data-value="1"]',title:'标题一'},
        {Choice:'.ql-header .ql-picker-item[data-value="2"]',title:'标题二'},
        {Choice:'.ql-header .ql-picker-item[data-value="3"]',title:'标题三'},
        {Choice:'.ql-header .ql-picker-item[data-value="4"]',title:'标题四'},
        {Choice:'.ql-header .ql-picker-item[data-value="5"]',title:'标题五'},
        {Choice:'.ql-header .ql-picker-item[data-value="6"]',title:'标题六'},
        {Choice:'.ql-header .ql-picker-item:last-child',title:'标准'},
        {Choice:'.ql-size .ql-picker-item[data-value="small"]',title:'小号'},
        {Choice:'.ql-size .ql-picker-item[data-value="large"]',title:'大号'},
        {Choice:'.ql-size .ql-picker-item[data-value="huge"]',title:'超大号'},
        {Choice:'.ql-size .ql-picker-item:nth-child(2)',title:'标准'},
        {Choice:'.ql-align .ql-picker-item:first-child',title:'居左对齐'},
        {Choice:'.ql-align .ql-picker-item[data-value="center"]',title:'居中对齐'},
        {Choice:'.ql-align .ql-picker-item[data-value="right"]',title:'居右对齐'},
        {Choice:'.ql-align .ql-picker-item[data-value="justify"]',title:'两端对齐'}
      ],
    }
  },
  props: {
    value: '',
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      document.getElementsByClassName('ql-editor')[0].dataset.placeholder = ''
      for (let item of this.toolbarTips) {
        let tip = document.querySelector('.quill-editor ' + item.Choice)
        if (!tip) {
          continue
        }
        tip.setAttribute('title', item.title)
      }
    },
    onEditorReady(editor) { // 准备编辑器

    },
    onEditorBlur(){}, // 失去焦点事件
    onEditorFocus(){}, // 获得焦点事件
    onEditorChange(event){
      console.log(event.html)
      console.log(this.content, 'content')
      // this.$emit('update:value', event.html)
    }, // 内容改变事件

    // 上传图片的回调
    quillImgSuccess(res, file) {
      this.imageLoading = false;
      // res为图片服务器返回的数据
      // 获取富文本组件实例
      let quill = this.$refs.quillEditor.quill;
      // 如果上传成功
      if (res.result == 1) {
        // 获取光标所在位置
        let length = quill.getSelection().index;
        // 插入图片  res.url为服务器返回的图片地址
        quill.insertEmbed(length, "image", this.imageUrlPrefix + res.para);
        // 调整光标到最后
        quill.setSelection(length + 1);
      } else {
        this.$message.error("图片插入失败");
      }
    },
    uploadError() {
      this.imageLoading = false;
      this.$message.error("图片插入失败");
    },
    // 上传到后台前的图片验证
    quillImgBefore(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isQualified = file.size / 1024 / 1024 < this.fileMaxSize;
      if (!isJPG && !isPNG) {
        this.$message.error('上传物料图片只能是 JPG或PNG 格式!');
      }
      if (!isQualified) {
        this.$message.error(`上传头像图片大小不能超过 ${this.fileMaxSize}MB!`);
      }
      if ((isJPG || isPNG) && isQualified) {
        this.imageLoading = true;
        return true;
      }
    }
  },
  computed: {
    editor() {
      return this.$refs.quillEditor.quill;
    },
    value2:{
      get:function(){
        return this.value
      },
      set:function(val){
         this.$emit('input',val);
      }

    }
  },

}
</script>
<style scoped>


  p {
    margin: 10px;
  }
  .edit_container,
  .quill-editor {
    height: 400px;
  }

  .quill-editor .ql-snow .ql-picker.ql-size .ql-picker-label::before,
  .quill-editor .ql-snow .ql-picker.ql-size .ql-picker-item::before {
    content: "14px";
  }

  .quill-editor .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
  .quill-editor .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
    content: "10px";
  }
  .quill-editor .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
  .quill-editor .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
    content: "18px";
  }
  .quill-editor .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
  .quill-editor .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
    content: "32px";
  }

  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-label::before,
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-item::before {
    content: "文本";
  }
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
    content: "标题1";
  }
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
    content: "标题2";
  }
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
    content: "标题3";
  }
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
    content: "标题4";
  }
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
    content: "标题5";
  }
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
  .quill-editor .ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
    content: "标题6";
  }

  .quill-editor .ql-snow .ql-picker.ql-font .ql-picker-label::before,
  .quill-editor .ql-snow .ql-picker.ql-font .ql-picker-item::before {
    content: "标准字体";
  }
  .quill-editor .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
  .quill-editor .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
    content: "衬线字体";
  }
  .quill-editor .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,
  .quill-editor .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
    content: "等宽字体";
  }
  .ql-formats {
    line-height: 1;
  }
  .quill-editor .ql-snow .ql-picker.ql-size, .quill-editor .ql-snow .ql-picker.ql-header {
    width: 70px;
  }
  .quill-img-height-zone {
    height: 0;
  }
  .quill-editor em {
    font-style: italic;
  }

</style>

