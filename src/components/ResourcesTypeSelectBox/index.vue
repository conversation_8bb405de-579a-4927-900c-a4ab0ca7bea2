<!--2019/07/25-->
<template>
  <!-- <div class="select-box"> -->
    <el-select v-model="select_type" size="mini" clearable filterable placeholder="请选择" @change="selectType">
        <el-option
        v-for="item in typeList"
        :key="item.value"
        :label="item.name"
        :value="item.value">
        </el-option>
    </el-select>
  <!-- </div> -->
</template>

<script>
import { getAllSystem } from '@api/systemManage'

  export default {
    name:"select-box",
    props: [],
    components: {},
    data () {
      return {
        select_type:'',//选中的类型
        typeList: [],//获取的列表
      };
    },

    beforeCreate() {},

    created() {},

    beforeMount() {},

    mounted() {
      this.type_list();
    },

    beforeUpdate() {},

    updated() {},

    activated() {},

    deactivated() {},

    beforeDestroy() {},

    destroyed() {},

    errorCaptured() {},

    methods: {
      // 获取所有资源类型列表
        type_list(){
            this.typeList = [
              {
                "name":'MENU-菜单',
                "value":"MENU"
              },
              {
                "name":'BUTTON-按钮',
                "value":"BUTTON"
              },
              {
                "name":'CATALOG-目录',
                "value":"CATALOG"
              }
            ]
        },
        // 选中触发请求列表
        selectType(){
          this.$emit('selectType',this.select_type,'ResourcesTypeSelected');//资源管理-筛选资源类型
        },
        // 设置默认选中的类型
        setID(type){
          this.select_type = type
        }
    },

    computed: {},

    watch: {}

  }

</script>
<style scoped>

</style>