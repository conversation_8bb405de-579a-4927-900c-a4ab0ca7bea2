<template lang="html">
  <el-dialog
    :visible="visible"
    :width="width"
    @close="cancelFn"
    class="detail-modal"
  >
    <div slot="title" class="title">
      {{ title }}
    </div>
    <slot name="detail-container"></slot>
    <div slot="footer">
      <div class="footer" v-if="hasFooter">
        <el-button @click="cancelFn">
          {{cancelBtn}}
        </el-button>
        <el-button type="primary" @click="confirmFn">
          {{confirmBtn}}
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    name: "DetailModal856",
    props: {
      title: {
        type: String,
        default: '温馨提示'
      },
      width: {
        type: String,
        default: '30%'
      },
      visible: {
        type: Boolean,
        default: true
      },
      hasFooter: {
        type: Boolean,
        default: true
      },
      cancelBtn: {
        type: String,
        default: '取消'
      },
      confirmBtn: {
        type: String,
        default: '保存'
      },
      cancelFn: {
        type: Function,
        default: () => {
        }
      },
      confirmFn: {
        type: Function,
        default: () => {
        }
      },
    }
  };
</script>

<style lang="less" scoped>
  .detail-modal {

    .el-dialog__body {
      padding: 0 60px;
      margin-top: 50px;
    }

    .title {
      line-height: 30px;
      font-size: 16px;
    }

    .el-dialog__footer {
      text-align: center;

      .el-button {
        min-width: 100px;
      }
    }

    .body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
    }
  }
</style>
