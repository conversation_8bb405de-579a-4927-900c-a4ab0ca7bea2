<template>
  <div class="UploadFile">
    <el-upload
      :class="{'uploadDisabled': uploadDisabled}"
      :action="uploadApi"
      ref="upload"
      with-credentials
      :show-file-list="showFileList"
      :accept="accept"
      :limit="limit"
      :multiple="limit > 1"
      :on-success="handleSuccess"
      :file-list="fileList"
      :headers="getHeaders"
      :on-exceed="handleExceed"
      :on-remove="handleRemove"
      :before-upload="beforeUpload"
      :on-change="onChange"
      :auto-upload="autoUpload"
      v-bind="props"
    >
      <div class="upload-box" slot="trigger">
         <slot>
           <el-button size="small" type="primary">选取文件</el-button>
         </slot>
      </div>
    </el-upload>
  </div>
</template>

<script>
import config from '@/assets/js/config'
import { headerRamToken } from '@/utils/menu'

const prefix = config.aliyunImgBaseUrl
export default {
  name: 'UploadFile',
  props: {
    file: {
      type: [ String, Array ],
      default: ''
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: 1
    },
    maxSize: { // 传入kb
      type: Number,
      default: 0
    },
    uploadApi: {
      type: String,
      default: ''
    },
    props: {
      type: Object,
      default: () => ({})
    },
    accept: {
      type: String,
      default: '*/*'
    },
    autoUpload: {
      type: Boolean,
      default: false
    },
    fileName: {
      type: String,
      default: ''
    },
    fileDir: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      temp: [],
      fileList: []
    }
  },
  model: {
    prop: 'file',
    event: 'update'
  },
  computed: {
    uploadDisabled: function () {
      return this.temp.length >= this.limit
    },
    getHeaders: function() {
      const headers = this.headers || {}
      return {
        ...headerRamToken,
        ...headers,
      }
    }
  },
  created () {
    this.init()
  },
  watch: {
    file: function () {
      // console.log('file', this.file)
      if (this.limit === 1) {
        this.init()
      } else {
        if (this.file.length === 0) {
          this.init()
        }
      }
    }
  },
  methods: {
    init: function () {
      // console.log('created', this.file)
      if (this.file) {
        if (this.limit > 1) {
          this.fileList = [ ...this.file.map(url => {
            return {
              name: this.fileName || '',
              para: this.fileDir + url,
              url: prefix + this.fileDir + url
            }
          }) ]
        } else {
          this.fileList = this.file ? [ {
            name: this.fileName || '',
            para: this.file,
            url: prefix + this.fileDir + this.file
          } ] : []
        }
        this.temp = [ ...this.fileList ]
      } else {
        this.temp = []
        this.fileList = []
      }
    },
    handleUpdateFile: function () {
      return this.limit > 1 ? [ ...this.temp.map(item => item.para) ] : (this.temp.length === 0 ? '' : this.temp[0].para)
    },
    beforeUpload: function (file) {
      if (this.maxSize > 0 && file.size > this.maxSize * 1000) {
        this.$message.warning(`上传文件限制${this.maxSize}kb, 请合理压缩文件大小再上传`)
        return false
      }
      return true
    },
    handleSuccess: function (res, file, file_list) {
      if (res && res.result === 1) {
        if (this.limit > 1) {
          this.temp.push({
            name: res.para,
            para: this.fileDir +res.para,
            url: prefix + this.fileDir + res.para
          })
        } else {
          this.temp = [ {
            name: res.para,
            para: this.fileDir + res.para,
            url: prefix + this.fileDir + res.para
          } ]
        }
        this.$emit('update', this.handleUpdateFile(), file)
      } else {
        this.handleError(res, file, file_list)
      }
    },
    handleError: function (err, file, file_list) {
      this.fileList = [ ...this.fileList ]
      this.temp = [ ...this.fileList ]
      this.$message.warning(err.data || '上传失败，请刷新重试')
      this.$emit('update', this.handleUpdateFile())
    },
    handleRemove: function (file, file_list) {
      let _files = file_list.map(item => {
        return {
          name: '',
          para: item.response ? item.response.para : item.url,
          url: item.response ? (prefix + this.fileDir + item.response.para) : item.url
        }
      })
      this.fileList = [ ..._files ]
      this.temp = [ ...this.fileList ]
      this.$emit('update', this.handleUpdateFile())
    },
    handleExceed: function (file, file_list) {
      this.$message.warning('超出可上传数量')
    },
    onChange: function (file, fileList) {
      // console.log('change', fileList)
    },
    upload: function () {
      if (!this.autoUpload && this.temp.length) {
        this.$refs.upload.submit()
      }
    }
  },
  beforeDestroy () {
    this.fileList = []
    this.temp = []
    this.$refs.upload.clearFiles()
    this.$emit('update', this.handleUpdateFile())
  }
}
</script>

<style scoped lang="less">
  .uploadDisabled{
    /deep/ .el-upload{
      display: none !important;
    }
    /deep/ .el-upload-list__item:first-child {
      margin-top: 5px;
    }
  }
</style>
