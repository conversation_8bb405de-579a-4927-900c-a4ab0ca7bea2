<!--2019/07/25-->
<template>
  <!-- <div class="select-box"> -->
    <!-- <el-select v-model="select_id" size="mini" :disabled="!isDisabled" clearable filterable placeholder="请选择" @change="selectID"> -->
    <el-select v-model="select_id" size="mini" clearable filterable placeholder="请选择" @change="selectID">
        <el-option
          v-for="(item,index) in (listData)"
          :key="index"
          :label="item.adResourcesId+'->'+item.name"
          :value="item.adResourcesId">
        </el-option>
    </el-select>
  <!-- </div> -->
</template>

<script>
import { getCatalogAndMenu } from '@api/resourcesManage'

  export default {
    name:"select-box",
    props: [],
    components: {},
    data () {
      return {
        select_id:'',//选中的id
        listData: [],//获取的列表
        // isDisabled:false,//父节点ID，需要先选系统id才能选父节点id
        systemId:'',//
      };
    },

    beforeCreate() {},

    created() {},

    beforeMount() {},

    mounted() {
      // this.getList();
    },

    beforeUpdate() {},

    updated() {},

    activated() {},

    deactivated() {},

    beforeDestroy() {},

    destroyed() {},

    errorCaptured() {},

    methods: {
      // 获取所有列表
        getList(){
          if (this.systemId === '' || typeof this.systemId === 'undefined' || this.systemId === null) {
            this.listData = [];
          } else {
            let data = {
              systemId:this.systemId
            }
            getCatalogAndMenu(data).then(res=> {
              if (res.result == 0) {
                // this.listData = res.data.filter((item)=>{
                //   return item.type == 'CATALOG'
                // });
                // 接口返回的资源类型是目录和菜单
                this.listData = res.data;
              }
            })
          }
        },
        // 选中触发请求列表
        selectID(){
          this.$emit('selectID',this.select_id,'ParentIdSlected');
        },
        // 设置默认选中的系统id
        setID(selectId){
          this.select_id = selectId
        },
        // 设置状态
        setStatus(adSystemId){
            this.select_id = '';//每次重选系统ID后，要重置本组件的父级ID
            this.systemId = adSystemId;
            this.getList();
        }
    },

    computed: {},

    watch: {}

  }

</script>
<style scoped>

</style>
