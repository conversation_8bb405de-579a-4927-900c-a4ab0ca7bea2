<template>
  <div class="render">
    <div id="render" v-if="elements&&elements.length"></div>
    <RenderTemplate v-if="elements&&elements.length" :pageConfig="elements" :jsobject="jsobject" :dataSetList="dataSetList"></RenderTemplate>
  </div>
</template>

<script>
import router from '@/router'
import axiosRequest from '@/utils/request'
import {getPageAuthCode} from '@/api/render-config.js';
export default {
  name: 'page-render',
  components: {
    RenderTemplate: leyaoyaoPcRender.createRender('render', router, true),
  },
  data() {
    return {
      elements: null,
      jsobject:[],
      dataSetList:[]
    }
  },
  mounted(){
    const box=document.querySelector('.page-container')
    box.className="page-container page-render"
  },
  destroyed(){
    const box=document.querySelector('.page-container')
    box.className="page-container"
    this.elements=[]
  },
  methods: {
    // 获取页面配置 json 对应环境变量
    getPageConfigEnv() {
      const hostName = location.hostname;
      if (/dsp.leyaoyao.com/.test(hostName)) {
        return 'DEV'
      } else if (/ssp.leyaoyao.com/.test(hostName)) {
        return 'SIT'
      } else if (/usp.leyaoyao.com/.test(hostName)) {
        return 'UAT'
      } else if (/sp.leyaoyao.com/.test(hostName)) {
        return 'PROD'
      }
      return 'DEV'
    },
    async getPageConfig(pageId) {
      if (!pageId) return
      Promise.all([this.getConfigArr(pageId), this.getJsObject(pageId)]).then(res => {
        const [config, jsObject] = res
        this.$set(this.$data, 'elements', config || [])
        this.$set(this.$data, 'jsobject', jsObject.js || [])
        this.$set(this.$data, 'dataSetList', jsObject.datasource || [])
      })
    },
    getConfigArr(pageId) {
      return axiosRequest({
        method: 'get',
        url: `https://page-configure.oss-cn-shenzhen.aliyuncs.com/page-configure/${this.getPageConfigEnv()}/${pageId}.json?t=${Date.now()}`,
      })
      // .then((res) => {
      //   this.$set(this.$data, 'elements', res || [])
      // })
    },
    getJsObject(pageId) {
      return axiosRequest({
        method: 'get',
        url: `https://page-configure.oss-cn-shenzhen.aliyuncs.com/page-configure/${this.getPageConfigEnv()}/component/${pageId}.json?t=${Date.now()}`,
      })
      // .then((res) => {
      //   this.$set(this.$data, 'jsobject', res.js || [])
      //   this.$set(this.$data, 'dataSetList', res.datasource || [])
      // })
    },
    async getAuthCode(id){
      const meta = this.$route.meta || {}
      const res = await getPageAuthCode(
        '/gw/ram-service/permission/menu/authCode',
        {
          pageConfigureId: id,
          queryButton: meta.queryButtonAuthCode,
          queryField: meta.queryFieldAuthCode,
        },
      )
      if (res?.body) {
        try {
          let authCodeList = JSON.parse(sessionStorage.getItem('service-authCodeList'))
          authCodeList[id] = res?.body
          sessionStorage.setItem(
            'service-authCodeList',
            JSON.stringify(authCodeList),
          )
        } catch {
          sessionStorage.setItem('service-authCodeList', JSON.stringify({}))
        }
      }
      this.getPageConfig(id)
    },
    handlePageConfig () {
      const pathItems = this.$route.path.split('/')
      const id = pathItems.pop()
      const meta = this.$route.meta || {}
      if (!meta.queryButtonAuthCode && !meta.queryFieldAuthCode) {
        this.getPageConfig(id)
        return
      }
      const serviceAuthCodeList = sessionStorage.getItem('service-authCodeList')
      if (serviceAuthCodeList) {
        let authCodeList = JSON.parse(serviceAuthCodeList)
        if (authCodeList[id]) {
          this.getPageConfig(id)
        } else {
          this.getAuthCode(id)
        }
      } else {
        sessionStorage.setItem('service-authCodeList', JSON.stringify({}))
        this.getAuthCode(id)
      }
    }
  },
  watch: {
    '$route.path': {
      immediate: true,
      handler(newValue) {
        this.$set(this.$data, 'elements', null)
        const parts = newValue.split('/')
        const reg = /^\d+$/
        if (reg.test(parts[1])) {
          this.handlePageConfig()
          // this.getPageConfig(parts[1])
        }
      },
    },
  },
}
</script>
<style scoped lang="less">
.render /deep/ .lyy-search-pro .ant-card-body{
  padding:16px 16px 0
}
.render /deep/ .content-container>.lyy-tabs{
  background: #f0f0f0;
}
.render /deep/ .lyy-tabs .ant-tabs-nav-wrap{
  padding:0;
}
.render /deep/ .lyy-search-pro .button-list{
  margin-bottom: 16px;
}
</style>
