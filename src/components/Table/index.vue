<!--
 * @Description: table
 * @Author: zhaorubo
 * @Email: <EMAIL>
 * @Date: 2022-02-21 11:27:51
 * @LastEditTime: 2022-05-07 10:17:05
 * @LastEditors: zhaorubo
-->
<template>
  <div :class="['lyy-table', { pb60: pagination }]" ref="lyyTable">
    <el-table
      ref="table"
      :stripe="stripe"
      :data="data"
      style="width: 100%"
      :sum-text="sumText"
      :show-summary="showSummary"
      :summary-method="getSummaries"
      header-cell-class-name="headerCell"
      v-bind="$attrs"
      header-align="center"
      :border="showBorder"
      :class="{ showBorder: showBorder }"
      v-on="$listeners"
      :max-height="maxHeight"

    >
      <template #empty>
        <div>
          <el-empty :image-size="200"></el-empty>
        </div>
      </template>
      <template v-for="(item, index) in column">
        <el-table-column
          v-if="item.type === 'index'"
          align="center"
          type="index"
          :index="
            (index) => {
              return index + 1 < 10 ? `0${index + 1}` : index + 1;
            }
          "
          :key="index"
          :fixed="item.fixed"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
        >
        </el-table-column>
        <el-table-column
          v-else-if="item.type === 'date'"
          align="center"
          :key="index"
          :fixed="item.fixed"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
        >
          <template slot-scope="scope">
            <span>{{ scope.row[item.prop] | formatDate }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.type === 'custom'"
          align="center"
          :key="index"
          :fixed="item.fixed"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
        >
          <!--eslint-disable-next-line vue/no-unused-vars-->
          <template slot-scope="{ row }">
            <slot :name="item.prop" :row="row" />
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :key="index"
          align="center"
          :fixed="item.fixed"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :formatter="item.formatter"
        ></el-table-column>
      </template>
    </el-table>
    <el-pagination
      background
      v-if="pagination"
      :current-page="pagination.currentPage"
      :page-sizes="pagination.pageSizes ? pagination.pageSizes : [10, 50, 100, 200]"
      :page-size="pagination.pageSize"
      :layout="pagination.layout ? pagination.layout : 'total, sizes, prev, pager, next, jumper'"
      :total="pagination.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>

<script>
import { formatDate } from "@/utils/utils";

export default {
  name: "LyyTable",
  filters: {
    formatDate(val) {
      // console.log("=============", formatDate(val, "yyyy-MM-dd HH:mm:ss"));
      return formatDate(val, "yyyy-MM-dd hh:mm:ss");
    },
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
    column: {
      type: Array,
      default: () => {
        return [];
      },
    },
    showSummary: {
      // 显示合计
      type: Boolean,
      default: false,
    },
    sumText: {
      // 显示合计
      type: String,
      default: "合计",
    },
    stripe: {
      // 显示合计
      type: Boolean,
      default: false,
    },
    // {
    //       currentPage: 1,
    //       pageSizes: [10, 50, 100, 200],
    //       pageSize: 10,
    //       layout: 'total, sizes, prev, pager, next, jumper',
    //       total: 400,
    //     }
    pagination: {
      type: Object,
      default: () => {
        return null;
      },
    },
    showBorder: {
      type: Boolean,
      default: true,
    },
  },
  data(){
    return{
      maxHeight:300
    }
  },
  methods: {
    calcMaxHeight(){
      const defaultTableTop = 247
      const minHeight = 300
      const tableTop = this.$refs?.lyyTable?.getBoundingClientRect()?.top || defaultTableTop
      const clientH = document.body.clientHeight || document.body.offsetHeight
      this.maxHeight = Math.max( clientH - (tableTop + 150), minHeight) //注意这里是要number类型，否则无效
    },
    getSummaries(param) {
      const sums = [];
      const { data } = param;
      this.column.forEach((columnItem, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        } else if (columnItem.showSummary) {
          const values = data.map((item) => Number(item[columnItem.prop]));
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] += columnItem.summaryUnit ?? "";
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`)
      this.$emit("sizeChange", val);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`)
      this.$emit("currentChange", val);
    },
  },
  mounted(){
    this.$nextTick(()=>{
      this.calcMaxHeight()
    })
    window.addEventListener('resize',this.calcMaxHeight)
    this.$once('hook:beforeDestroy', () => window.removeEventListener('resize',this.calcMaxHeight))
  },
};
</script>

<style lang="less">
.lyy-table {
  position: relative;
  .el-table {
    margin-bottom: 20px;
    // overflow: auto;
  }
}
.pb60 {
  // padding-bottom: 60px;
}
</style>
