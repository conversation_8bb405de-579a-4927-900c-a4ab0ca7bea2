<!-- 编辑轮播图 -->
<template>
  <div class="page">
    <div class="type-change clear">
      <div class="height-box">
        <span class="left tit">
          时间样式
          <span class="value">样式{{style.styleType}}</span>
        </span>
        <div class="right">
          <el-radio-group class="left" v-model="style.styleType">
            <el-radio-button :label="item" v-for="item in 1" :key="item">样式{{item}}</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="color-change clear pd-t-0">
      <span class="tit left">背景颜色<span class="value">{{style.bgC||'请选择'}}</span></span>
      <div class="right lin1">
        <el-color-picker v-model="style.bgC"></el-color-picker>
        <span class="reset" @click="style.bgC='#333'">重置</span>
      </div>
    </div>
    <div class="slider-box clear">
      <span class="group-tit left">页面边距</span>
      <div class="group-slider">
        <el-slider v-model="style.paddingHorizontal" :min="0" :max="30"></el-slider>
      </div>
      <el-input class="num-input right" v-model="style.paddingHorizontal" />
    </div>
    <div class="slider-box clear">
      <span class="group-tit left">上下空隙</span>
      <div class="group-slider">
        <el-slider v-model="style.marginheight" :min="0" :max="30"></el-slider>
      </div>
      <el-input class="num-input right" v-model="style.marginheight" />
    </div>
  </div>
</template>
<script>
import Vue from "vue";
import { minxin } from "./mixins";
import Upload from "@/components/saasCom/Upload";
import draggable from "vuedraggable";
export default {
  name: "editBanner",
  mixins: [minxin],
  data() {
    return {
      height: "280px",
      style: {
				// 1 样式1
				styleType: "1",
				bgC: '#fff',
				// 左右边距
				paddingHorizontal: 0,
				// 上下边距
				marginheight: 0,
			},
    };
  },
  created() {
    if (this.pStyle) {
      this.imgList = this.pStyle.imgList;
    }
  },

  mounted() {},

  methods: {
    
    
  },

  components: {
    Upload,
    draggable
  },
  watch: {
    pStyle: {
      handler(newValue, oldValue) {
        if (newValue) {
          this.style = newValue;
        } else {
          this.style = {
            styleType: "1",
            bgC: '#fff',
            // 左右边距
            paddingHorizontal: 0,
            // 上下边距
            marginheight: 0,
          };
        }
      },
      deep: true
    }
  }
};
</script>
<style scoped lang='less'>
@import url("../css/editCom.less");
</style>