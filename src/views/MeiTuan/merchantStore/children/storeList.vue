<template>
  <div class="storeList-wrapper">
    <div class="margin-bottom-10">
      <el-button type="primary" @click="serchChannelFun">新增商家门店列表</el-button>
    </div>
    <el-table
      :data="tableData"
      border
      style="width: 100%"
      height="600"
      v-loading.lock="LoadingFlag"
    >
      <el-table-column
        prop="created"
        label="创建时间"
      >
      </el-table-column>
      <el-table-column
        prop="created"
        label="页面编号"
      >
      </el-table-column>
      <el-table-column
        prop="created"
        label="页面标题"
      >
      </el-table-column>
      <el-table-column
        prop="created"
        label="关联商家信息"
      >
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button @click="goToeditChannelFun(scope.row)" type="text" size="small">编辑</el-button>
          <el-button @click="changeChannelFun(scope.row)" :class="scope.row.isActive==='Y'?'status-error':'status-success'" type="text" size="small">
            {{scope.row.isActive==='Y'?'禁用':'启用'}}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    name: "storeList",
    data(){
      return{
        LoadingFlag: false,
      }
    }
  }
</script>

<style scoped>

</style>
