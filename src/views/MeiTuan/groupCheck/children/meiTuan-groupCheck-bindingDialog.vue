<template>
  <el-dialog
    title="绑定店铺状态"
    class="meiTuan-groupCheck-bindingDialog"
    :visible.sync="dialogVisible"
    width="500px"
    v-loading="dialogLoadingFlag"
    @closed="resetForm()"
  >
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" class="demo-ruleForm">
      <el-form-item label="美团商家ID：" prop="meituanBId">
        <el-input
          v-model="ruleForm.meituanBId"
          oninput="value=value.replace(/[\W]/g,'')"
          placeholder="输入美团商家标识"
          clearable/>
      </el-form-item>
      <el-form-item label="美团店铺ID：" prop="meituanStoreId">
        <el-input
          v-model="ruleForm.meituanStoreId"
          oninput="value=value.replace(/[\W]/g,'')"
          placeholder="输入美团店铺ID"
          clearable/>
      </el-form-item>

      <el-form-item label="美团店铺opPoiId：" prop="opPoiId">
        <el-input
          v-model="ruleForm.opPoiId"
          oninput="value=value.replace(/[\W]/g,'')"
          placeholder="输入美团店铺opPoiId"
          clearable/>
      </el-form-item>
      <el-form-item required label="店铺来源：" prop="channel">
        <el-select v-model="ruleForm.channel">
          <el-option label="乐摇摇渠道" value="0"></el-option>
          <el-option label="美团渠道" value="2"></el-option>
          <el-option label="其他渠道" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item required  v-show="!ruleForm.changeMeituanPhone" label="美团手机号：">
        <el-input
          maxlength="11"
          v-model="ruleForm.meituanPhone"
          placeholder="输入美团手机号"
          @focus="meituanPhoneFocus"
          clearable/>
      </el-form-item>
      <el-form-item v-show="ruleForm.changeMeituanPhone" label="美团手机号：" prop="meituanPhone">
        <el-input
          maxlength="11"
          v-model="ruleForm.meituanPhone"
          placeholder="输入美团手机号"
          clearable/>
      </el-form-item>
      <el-form-item>
        <div class="btn-wrapper">
          <el-button @click="closeFn()">取消</el-button>
          <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { postMeiTuanStoreBind } from '@/api/meiTuan'

export default {
  name: 'MeiTuanGroupCheckBindingDialog',
  data() {
    var checkPhone = (rule, value, callback) => {
      if (!this.ruleForm.changeMeituanPhone) {
        callback();
      } else {
        if (!/^1[3456789]\d{9}$/.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        } else {
          callback();
        }
      }
    }
    return {
      dialogVisible: false,
      dialogLoadingFlag: false,
      rowData: null,
      ruleForm: {
        meituanBId: null, // 美团商家id
        meituanStoreId: null, // 美团店铺id
        opPoiId: null, // 美团店铺 opPoiId
        channel: '0', //店铺来源  0 -- 乐摇摇渠道，2 -- 美团渠道， 1 -- 其他渠道
        meituanPhone: null, // 美团手机号
        meituanStoreApplyId: null, // 乐摇摇美团门店申请id
        changeMeituanPhone: false, // 是否更换绑定手机
      },
      rules: {
        meituanBId: [
          { required: false, message: '输入美团商家标识', trigger: 'change' },
          { min: 4, max: 40, message: '长度在 4 到 40 个字符', trigger: 'change' }
        ],
        meituanStoreId: [
          { required: false, message: '输入美团店铺ID', trigger: 'change' },
          { min: 4, max: 40, message: '长度在 4 到 40 个字符', trigger: 'change' }
        ],
        meituanPhone: [
          { required: true, message: '请输入手机号码', trigger: 'change', validator: checkPhone },
          // { pattern:/^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'change' }
        ],
      }
    }
  },
  methods: {
    openFn(row) {
      this.rowData = row
      this.ruleForm = {
        meituanBId: row.meituanBId,
        meituanStoreId: row.meituanStoreId,
        opPoiId: row.opPoiId,
        channel: row.channel,
        meituanPhone: row.phoneNumber,
        meituanStoreApplyId: row.meituanStoreApplyId,
        changeMeituanPhone: row.changeMeituanPhone
      }
      this.dialogVisible = true
    },
    resetForm() {
      this.$refs['ruleForm'].resetFields();
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            this.dialogLoadingFlag = true
            const res = await postMeiTuanStoreBind(this.ruleForm)
            if (res.result === 0) {
              this.$message.success("绑定成功！")
              this.$emit('updateDateList')
              this.closeFn()
            }
          } catch(e) {
            console.log('提交错误', e)
          } finally {
            this.dialogLoadingFlag = false;
          }
        } else {
          return false;
        }
      });
    },
    meituanPhoneFocus() {
      if (this.ruleForm.changeMeituanPhone) {
        return
      }
      this.ruleForm.changeMeituanPhone = true
      this.ruleForm.meituanPhone = null
    },
    closeFn() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
  .btn-wrapper {
    text-align: right;
  }
</style>
