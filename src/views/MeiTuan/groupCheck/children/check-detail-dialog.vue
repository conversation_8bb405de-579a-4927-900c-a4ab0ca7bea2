<template>
  <!-- 弹窗审核内容 -->
  <el-dialog title="团单详情" :visible.sync="visible">
    <!-- 未审核 审核不通过 -->
     <el-form v-if="checkItemInfo.approveStatus !== 1">
       <el-row :gutter="30">
         <el-col :span="6">
           <p class="item-title">乐摇摇主账号：</p>
           <p>{{checkItemInfo.phoneNumber}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">美团店铺名称：</p>
           <p>{{checkItemInfo.storeName}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">投放地址ID：</p>
           <p>{{checkItemInfo.lyyEquipmentGroupId}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">美团店铺ID：</p>
           <p>{{checkItemInfo.meituanStoreId}}</p>
         </el-col>
       </el-row>
       <el-row :gutter="30">
         <el-col :span="6">
           <p class="item-title">工单号：</p>
           <p>{{checkItemInfo.meituanPackageApplyId}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">所在区域：</p>
           <p>{{checkItemInfo.districtInfo}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">详细地址：</p>
           <p>{{checkItemInfo.address}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">投放地址名称：</p>
           <p>{{checkItemInfo.groupName}}</p>
         </el-col>
       </el-row>
       <el-row :gutter="30">
         <el-col :span="6">
           <p class="item-title">美团团购套餐信息：</p>
           <p>美团价格：{{checkItemInfo.meituanPrice}}元</p>
           <p>商品原价：{{checkItemInfo.marketPrice}}元</p>
           <p>兑币数量：{{checkItemInfo.coins}}币</p>
           <p>玩家购买上限数量：{{checkItemInfo.saleUpperLimit !=0 ?checkItemInfo.saleUpperLimit:'无上限'}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">团购套餐审核状态：</p>
           <p class="status-warning" v-if="checkItemInfo.approveStatus=== 0">未审核</p>
           <p class="status-error" v-else>审核未通过</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">商家操作类型：</p>
           <p class="status-success" v-if="checkItemInfo.packageType === 1">上架</p>
           <p class="status-error" v-else>下架</p>
         </el-col>
         <el-col :span="6" v-if="checkItemInfo.qrcode">
           <p class="item-title">二维码：</p>
           <p>
             <el-image
               style="width: 100px; height: 100px"
               :src="checkItemInfo.qrcode"
               fit="contain"></el-image>
           </p>
         </el-col>
       </el-row>
     </el-form>

     <!-- 审核已通过 -->
     <el-form v-else>
       <el-row :gutter="30">
         <el-col :span="6">
           <p class="item-title">乐摇摇账号：</p>
           <p>{{checkItemInfo.merchantAccount}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">美团手机号：</p>
           <p>{{checkItemInfo.phoneNumber}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">美团商家ID：</p>
           <p>{{checkItemInfo.meituanBId}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">美团店铺名称：</p>
           <p>{{checkItemInfo.storeName}}</p>
         </el-col>
       </el-row>
       <el-row :gutter="30">
         <el-col :span="6">
           <p class="item-title">美团店铺ID：</p>
           <p>{{checkItemInfo.meituanStoreId}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">所在区域：</p>
           <p>{{checkItemInfo.districtInfo}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">详情地址：</p>
           <p>{{checkItemInfo.address}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">投放地址名称：</p>
           <p>{{checkItemInfo.groupName}}</p>
         </el-col>
       </el-row>
       <el-row :gutter="30">
        <el-col :span="6">
           <p class="item-title">投放地址ID：</p>
           <p>{{checkItemInfo.lyyEquipmentGroupId}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">美团团购套餐：</p>
           <p>商品原价：{{checkItemInfo.marketPrice}}元</p>
           <p>美团价格：{{checkItemInfo.meituanPrice}}元</p>
           <p>兑币数量：{{checkItemInfo.coins}}币</p>
           <p>玩家购买上限数量：{{checkItemInfo.saleUpperLimit !=0 ?checkItemInfo.saleUpperLimit:'无上限'}}</p>
         </el-col>
         <el-col :span="6">
           <p class="item-title">团购套餐审核状态：</p>
           <p class="status-warning" v-if="checkItemInfo.approveStatus=== 0">未审核</p>
           <p class="status-success" v-else-if="checkItemInfo.approveStatus=== 1">审核通过</p>
           <p class="status-error" v-else>审核未通过</p>
         </el-col>
         <el-col :span="6" v-if="checkItemInfo.qrcode">
           <p class="item-title">二维码：</p>
           <p>
             <el-image
               style="width: 100px; height: 100px"
               :src="checkItemInfo.qrcode"
               fit="contain"></el-image>
           </p>
         </el-col>
       </el-row>
     </el-form>

     <div slot="footer" class="dialog-footer" v-if="checkItemInfo.approveStatus=== 0">
       <el-button type="danger" v-if="checkItemInfo.packageType === 1" @click="checkBtn(checkItemInfo.meituanPackageApplyId,2,checkItemInfo.meituanStoreApplyId,checkItemInfo.packageType)">审核不通过</el-button>
       <el-button type="primary" @click="checkBtn(checkItemInfo.meituanPackageApplyId,1,checkItemInfo.meituanStoreApplyId,checkItemInfo.packageType)">审核通过</el-button>
     </div>
   </el-dialog>
</template>

<script>
export default {
 name: 'MeiTuanCheckDetailDialog',
 props: {
   checkItemInfo: {
     type: Object,
     default: () => {}
   }
 },
 data() {
  return {
    visible: false
  }
 },
 methods: {
    openFn() {
      this.visible = true
    },
    closeFn() {
      this.visible = false
    },
    checkBtn(packageApplyId,approveStatus,applyId,packageType){
     this.$emit('checkBtn', packageApplyId,approveStatus,applyId,packageType)
   }
 }
}
</script>

<style lang=less scoped>
.el-row {
  margin-bottom: 20px;
  .item-title{
    color: #303133;
    font-weight: bold;
  }
}
</style>
