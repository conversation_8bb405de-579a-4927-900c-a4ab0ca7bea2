<!--
日期：2019/10/18
功能：模板组件
作者：杨楠锋
-->
<template>
  <div v-loading="pageInfo.loading">
    <el-form :inline="true"
             v-if="ruleInitFlag"
             label-position="center"
             :model="formData" :rules="rules" ref="ruleForm"
             label-width="100px">
      <el-form-item label="查询类型：" required>
        <el-radio-group v-model="type" @change="typeChange">
          <el-radio-button v-for="(item, index) in typeArr" :key="index" :label="item">根据{{item}}查询</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="typeIdCn + '：'"
                    prop="id"
                    v-if="typeObj[typeIdCn]">
        <el-input class="inline-input"
                  v-model.trim="formData.id"
                  :placeholder="'请输入'+typeIdCn"
                  maxlength="20"
                  clearable></el-input>
      </el-form-item>
      <el-form-item :label="typeNumberCn + '：'"
                    prop="number"
                    label-width="160px"
                    v-if="typeObj[typeNumberCn]">
        <el-input class="inline-input"
                  v-model.trim="formData.number"
                  :placeholder="'请输入'+typeNumberCn"
                  maxlength="20"
                  clearable></el-input>
      </el-form-item>
      <el-form-item :label="typePaymentNoCn + '：'"
                    prop="paymentNo"
                    v-if="typeObj[typePaymentNoCn]">
        <el-input class="inline-input"
                  v-model.trim="formData.paymentNo"
                  :placeholder="'请输入'+typePaymentNoCn"
                  maxlength="40"
                  clearable></el-input>
      </el-form-item>
      <el-form-item label="日期：" prop="date">
        <!-- <el-date-picker
          v-model="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择日期"
          @change="changeDate"
          :picker-options="pickerOptions"
          :editable="false"
        >
        </el-date-picker> -->
        <dateTimePicker v-model="formData.date"></dateTimePicker>
      </el-form-item>
      <!--按钮-->
      <el-form-item>
        <el-button type="primary"
                   icon="el-icon-search"
                   @click="queryBtn">查询
        </el-button>
        <el-button type="success"
                   icon="el-icon-download"
                   @click="exportBtn">导出excel
        </el-button>
      </el-form-item>
    </el-form>
    <!--列表 start-->
    <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;">
      <template v-for="(item, index) in colums">
        <el-table-column
          v-if="item.key === 'operation'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'paytype'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p v-if="scope.row.paytype">{{scope.row.paytype | arrGetValue(payTypeArr, 'key', 'value')}}</p>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'totalFee'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p v-if="scope.row.totalFee">{{scope.row.totalFee}}元</p>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'status'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p v-if="scope.row.status">{{scope.row.status | arrGetValue(statusArr, 'key', 'value')}}</p>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'value'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p>
              <span v-if="scope.row.type === specialType">{{scope.row.groupNumber}}号机_</span>
              <span>{{scope.row.type}}{{scope.row.value}}</span>
            </p>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        />
      </template>
    </el-table>
    <el-pagination
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      :current-page="pageInfo.pageIndex"
      background
      layout="total, prev, pager, next, sizes, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!--列表 end-->
  </div>
</template>

<script>
  import {dateFormat, queryParams} from "@js/utils";
  import {transactionRecordExportOnlineUserPayment, transactionRecordQueryOnlineUserPayment} from "@api/transactionRecord";
  import dateTimePicker from '../components/dateTimePicker'
  import { downloadFileRamToken } from '@/utils/menu'

  export default {
    name: "transactionRecordOnlineUserPayment",
    props: {},
    components: {
      dateTimePicker,
    },
    data() {
      return {
        date: [], // 日期
        formData: {
          id: '',
          number: '',
          paymentNo: '',
          date: '',
        },
        // 日期插件的时间选择范围
        pickerOptions: {},
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          loading: false,
          list: [],
        },
        // 列表每一列参数
        colums: [
          {key: 'created', label: '充值时间'},
          {key: 'lyyUserId', label: '用户ID'},
          {key: 'totalFee', label: '充值金额（元）'},
          {key: 'coins', label: '币数'},
          {key: 'paytype', label: '交易类型'},
          {key: 'status', label: '状态'},
          {key: 'description', label: '备注'},
          {key: 'name', label: '场地'},
          // {key: 'locationStr', label: '用户坐标'},
          // {key: 'address', label: '坐标地址'},
          {key: 'value', label: '设备'},
          {key: 'phone', label: '商户账号'},
          {key: 'wechatTrxid', label: '交易单号'},
          {key: 'outTradeNo', label: '商户单号'},
        ],
        rules: {},
        ruleInitFlag: false,
        dateFormat: dateFormat,
        specialType: '娃娃机',
        type: '用户ID',
        typeArr: [],
        typeIdCn: '用户ID',
        typeNumberCn: '设备编号/商户账号',
        typePaymentNoCn: '交易单号',
        typeObj: {},
        payTypeArr: [
          {key: 'Pay', value: '付款'},
          {key: 'Refund', value: '退款'},
        ],
        statusArr: [
          {key: 'SUCCESS', value: '成功'},
          {key: 'FAIL', value: '失败'},
        ],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.init();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      init() {
        this.initRadio();
        // this.initDataPicker();
        this.initRule();
      },
      initRadio() {
        this.type = this.typeIdCn;
        this.typeArr.push(this.typeIdCn);
        this.typeArr.push(this.typeNumberCn);
        this.typeArr.push(this.typePaymentNoCn);
        this.typeObj[this.typeIdCn] = true;
        this.typeObj[this.typeNumberCn] = false;
        this.typeObj[this.typePaymentNoCn] = false;
      },
      typeChange(val) {
        this.type = val;
        let obj = Object.assign({}, this.typeObj);
        for (let key in obj) {
          if (obj.hasOwnProperty(key)) {
            if (this.type === key) {
              obj[key] = true;
            } else {
              obj[key] = false;
            }
          }
        }
        this.typeObj = Object.assign({}, obj);
        this.initRule();
      },
      queryBtn() {
        this.isParamsComplate(() => {
          this.pageInfo.pageSize = 20;
          this.pageInfo.pageIndex = 1;
          this.pageInfo.total = 0;
          this.queryList();
        })
      },
      initDataPicker() {
        // 初始化date和fordate的值
        this.date = dateFormat(new Date(new Date().setHours(0, 0, 0, 0)), 'yyyy-MM-dd');
        this.changeDate(this.date);
        //初始化datepicker最大时间和跨度和选中回调和快捷选择时间
        this.pickerOptions = {
          shortcuts: [
            {
              text: '昨天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 1; // 最大天数
                const date = new Date();
                date.setTime(date.getTime() - times * days);
                picker.$emit('pick', date);
              }
            },
            {
              text: '前天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 2; // 最大天数
                const date = new Date();
                date.setTime(date.getTime() - times * days);
                picker.$emit('pick', date);
              }
            },
          ],
        }
      },
      // 选择结算日期
      changeDate(dates) {
        if (dates) {
          this.formData.date = dates;
        } else {
          this.formData.date = '';
        }
      },
      // 导出按钮
      exportBtn() {
        this.isParamsComplate(() => {
          const loading = this.$loading({
          lock: true,
          text: '导出中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
          const jsonStr = JSON.stringify(this.getParams());
          downloadFileRamToken(encodeURI(transactionRecordExportOnlineUserPayment + '?queryparam=' + jsonStr), true);
          loading.close()
        })
      },
      // 参数是否完整
      async isParamsComplate(call) {
        await this.$refs['ruleForm'].validate((isSuccess, valid) => {
          if (isSuccess) {
            if (!this.getIsAllowDate()) {
              return false
            }
            call();
            return true;
          } else {
            let sign = '';
            if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]['message'];
              this.$message.error(sign);
            }
            return false;
          }
        });
      },
      // 列表查询
      queryList() {
        this.isParamsComplate(() => {
          const loading = this.$loading({
          lock: true,
          text: '导出中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
          this.pageInfo.loading = true;
          transactionRecordQueryOnlineUserPayment(this.getListParams()).then(res => {
            this.pageInfo.loading = false;
            loading.close()
            if (res.result === 0) {
              if (res.data) {
                this.pageInfo.list = res.data.items || [];
                this.pageInfo.total = res.data.total || 0;
              }
            }
          }).catch(_=>{
            this.pageInfo.loading = false;
          }).finally(()=> {
            loading.close()
          })
        });
      },
      // 获取查询列表参数
      getListParams() {
        const params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        return Object.assign(params, this.getParams())
      },
      // 获取时间参数和经销商账号
      getParams() {
        const params = {};
        // if (this.formData.date) {
        //   params.time = this.formData.date;
        // }
        console.log(this.formData.date, 'this.formData.date')
        if (this.formData.date && this.formData.date.length > 0) {
          params.startTime = this.formData.date[0]
          params.endTime = this.formData.date[1]
        }
        if (this.typeObj[this.typeIdCn] && this.formData.id) {
          params.userId = this.formData.id;
        }
        if (this.typeObj[this.typeNumberCn] && this.formData.number) {
          params.equipmentId = this.formData.number;
        }
        if (this.typeObj[this.typePaymentNoCn] && this.formData.paymentNo) {
          params.paymentNo = this.formData.paymentNo;
        }
        return params;
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.queryList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.queryList();
      },
      initRule() {
        this.rules = {
          id: [
            {required: this.typeObj[this.typeIdCn], message: `${this.typeIdCn}不能为空`, trigger: 'blur'},
          ],
          number: [
            {required: this.typeObj[this.typeNumberCn], message: `${this.typeNumberCn}不能为空`, trigger: 'blur'},
          ],
          paymentNo: [
            {required: this.typeObj[this.typePaymentNoCn], message: `${this.typePaymentNoCn}不能为空`, trigger: 'blur'},
          ],
          date: [
            {required: true, message: '日期不能为空', trigger: 'blur'},
          ],
        };
        this.ruleInitFlag = true;
      },
      getIsAllowDate() {
        let isAllow = true
        if (this.formData.date && this.formData.date.length > 0) {
          let startTime = this.formData.date[0]
          let endTime = this.formData.date[1]
          if (new Date(endTime).getTime() - new Date(startTime).getTime() > 7 * 24 * 60 * 60 * 1000) {
            this.$message.error('最多只能查询一周的数据');
            isAllow = false
          }
        }
        return isAllow
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="css" scoped>
</style>
