<!--
日期：2019/10/18
功能：模板组件
作者：杨楠锋
-->
<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="线上充值记录" name="线上充值记录">
        <slot name="onlineUserPayment"></slot>
      </el-tab-pane>
      <el-tab-pane label="线上启动记录" name="线上启动记录">
        <slot name="onlineCoins"></slot>
      </el-tab-pane>
      <el-tab-pane label="现金充值记录" name="现金充值记录">
        <slot name="cashUserPayment"></slot>
      </el-tab-pane>
      <el-tab-pane label="线下投币记录" name="线下投币记录">
        <slot name="offlineCoins"></slot>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  export default {
    name: "transactionRecordTabs",
    props: {},
    components: {},
    data() {
      return {
        activeName: '线上充值记录',
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      handleClick(tab, event) {
        this.activeName = tab.label;
      }
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="css" scoped>
</style>
