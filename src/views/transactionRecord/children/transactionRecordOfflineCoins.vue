<!--
日期：2019/10/18
功能：模板组件
作者：杨楠锋
-->
<template>
  <div v-loading="pageInfo.loading">
    <el-form :inline="true"
             v-if="ruleInitFlag"
             label-position="center"
             :model="formData" :rules="rules" ref="ruleForm"
             label-width="100px">
      <el-form-item label="设备编号：" prop="value">
        <el-input class="inline-input"
                  v-model.trim="formData.value"
                  placeholder="请输入设备编号"
                  maxlength="20"
                  clearable></el-input>
      </el-form-item>
      <el-form-item label="日期：" prop="date">
        <dateTimePicker v-model="formData.date"></dateTimePicker>
      </el-form-item>
      <!--按钮-->
      <el-form-item>
        <el-button type="primary"
                   icon="el-icon-search"
                   @click="queryBtn">查询
        </el-button>
        <el-button type="success"
                   icon="el-icon-download"
                   @click="exportBtn">导出excel
        </el-button>
      </el-form-item>
    </el-form>
    <!--列表 start-->
    <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;">
      <template v-for="(item, index) in colums">
        <el-table-column
          v-if="item.key === 'operation'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'time'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p v-if="scope.row.time">{{scope.row.time}}</p>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'value'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p>
              <span v-if="scope.row.type === specialType">{{scope.row.groupNumber}}"号机_"</span>
              <span>{{scope.row.type}}{{scope.row.value}}</span>
            </p>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        />
      </template>
    </el-table>
    <el-pagination
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      :current-page="pageInfo.pageIndex"
      background
      layout="total, prev, pager, next, sizes, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!--列表 end-->
  </div>
</template>

<script>
  import {dateFormat, queryParams} from "@js/utils";
  import {
    transactionRecordExportOffCoinsRecord,
    transactionRecordQueryOffCoinsRecord
  } from "@api/transactionRecord";
  import dateTimePicker from '../components/dateTimePicker'
  import { downloadFileRamToken } from '@/utils/menu'

  export default {
    name: "transactionRecordOfflineCoins",
    props: {},
    components: {
      dateTimePicker,
    },
    data() {
      return {
        date: [], // 日期
        formData: {
          value: '',
          date: '',
        },
        // 日期插件的时间选择范围
        pickerOptions: {},
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          loading: false,
          list: [],
        },
        // 列表每一列参数
        colums: [
          {key: 'time', label: '投币时间'},
          {key: 'counter', label: '投币数'},
          {key: 'address', label: '场地名称'},
          {key: 'value', label: '设备'},
          {key: 'phone', label: '商户账号'},
        ],
        rules: {},
        ruleInitFlag: false,
        dateFormat: dateFormat,
        specialType: '娃娃机',
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.init();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      init() {
        // this.initDataPicker();
        this.initRule();
      },
      queryBtn() {
        this.isParamsComplate(() => {
          this.pageInfo.pageSize = 20;
          this.pageInfo.pageIndex = 1;
          this.pageInfo.total = 0;
          this.queryList();
        })
      },
      // 导出按钮
      exportBtn() {
        this.isParamsComplate(() => {
          const jsonStr = JSON.stringify(this.getParams());
          downloadFileRamToken(encodeURI(transactionRecordExportOffCoinsRecord + '?queryparam=' + jsonStr), true);
        })
      },
      // 参数是否完整
      async isParamsComplate(call) {
        await this.$refs['ruleForm'].validate((isSuccess, valid) => {
          if (isSuccess) {
            if (!this.getIsAllowDate()) {
              return false
            }
            call();
            return true;
          } else {
            let sign = '';
            if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]['message'];
              this.$message.error(sign);
            }
            return false;
          }
        });
      },
      // 列表查询
      queryList() {
        this.isParamsComplate(() => {
          this.pageInfo.loading = true;
          transactionRecordQueryOffCoinsRecord(this.getListParams()).then(res => {
            this.pageInfo.loading = false;
            if (res.result === 0) {
              if (res.data) {
                this.pageInfo.list = res.data.items || [];
                this.pageInfo.total = res.data.total || 0;
              }
            }
          }).catch(_=>{
            this.pageInfo.loading = false;
          })
        });
      },
      // 获取查询列表参数
      getListParams() {
        const params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        return Object.assign(params, this.getParams())
      },
      // 获取时间参数和经销商账号
      getParams() {
        const params = {};
        console.log(this.formData.date, 'this.formData.date')
        if (this.formData.date && this.formData.date.length > 0) {
          params.startTime = this.formData.date[0]
          params.endTime = this.formData.date[1]
        }
        if (this.formData.value) {
          params.value = this.formData.value;
        }
        return params;
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.queryList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.queryList();
      },
      initRule() {
        this.rules = {
          value: [
            {required: true, message: '设备编号不能为空', trigger: 'blur'},
          ],
          date: [
            {required: true, message: '日期不能为空', trigger: 'blur'},
          ],
        };
        this.ruleInitFlag = true;
      },
      getIsAllowDate() {
        let isAllow = true
        if (this.formData.date && this.formData.date.length > 0) {
          let startTime = this.formData.date[0]
          let endTime = this.formData.date[1]
          if (new Date(endTime).getTime() - new Date(startTime).getTime() > 7 * 24 * 60 * 60 * 1000) {
            this.$message.error('最多只能查询一周的数据');
            isAllow = false
          }
        }
        return isAllow
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="css" scoped>
</style>
