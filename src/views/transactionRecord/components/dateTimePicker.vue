<template>
  <div class="date-time-picker">
    <el-date-picker
      v-model="value"
      type="datetimerange"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      value-format="yyyy-MM-dd HH:mm:ss"
      popper-class='lyy-date-time-picker'
      :default-time="['00:00:00', '23:59:59']"
      @change="changeDate">
    </el-date-picker>
  </div>
</template>
<script>
import { dateFormat } from "@/utils/utils";
export default {
  data() {
    return {
      // 默认当天一整天
      value: [new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getUTCDate(), 0, 0), new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getUTCDate(), 23, 59, 59)],
    }
  },
  created() {
    let list = []
    list[0] = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getUTCDate()} 00:00:00`
    // list[1] = dateFormat(new Date(), 'yyyy-MM-dd HH:mm:ss')
    list[1] = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getUTCDate()} 23:59:59`
    this.$emit('input', list)
  },
  methods: {
    changeDate(val) {
      console.log(val, 'val')
      this.$emit('input', val)
      if (val) {
        let startTime = val[0]
        let endTime = val[1]
        this.$emit('startTime', startTime)
        this.$emit('endTime', endTime)
      } else {
        this.$emit('startTime', '')
        this.$emit('endTime', '')
      }
    }
  }
}
</script>
<style lang="less" scoped>
.date-time-picker {
  width: 450px;
  display: inline-block;
}
</style>
<style lang="less">
.lyy-date-time-picker {
  top: 0 !important;
}
</style>



