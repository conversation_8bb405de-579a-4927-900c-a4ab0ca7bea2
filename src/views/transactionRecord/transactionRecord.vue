<!--
日期：2019/10/9
功能：交易记录查询
作者：杨楠锋
-->
<template>
  <div>
    <el-card>
      <transaction-record-tabs>
        <div slot="onlineUserPayment">
          <transaction-record-online-user-payment/>
        </div>
        <div slot="onlineCoins">
          <transaction-record-online-coins/>
        </div>
        <div slot="cashUserPayment">
          <transaction-record-cash-user-payment/>
        </div>
        <div slot="offlineCoins">
          <transaction-record-offline-coins/>
        </div>
      </transaction-record-tabs>
    </el-card>
  </div>
</template>

<script>
  import TransactionRecordTabs from "@views/transactionRecord/children/transactionRecordTabs";
  import TransactionRecordOfflineCoins from "@views/transactionRecord/children/transactionRecordOfflineCoins";
  import TransactionRecordOnlineUserPayment from "@views/transactionRecord/children/transactionRecordOnlineUserPayment";
  import TransactionRecordCashUserPayment from "@views/transactionRecord/children/transactionRecordCashUserPayment";
  import TransactionRecordOnlineCoins from "@views/transactionRecord/children/transactionRecordOnlineCoins";

  export default {
    name: "transactionRecord",
    props: {},
    components: {
      TransactionRecordOnlineCoins,
      TransactionRecordCashUserPayment,
      TransactionRecordOnlineUserPayment, TransactionRecordOfflineCoins, TransactionRecordTabs},
    data() {
      return {};
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {},

    computed: {},

    watch: {}

  }

</script>
<style lang="css" scoped>
</style>
