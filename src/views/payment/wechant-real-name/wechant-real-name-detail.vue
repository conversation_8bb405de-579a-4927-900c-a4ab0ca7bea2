<!--
* @Description: 微信实名页面详情
* @Author:
* @Date:
* @LastEditors:
* @LastEditTime:
-->
<template>
  <div class="wechant-real-name-detail">
    <dialog-base ref="refDialogBase">
      <el-card>
        <p>状态：{{ getStatus() }}</p>
      </el-card>
      <el-card class="margin-top-10">
        <div slot="header" class="clearfix">
          <h2>商户基本信息</h2>
        </div>
        <div>
          <el-form label-position="top" :inline="true">
            <el-form-item label="商户名称">
              <div :class="copySign" @click="copyFun(info.merchant_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.merchant_name) || ''"
                  placement="top"
                  :disabled="!info.merchant_name"
                >
                  <el-input v-model="info.merchant_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="外商户号">
              <div :class="copySign" @click="copyFun(info.out_merchant_id)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.out_merchant_id) || ''"
                  placement="top"
                  :disabled="!info.out_merchant_id"
                >
                  <el-input v-model="info.out_merchant_id" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <template
              v-if="info.log_status != '3' && info.log_status != '4' && info.log_status != '5'"
            >
              <el-form-item label="经销商编号">
                <div :class="copySign" @click="copyFun(info.org_value)">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="String(info.org_value) || ''"
                    placement="top"
                    :disabled="!info.org_value"
                  >
                    <el-input v-model="info.org_value" readonly disabled></el-input>
                  </el-tooltip>
                </div>
              </el-form-item>
              <el-form-item label="提交时间">
                <div :class="copySign" @click="copyFun(info.created)">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="String(info.created) || ''"
                    placement="top"
                    :disabled="!info.created"
                  >
                    <el-input v-model="info.created" readonly disabled></el-input>
                  </el-tooltip>
                </div>
              </el-form-item>
              <el-form-item label="备注">
                <div :class="copySign" @click="copyFun(info.remark)">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="String(info.remark) || ''"
                    placement="top"
                    :disabled="!info.remark"
                  >
                    <el-input v-model="info.remark" readonly disabled></el-input>
                  </el-tooltip>
                </div>
              </el-form-item>
            </template>
            <template v-if="info.log_status == '5'">
              <el-form-item label="商户号">
                <div :class="copySign" @click="copyFun(info.merchant_num)">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="String(info.merchant_num) || ''"
                    placement="top"
                    :disabled="!info.merchant_num"
                  >
                    <el-input v-model="info.merchant_num" readonly disabled></el-input>
                  </el-tooltip>
                </div>
              </el-form-item>
            </template>
          </el-form>
        </div>
      </el-card>
      <el-card class="margin-top-10">
        <div slot="header" class="clearfix">
          <h2>商户经营品类</h2>
        </div>
        <div>
          <el-form label-position="top" :inline="true">
            <el-form-item label="商户id">
              <div :class="copySign" @click="copyFun(info.ad_org_id)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.ad_org_id) || ''"
                  placement="top"
                  :disabled="!info.ad_org_id"
                >
                  <el-input v-model="info.ad_org_id" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="商户号">
              <div :class="copySign" @click="copyFun(info.merchant_num)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.merchant_num) || ''"
                  placement="top"
                  :disabled="!info.merchant_num"
                >
                  <el-input v-model="info.merchant_num" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="margin-top-10">
        <div slot="header" class="clearfix">
          <h2>商户详情</h2>
        </div>
        <div>
          <el-form label-position="top" :inline="true">
            <el-form-item label="商户简称">
              <div :class="copySign" @click="copyFun(info.merchant_short_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.merchant_short_name) || ''"
                  placement="top"
                  :disabled="!info.merchant_short_name"
                >
                  <el-input v-model="info.merchant_short_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="行业类型">
              <div :class="copySign" @click="copyFun(info.industr_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.industr_name) || ''"
                  placement="top"
                  :disabled="!info.industr_name"
                >
                  <el-input v-model="info.industr_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="省份">
              <div :class="copySign" @click="copyFun(info.province_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.province_name) || ''"
                  placement="top"
                  :disabled="!info.province_name"
                >
                  <el-input v-model="info.province_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="城市">
              <div :class="copySign" @click="copyFun(info.city_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.city_name) || ''"
                  placement="top"
                  :disabled="!info.city_name"
                >
                  <el-input v-model="info.city_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="区县">
              <div :class="copySign" @click="copyFun(info.country_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.country_name) || ''"
                  placement="top"
                  :disabled="!info.country_name"
                >
                  <el-input v-model="info.country_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="地址">
              <div :class="copySign" @click="copyFun(info.address)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.address) || ''"
                  placement="top"
                  :disabled="!info.address"
                >
                  <el-input v-model="info.address" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="企业法人">
              <div :class="copySign" @click="copyFun(info.legal_person)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.legal_person) || ''"
                  placement="top"
                  :disabled="!info.legal_person"
                >
                  <el-input v-model="info.legal_person" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="电话">
              <div :class="copySign" @click="copyFun(info.principal_mobile)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.principal_mobile) || ''"
                  placement="top"
                  :disabled="!info.principal_mobile"
                >
                  <el-input v-model="info.principal_mobile" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="邮箱">
              <div :class="copySign" @click="copyFun(info.email)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.email) || ''"
                  placement="top"
                  :disabled="!info.email"
                >
                  <el-input v-model="info.email" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="客服电话">
              <div :class="copySign" @click="copyFun(info.customer_phone)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.customer_phone) || ''"
                  placement="top"
                  :disabled="!info.customer_phone"
                >
                  <el-input v-model="info.customer_phone" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="负责人">
              <div :class="copySign" @click="copyFun(info.principal)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.principal) || ''"
                  placement="top"
                  :disabled="!info.principal"
                >
                  <el-input v-model="info.principal" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="负责人手机号码">
              <div :class="copySign" @click="copyFun(info.principal_mobile)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.principal_mobile) || ''"
                  placement="top"
                  :disabled="!info.principal_mobile"
                >
                  <el-input v-model="info.principal_mobile" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="负责人身份证">
              <div :class="copySign" @click="copyFun(info.id_code)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.id_code) || ''"
                  placement="top"
                  :disabled="!info.id_code"
                >
                  <el-input v-model="info.id_code" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item
              :label="
                isPublicInstitution(info.merchant_type) || isOtherOrganizations(info.merchant_type)
                  ? '证书编号'
                  : '营业执照'
              "
            >
              <div :class="copySign" @click="copyFun(info.business_license)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.business_license) || ''"
                  placement="top"
                  :disabled="!info.business_license"
                >
                  <el-input v-model="info.business_license" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="机台故障联系电话">
              <div :class="copySign" @click="copyFun(info.equipment_fault_phone)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.equipment_fault_phone) || ''"
                  placement="top"
                  :disabled="!info.equipment_fault_phone"
                >
                  <el-input v-model="info.equipment_fault_phone" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="证件有效期">
              <div :class="copySign" @click="copyFun(info.id_code_expire)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.id_code_expire) || ''"
                  placement="top"
                  :disabled="!info.id_code_expire"
                >
                  <el-input v-model="info.id_code_expire" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item
              :label="
                isPublicInstitution(info.merchant_type) || isOtherOrganizations(info.merchant_type)
                  ? '登记证书有效期'
                  : '营业执照有效期'
              "
            >
              <div :class="copySign" @click="copyFun(info.license_expire)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.license_expire) || ''"
                  placement="top"
                  :disabled="!info.license_expire"
                >
                  <el-input v-model="info.license_expire" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="证件类型">
              <div :class="copySign" @click="copyFun(info.id_code_type_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.id_code_type_name) || ''"
                  placement="top"
                  :disabled="!info.id_code_type_name"
                >
                  <el-input v-model="info.id_code_type_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="商户类型">
              <div :class="copySign" @click="copyFun(info.merchant_type_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.merchant_type_name) || ''"
                  placement="top"
                  :disabled="!info.merchant_type_name"
                >
                  <el-input v-model="info.merchant_type_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item
              label="登记证书类型"
              v-if="
                isPublicInstitution(info.merchant_type) || isOtherOrganizations(info.merchant_type)
              "
            >
              <div :class="copySign" @click="copyFun(info.cert_type_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.cert_type_name) || ''"
                  placement="top"
                  :disabled="!info.cert_type_name"
                >
                  <el-input v-model="info.cert_type_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="身份证图片">
              <div class="img-outside">
                <div class="img-box">
                  <encryptionImg :imgUrl="info.localIndentityPhoto1"></encryptionImg>
                </div>
                <div class="img-box margin-left-10">
                  <encryptionImg :imgUrl="info.localIndentityPhoto2"></encryptionImg>
                </div>
              </div>
            </el-form-item>
            <template
              v-if="
                info.local_license_photo ||
                  (info.company_cert_img && isPublicInstitution(info.merchant_type))
              "
            >
              <el-form-item
                :label="
                  isPublicInstitution(info.merchant_type) ||
                  isOtherOrganizations(info.merchant_type)
                    ? '登记证书图片'
                    : '营业执照图片'
                "
              >
                <div class="img-box">
                  <encryptionImg :imgUrl="info.local_license_photo"></encryptionImg>
                </div>
              </el-form-item>
              <el-form-item
                label="单位证明函扫描件"
                v-if="info.company_cert_img && isPublicInstitution(info.merchant_type)"
              >
                <div class="img-box">
                  <encryptionImg :imgUrl="info.company_cert_img"></encryptionImg>
                </div>
              </el-form-item>
            </template>
            <el-form-item label="商户协议照图片" v-if="info.local_protocol_photo">
              <div class="img-box">
                <encryptionImg :imgUrl="info.local_protocol_photo"></encryptionImg>
              </div>
            </el-form-item>
            <el-form-item label="组织机构代码照图片" v-if="info.local_org_photo">
              <div class="img-box">
                <encryptionImg :imgUrl="info.local_org_photo"></encryptionImg>
              </div>
            </el-form-item>
            <el-form-item label="手持身份证正反面图片">
              <div class="img-outside">
                <div class="img-box">
                  <encryptionImg :imgUrl="info.id_card_face_hand_img"></encryptionImg>
                </div>
                <div class="img-box margin-left-10">
                  <encryptionImg :imgUrl="info.id_card_national_hand_img"></encryptionImg>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="门头照片和设备照片">
              <div class="img-outside">
                <div class="img-box">
                  <encryptionImg :imgUrl="info.shop_img"></encryptionImg>
                </div>
                <div class="img-box margin-left-10">
                  <encryptionImg :imgUrl="info.equipment_img"></encryptionImg>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="内景照片">
              <div class="img-box">
                <encryptionImg :imgUrl="info.inner_img"></encryptionImg>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="margin-top-10">
        <div slot="header" class="clearfix">
          <h2>联系人信息</h2>
        </div>
        <div>
          <el-form label-position="top" :inline="true">
            <el-form-item label="联系人姓名">
              <div :class="copySign" @click="copyFun(info.contact_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.contact_name) || ''"
                  placement="top"
                  :disabled="!info.contact_name"
                >
                  <el-input v-model="info.contact_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="联系人身份证号">
              <div :class="copySign" @click="copyFun(info.contact_id_code)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.contact_id_code) || ''"
                  placement="top"
                  :disabled="!info.contact_id_code"
                >
                  <el-input v-model="info.contact_id_code" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="联系人身份证有效期">
              <div :class="copySign" @click="copyFun(info.contact_id_code_expire)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.contact_id_code_expire) || ''"
                  placement="top"
                  :disabled="!info.contact_id_code_expire"
                >
                  <el-input v-model="info.contact_id_code_expire" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="联系人手机号">
              <div :class="copySign" @click="copyFun(info.contact_phone)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.contact_phone) || ''"
                  placement="top"
                  :disabled="!info.contact_phone"
                >
                  <el-input v-model="info.contact_phone" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="联系人身份证人面像面">
              <div class="img-box">
                <encryptionImg :imgUrl="info.contact_id_card_face_img"></encryptionImg>
              </div>
            </el-form-item>
            <el-form-item label="联系人身份证国徽面">
              <div class="img-box">
                <encryptionImg :imgUrl="info.contact_id_card_national_img"></encryptionImg>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="margin-top-10">
        <div slot="header" class="clearfix">
          <h2>银行账户</h2>
        </div>
        <div>
          <el-form label-position="top" :inline="true">
            <el-form-item label="银行卡号">
              <div :class="copySign" @click="copyFun(info.account_code)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.account_code) || ''"
                  placement="top"
                  :disabled="!info.account_code"
                >
                  <el-input v-model="info.account_code" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="开户银行">
              <div :class="copySign" @click="copyFun(info.parent_bank_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.parent_bank_name) || ''"
                  placement="top"
                  :disabled="!info.parent_bank_name"
                >
                  <el-input v-model="info.parent_bank_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="开户人">
              <div :class="copySign" @click="copyFun(info.account_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.account_name) || ''"
                  placement="top"
                  :disabled="!info.account_name"
                >
                  <el-input v-model="info.account_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="账户类型">
              <div :class="copySign" @click="copyFun(info.account_type_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.account_type_name) || ''"
                  placement="top"
                  :disabled="!info.account_type_name"
                >
                  <el-input v-model="info.account_type_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="联行号">
              <div :class="copySign" @click="copyFun(info.contact_line_code)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.contact_line_code) || ''"
                  placement="top"
                  :disabled="!info.contact_line_code"
                >
                  <el-input v-model="info.contact_line_code" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="开户支行名称">
              <div :class="copySign" @click="copyFun(info.bank_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.bank_name) || ''"
                  placement="top"
                  :disabled="!info.bank_name"
                >
                  <el-input v-model="info.bank_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="开户支行所在省">
              <div :class="copySign" @click="copyFun(info.bank_province_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.bank_province_name) || ''"
                  placement="top"
                  :disabled="!info.bank_province_name"
                >
                  <el-input v-model="info.bank_province_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="开户支行所在市">
              <div :class="copySign" @click="copyFun(info.bank_city_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.bank_city_name) || ''"
                  placement="top"
                  :disabled="!info.bank_city_name"
                >
                  <el-input v-model="info.bank_city_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="持卡人证件类型">
              <div :class="copySign" @click="copyFun(info.id_card_type_name)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.id_card_type_name) || ''"
                  placement="top"
                  :disabled="!info.id_card_type_name"
                >
                  <el-input v-model="info.id_card_type_name" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="持卡人证件号码">
              <div :class="copySign" @click="copyFun(info.id_card)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.id_card) || ''"
                  placement="top"
                  :disabled="!info.id_card"
                >
                  <el-input v-model="info.id_card" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="持卡人地址">
              <div :class="copySign" @click="copyFun(info.bank_address)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.bank_address) || ''"
                  placement="top"
                  :disabled="!info.bank_address"
                >
                  <el-input v-model="info.bank_address" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="手机号码">
              <div :class="copySign" @click="copyFun(info.bank_tel)">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="String(info.bank_tel) || ''"
                  placement="top"
                  :disabled="!info.bank_tel"
                >
                  <el-input v-model="info.bank_tel" readonly disabled></el-input>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="银行卡图片">
              <div class="img-box">
                <encryptionImg :imgUrl="info.local_account_photo"></encryptionImg>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <el-card class="margin-top-10">
        <div class="btn-wrapper">
          <el-button type="default" @click="dialogClose">关闭</el-button>
        </div>
      </el-card>
    </dialog-base>
  </div>
</template>

<script>
import DialogBase from "@components/tableBase/dialogBase";
import { copyFun, lodash, normalString } from "@/components/tableBase/utilsTableBase";
import {
  getMerchantTypeByValue,
  idCardTypeName,
  getCertTypeByValue
} from "@/views/payment/paymentCommon.js";
import encryptionImg from "@/views/payment/applicationData/encryptionImg.vue";
import { getMerchantThirdByOrgAndType } from "@/api/payment/payment.js";
export default {
  name: "WechantRealNameDetail",

  components: { DialogBase, encryptionImg },

  mixins: [],

  props: {},

  data() {
    return {
      // 弹窗组件实例
      refDialogBase: null,
      // 商户信息
      info: {},
      // 复制功能dom标记
      copySign: "copy-sign",
      // 资料ID
      lyySwiftpassMerchantId: null,
      lodash: lodash
    };
  },

  computed: {},

  watch: {},

  beforeCreate() {},

  created() {},

  beforeMount() {},

  mounted() {
    // 初始化弹窗
    this.refDialogBase = this.$refs.refDialogBase;
    this.refDialogBase.setTitleFun("特约商户审批");
    this.refDialogBase.setWidthFun("70%");
    this.refDialogBase.highDefaultFooterFun();
  },

  beforeUpdate() {},

  updated() {},

  activated() {},

  deactivated() {},

  beforeDestroy() {},

  destroyed() {},

  errorCaptured() {},

  methods: {
    // 打开弹窗
    async dialogOpen(lyySwiftpassMerchantId) {
      this.info = {};
      this.lyySwiftpassMerchantId = null;
      if (lodash.isNumber(lyySwiftpassMerchantId)) {
        this.lyySwiftpassMerchantId = lyySwiftpassMerchantId;
      }
      this.refDialogBase.setLoading(true);
      const res = await getMerchantThirdByOrgAndType({
        lyySwiftpassMerchantId: lyySwiftpassMerchantId
      });
      this.refDialogBase.setLoading(false);
      if (lodash.isObject(res) && lodash.eq(res.result, 0) && lodash.isObject(res.data)) {
        this.refDialogBase.openFun();
        let id_code_type_name = "";
        if (lodash.isString(res.data.id_code_type)) {
          id_code_type_name = idCardTypeName(res.data.id_code_type);
        }
        let merchant_type_name = "";
        if (lodash.isString(res.data.merchant_type)) {
          const merchantTypeObj = getMerchantTypeByValue(res.data.merchant_type);
          if (lodash.isObject(merchantTypeObj) && lodash.isString(merchantTypeObj.name)) {
            merchant_type_name = merchantTypeObj.name;
          }
        }
        let cert_type_name = "";
        if (lodash.isString(res.data.cert_type)) {
          const certTypeObj = getCertTypeByValue(res.data.cert_type);
          if (lodash.isObject(certTypeObj) && lodash.isString(certTypeObj.name)) {
            cert_type_name = certTypeObj.name;
          }
        }
        let localIndentityPhoto1 = "";
        let localIndentityPhoto2 = "";
        // 设置身份证图片
        if (normalString(res.data.local_indentity_photo)) {
          const photoArr = res.data.local_indentity_photo.split(";");
          if (Array.isArray(photoArr)) {
            const photoArr0 = photoArr[0];
            if (lodash.isString(photoArr0) && normalString(photoArr0)) {
              localIndentityPhoto1 = photoArr0; // 身份证正面
            }
            const photoArr1 = photoArr[1];
            if (lodash.isString(photoArr1) && normalString(photoArr1)) {
              localIndentityPhoto2 = photoArr1; // 身份证反面
            }
          }
        }
        this.info = {
          ...res.data,
          id_code_type_name: id_code_type_name,
          merchant_type_name: merchant_type_name,
          cert_type_name: cert_type_name,
          localIndentityPhoto1: localIndentityPhoto1,
          localIndentityPhoto2: localIndentityPhoto2,
          type1: "娱乐设备", // 经营品类一
          type2: "儿童类设备" // 经营品类二
        };
        console.log("this.info=", this.info);
      } else {
        this.dialogClose();
      }
    },
    // 关闭弹窗
    dialogClose() {
      this.refDialogBase.closeFun();
    },
    // 复制到剪贴板
    copyFun(text = "") {
      copyFun(text, this.copySign);
    },
    // 审核状态
    getStatus() {
      let statusName = "";
      if (
        lodash.isObject(this.info) &&
        !lodash.isEmpty(this.info) &&
        lodash.isNumber(this.info.log_status)
      ) {
        switch (this.info.log_status) {
          case 1:
            statusName = "等待审核";
            break;
          case 2:
            statusName = "审核不通过";
            break;
          case 3:
            statusName = "等待微信配置";
            break;
          case 4:
            statusName = "等待汇付审核";
            break;
          case 5:
            statusName = "审核通过";
            break;
        }
      }
      return statusName;
    },
    // 判断商户类型是否事业单位和党政机关
    isPublicInstitution(merchant_type) {
      return ["4", "5"].includes(String(merchant_type));
    },
    // 判断商户类型是否其他组织
    isOtherOrganizations(merchant_type) {
      return ["6"].includes(String(merchant_type));
    }
  }
};
</script>

<style lang="less" scoped>
.wechant-real-name-detail {
  .img-outside {
    display: flex;
  }
  .img-box {
    width: 200px;
    height: 200px;
    .el-image {
      width: 100%;
      height: 100%;
    }
  }
  .btn-wrapper {
    text-align: right;
  }
}
</style>
