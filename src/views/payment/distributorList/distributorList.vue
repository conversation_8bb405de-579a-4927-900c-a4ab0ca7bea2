<!--
 * @Description: 支付商户列表
 * @Author:
 * @Date:
 * @LastEditors: 'chenruifeng' <EMAIL>
 * @LastEditTime: 2024-03-26 14:16:40
 -->
<template>
  <div class="distributorList">
    <el-card>
      <div slot="header" class="clearfix header">
        <el-form :inline="true" label-position="right">
          <el-form-item label="商户类型">
            <el-select
              clearable
              placeholder="请选择商户类型"
              filterable
              v-model="formData.merchantType"
            >
              <el-option
                v-for="(item, index) in merchantTypeList"
                :key="index"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="商户账号" class="orgValue">
            <div class="wraper">
              <el-select
                clearable
                placeholder="请选择进件渠道"
                filterable
                v-model="formData.orgValuePrefix"
              >
                <el-option
                  v-for="(item, index) in orgValuePrefixList"
                  :key="index"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
              <el-input
                class="inline-input"
                v-model.trim="formData.orgValue"
                placeholder="请输入商户账号"
                clearable
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item label="商户全称">
            <el-input
              class="inline-input"
              v-model.trim="formData.merchantName"
              placeholder="请输入商户名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="子商户号">
            <el-input
              class="inline-input"
              v-model.trim="formData.wxSmid"
              placeholder="请输入子商户号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="平台申请单号" label-width="115px">
            <el-input
              class="inline-input"
              v-model.trim="formData.merchantApplyNo"
              placeholder="请输入平台申请单号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="商户ID">
            <el-input
              class="inline-input"
              v-model.trim="formData.distributorId"
              @input="formData.distributorId = getNatural(formData.distributorId)"
              placeholder="请输入商户ID"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="法人/负责人">
            <el-input
              class="inline-input"
              v-model.trim="formData.legalName"
              placeholder="请输入法人/负责人"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="资料提交时间：" label-width="115px">
            <el-date-picker
              v-model="formData.infoCreatedDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              :editable="false"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="进件渠道">
            <el-select
              @remove-tag="channelCodeClickChange"
              style="width: 300px"
              :multiple="true"
              clearable
              placeholder="请选择进件渠道"
              filterable
              v-model="formData.channelCodes"
            >
              <el-option
                @click.native="channelCodeClickChange(item)"
                v-for="(item, index) in channelList"
                :key="index"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="进件单创建时间：" label-width="125px">
            <el-date-picker
              v-model="formData.elCreatedDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              :editable="false"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="商户归属">
            <el-select
              clearable
              placeholder="请选择商户归属"
              filterable
              v-model="formData.agentDistributorId"
            >
              <el-option
                v-for="(item, index) in merchantEntryAgentLists"
                :key="index"
                :label="item.agentName"
                :value="item.appId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="渠道审核状态" label-width="115px">
            <el-select
              clearable
              placeholder="请选择渠道审核状态"
              filterable
              v-model="formData.elStatus"
            >
              <el-option
                v-for="(item, index) in verifyStateList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="平台审核状态" label-width="115px">
            <el-select
              clearable
              placeholder="请选择平台审核状态"
              filterable
              v-model="formData.status"
            >
              <el-option
                v-for="(item, index) in verifyPlatformStateList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="错误描述">
            <el-input
              class="inline-input"
              v-model.trim="formData.applyDesc"
              placeholder="请输入错误描述"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="渠道商户号">
            <el-input
              class="inline-input"
              v-model.trim="formData.merchantCode"
              placeholder="请输入渠道商户号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="进件单状态" label-width="115px">
            <el-select
              clearable
              placeholder="请选择进件单状态"
              filterable
              v-model="formData.entryStatus"
            >
              <el-option
                v-for="(item, index) in entryStatusList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资料编号">
            <el-input
              class="inline-input"
              v-model.trim="formData.lyySwiftpassMerchantId"
              placeholder="资料编号"
              clearable
            ></el-input>
          </el-form-item>
          <!--按钮-->
          <el-form-item>
            <el-button type="primary" @click="queryBtn">搜索</el-button>
            <el-button type="default" @click="defaultBtn">重置</el-button>
            <el-button
              type="primary"
              @click="submitHandle"
              :loading="getInfoLoading"
              :disabled="getInfoLoading"
              >提交进件<i class="el-icon-document" style="padding-left: 10px"></i
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <table-base ref="refTableBase" @callBack="getList()" :height="tableH">
          <div slot="lyySwiftpassMerchantId" slot-scope="scope">
            <el-button
              v-if="scope.row.lyySwiftpassMerchantId"
              type="text"
              @click="gotoDetail(scope.row)"
              >{{ scope.row.lyySwiftpassMerchantId }}</el-button
            >
          </div>
          <div slot="channelCode" slot-scope="scope">
            <p v-if="lodash.isNumber(scope.row.channelCode)">
              {{ scope.row.channelCode | arrGetValue(allChannelList, 'value', 'name') }}
            </p>
          </div>
          <div slot="elMerchantType" slot-scope="scope">
            <p v-if="lodash.isNumber(scope.row.elMerchantType)">
              {{
                String(scope.row.elMerchantType) | arrGetValue(merchantTypeList, 'value', 'name')
              }}
            </p>
          </div>
          <div slot="elStatus" slot-scope="scope">
            <p v-if="lodash.isNumber(scope.row.elStatus)">
              {{ scope.row.elStatus | arrGetValue(verifyStateList, 'value', 'label') }}({{
                scope.row.applyStatus
              }})
            </p>
          </div>
          <div slot="infoStatus" slot-scope="scope">
            <p v-if="scope.row.infoStatus">
              {{ scope.row.infoStatus | arrGetValue(verifyPlatformStateList, 'value', 'label') }}
            </p>
          </div>
          <div slot="applyDesc" slot-scope="scope">
            <div class="apply-desc" v-if="scope.row.applyDesc">
              <el-tooltip
                class="item"
                effect="dark"
                :content="scope.row.applyDesc || ''"
                placement="top"
              >
                <el-button type="text" class="copy" @click="copy(scope.row.applyDesc)">
                  {{ scope.row.applyDesc }}
                </el-button>
              </el-tooltip>
            </div>
          </div>
          <div slot="entryStatus" slot-scope="scope">
            <span v-if="scope.row.entryStatus == 1" class="success_color">有效</span>
            <span v-else-if="scope.row.entryStatus == 2" class="error_color">无效</span>
            <span v-else>-</span>
          </div>
          <div slot="entryMchType" slot-scope="scope">
            <span v-if="scope.row.entryMchType == 1">商户</span>
            <span v-else-if="scope.row.entryMchType == 2">分成方</span>
            <span v-else></span>
          </div>
          <div slot="elDescription" slot-scope="scope">
            <div class="apply-desc" v-if="scope.row.elDescription">
              <el-tooltip
                class="item"
                effect="dark"
                :content="scope.row.elDescription || ''"
                placement="top"
              >
                <el-button type="text" class="copy" @click="copy(scope.row.elDescription)">
                  {{ scope.row.elDescription }}
                </el-button>
              </el-tooltip>
            </div>
          </div>
          <div slot="runningStatus" slot-scope="scope">
            <p v-if="lodash.isNumber(scope.row.runningStatus)">
              {{ scope.row.runningStatus | arrGetValue(runningStatusList, 'value', 'label') }}
            </p>
          </div>
          <div slot="operation" slot-scope="scope">
            <el-button
              type="text"
              @click="merchantEntryChannel(scope.row)"
              :disabled="scope.row.elStatus === 6"
              >手动重试</el-button
            >
            <el-button type="text" @click="applyChannel(scope.row)">开通详情</el-button>
            <!-- 进件快照 -->
            <el-button type="text" @click="snapshot(scope.row)">进件快照</el-button>
            <el-button
              v-if="scope.row.elStatus !== -1"
              type="text"
              @click="merchantEntryDisable(scope.row)"
              >作废重生成</el-button
            >
          </div>
        </table-base>
      </div>
    </el-card>
    <!-- 提交进件 -->
    <el-dialog title="提交商户资料给渠道" :visible.sync="dialogVisible" width="600px">
      <div class="body">
        <div style="margin-bottom: 20px">选择对应资料提交给渠道</div>
        <el-form label-position="right">
          <el-form-item label="卡号和资料编号" label-width="110px">
            <el-select
              style="width: 400px"
              clearable
              placeholder="请选择卡号和资料编号"
              filterable
              v-model="cardForm.lyySwiftpassMerchantId"
              @change="lyySwiftpassMerchantIdChange"
            >
              <!-- <template v-if="bankCardList.length > 0"> -->
              <el-option
                v-for="(item, index) in bankCardList"
                :key="index"
                :label="`卡号：${item.accountCode || '--'},资料编号：${
                  item.lyySwiftpassMerchantId || '--'
                }`"
                :value="item.lyySwiftpassMerchantId"
              ></el-option>
              <!-- </template> -->
            </el-select>
          </el-form-item>
          <el-form-item label="可提交渠道" label-width="110px">
            <el-select
              style="width: 400px"
              multiple
              clearable
              placeholder="请选择进件渠道"
              filterable
              v-model="cardForm.channelCodes"
            >
              <el-option
                v-for="(item, index) in availableChannelList"
                :key="index"
                :label="item.name"
                :value="item.channelCode"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmHandler" :disabled="isDisable">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import TableBase from '@components/tableBase/tableBase';
import {
  merchantEntryChannel,
  merchantEntryList,
  merchantEntryDisable,
  merchantEntryInfo,
  applyOpneEntry,
  getToOpenEntry,
  getChannelListByType
} from '@api/payment/payment';
import {
  getAllChannelMap,
  getAllMerchantTypeList,
  getAllMerchantTypeMap,
  getChannelList,
  // getMainChannelList
} from '@views/payment/paymentCommon';
import { dateFormat } from '@js/utils';
import _ from 'lodash';
import Clipboard from 'clipboard';
import { dayMilliseconds } from '@components/tableBase/utilsTableBase';
import { getNatural } from '@js/index';
import setTableH from '@/mixins/setTableH';
import { getChannelByKey } from '@/views/payment/paymentCommon.js';

export default {
  name: 'distributorList',

  props: {},

  components: { TableBase },

  mixins: [setTableH],

  data() {
    const allMerchantTypeMap = getAllMerchantTypeMap(); // 商户类型，全部选项
    const allChannelMap = getAllChannelMap(); // 渠道-全部
    let allVerifyStateMap = { label: "全部", value: -2 }; // 渠道审核状态，全部
    let verifyStateList = [
      // 渠道审核状态列表
      allVerifyStateMap,
      { label: '无效', value: -1 },
      { label: '初始', value: 0 },
      { label: '待自动进件', value: 1 },
      { label: '进件中', value: 2 },
      { label: '进件失败', value: 3 },
      { label: '待支付验证', value: 4 },
      { label: '支付验证失败', value: 5 },
      { label: '完成', value: 6 },
    ];
    const allVerifyPlatformStateMap = { label: '全部', value: '-2' }; // 平台审核状态，全部
    const successVerifyPlatformStateMap = { label: '提交成功', value: '1' }; // 平台审核状态，提交成功
    const verifyPlatformStateList = [
      // 平台审核状态列表
      allVerifyPlatformStateMap,
      successVerifyPlatformStateMap,
      { label: '商户资料审核不通过全量驳回（资料全驳回修改）', value: '0' },
      { label: '待办(新增)', value: '2' },
      { label: '编辑中', value: '4' },
      { label: '自动驳回挂起', value: '5' },
    ];
    const formData = {
      merchantType: allMerchantTypeMap.value, // 商户类型
      orgValue: '', // 商户账号
      merchantName: '', // 商户全称
      wxSmid: '', // 子商户号
      orgCreatedDate: [], // 注册日期
      merchantApplyNo: '', // 平台申请单号
      distributorId: '', // 商户ID
      legalName: '', // 法人/负责人
      infoCreatedDate: [new Date(Date.now() - 29 * dayMilliseconds()), new Date()], // 资料提交时间
      channelCodes: [], // 进件渠道
      elCreatedDate: [], // 进件单创建时间
      elStatus: allVerifyStateMap.value, // 渠道审核状态
      status: allVerifyPlatformStateMap.value, // 平台审核状态
      applyDesc: "", // 错误描述
      agentDistributorId: "", //商户归属id
      orgValuePrefix: "不限", //商户账号前缀
      entryStatus:1,//进件单状态
      allChannelCodes:[],
      lyySwiftpassMerchantId: '', // 资料Id
    };
    return {
      dialogVisible: false,
      cardForm: {
        adOrgId: '',
        lyySwiftpassMerchantId: '',
        channelCodes: [],
      },
      availableChannelList: [], //可选渠道
      bankCardList: [], //商户资料列表
      entryStatusList: [
        { label: '全部', value: '' },
        { label: '有效', value: 1 },
        { label: '失效', value: 2 },
      ],
      formData: Object.assign({}, formData), // 列表表单
      defaultFormData: Object.assign({}, formData), // 初始化列表表单
      copyFormData: Object.assign({}, formData), // 锁定列表参数
      refTableBase: null, // 列表控制
      pageSize: 20,
      pageSizeArr: [20, 30, 40, 50],
      columns: [
        { key: 'distributorId', label: '商户ID' },
        { key: 'lyySwiftpassMerchantId', label: '资料编号', slot: true },
        { key: 'merchantCode', label: '渠道商户号', width: '90px' },
        { key: 'lyySwiftpassMerchantId', label: '资料编号', width: '90px' },
        { key: 'channelCode', label: '渠道名称', slot: true },
        { key: 'orgValue', label: '商户账号', width: '140px' },
        { key: 'agentName', label: '商户归属', width: '140px' },
        { key: 'merchantName', label: '商户简称', width: '200px' },
        { key: 'elMerchantType', label: '商户类型', slot: true },
        { key: 'elLegalName', label: '法人/负责人', width: '95px' },
        { key: 'merchantApplyNo', label: '平台申请单号', width: '95px' },
        { key: 'infoStatus', label: '平台审核状态', slot: true, width: '95px' },
        { key: 'channelApplyNo', label: '渠道申请单号', width: '95px' },
        { key: 'elStatus', label: '渠道审核状态', slot: true, width: '95px' },
        { key: 'orgCreated', label: '平台注册日期', width: '140px' },
        { key: 'infoCreated', label: '资料提交时间', width: '140px' },
        { key: 'entryStatus', label: '进件单状态', width: '140px', slot: true },
        { key: 'infoUpdated', label: '资料更新时间', width: '140px' },
        { key: 'elCreateTime', label: '进件单创建时间', width: '140px' },
        { key: 'elUpdateTime', label: '进件单更新时间', width: '140px' },
        { key: 'entryMchType', label: '进件单类型', width: '140px', slot: true },
        {
          key: 'applyDesc',
          label: '进件失败描述',
          slot: true,
          width: '100px',
          bindProps: { fixed: 'right' },
        },
        { key: 'elDescription', label: '描述', slot: true, bindProps: { fixed: 'right' } },
        { key: 'runningStatus', label: '流转状态', slot: true, bindProps: { fixed: 'right' } },
        {
          key: 'operation',
          label: '操作',
          slot: true,
          width: '160px',
          bindProps: { fixed: 'right' },
        },
      ],
      merchantTypeList: getAllMerchantTypeList(), // 商户类型
      allMerchantTypeMap, // 商户类型，全部选项
      channelList: [], // 渠道
      allChannelList: [], // 渠道
      allChannelCodes:[], //所有渠道code值
      allChannelMap, // 渠道-全部
      verifyStateList, // 渠道审核状态列表
      allVerifyStateMap, // 渠道审核状态，全部
      verifyPlatformStateList, // 平台审核状态列表
      allVerifyPlatformStateMap, // 平台审核状态，全部
      lodash: _,
      runningStatusList: [
        // 流转状态
        { label: '进行中', value: 1 },
        { label: '已暂停', value: 0 },
      ],
      getNatural: getNatural,
      // merchantEntryAgentLists:initMerchantEntryAgentList, //商户归属列表
      orgValuePrefixList: [{ name: '不限' }, { name: 'qyh-' }, { name: 'open-' }, { name: 'div-' }], //商品账号前缀列表
      getInfoLoading: false, //提交进件获取资料loading
    };
  },

  beforeCreate() {},

  created() {},

  beforeMount() {},

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 通过 `vm` 访问组件实例
      if (
        vm.$route.query &&
        vm.$route.query.adOrgId &&
        from.name === 'paymentManagement-merchantPayAbility'
      ) {
        vm.formData = Object.assign({}, vm.defaultFormData);
        vm.formData.distributorId = vm.$route.query.adOrgId;
        vm.formData.infoCreatedDate = [];
        vm.init();
      }
    });
  },

  mounted() {
    this.init();
  },

  beforeUpdate() {},

  updated() {},

  activated() {
    this.$store.dispatch('payment/merchantEntryAgentList');
  },

  deactivated() {},

  beforeDestroy() {
    // window.removeEventListener('resize',this.setTableH)
  },

  destroyed() {},

  errorCaptured() {},

  methods: {
    async init() {
      // 初始化表格
      this.refTableBase = this.$refs.refTableBase;
      this.refTableBase.setColumnsSecurity(this.columns);
      this.refTableBase.setPageSizeArrSecurity(this.pageSizeArr);
      this.refTableBase.setPageSizeSecurity(this.pageSize);
      // 获取渠道列表
      await this.initChannel();
      this.queryBtn();
    },
    // 重置按钮
    defaultBtn() {
      this.formData = Object.assign({}, this.defaultFormData);
      this.queryBtn();
    },
    // 查询按钮
    queryBtn() {
      this.refTableBase.setPageIndexSecurity(1);
      this.copyFormData = Object.assign({}, this.formData);
      this.getList();
    },
    // 初始化渠道相关信息
    async initChannel() {
      this.allChannelList = await getChannelList();
      const res = await getChannelListByType({ "abilityCodes": ["entry"]});
      if(res && res.code === "0000000") {
        const channelList = res.body.map(item => {
          return {
            key: item.thirdPayServiceCode,
            value: item.channelCode,
            name: item.channelName,
          }
        });
        this.channelList = [
          getAllChannelMap(),
          ... channelList
        ];
        const allChannelCodes = this.channelList.map((mapItem) => {
          return mapItem.value
        });
        this.formData.allChannelCodes = [...allChannelCodes];
        this.formData.channelCodes = [...allChannelCodes];
        this.allChannelCodes = [...allChannelCodes];
      }
    },
    /**
     * @description: 警告消息
     * @param {string} message
     * @return {*}
     */
    showWarnMessage(message) {
      this.$message({
        message,
        type: 'warning',
      });
    },
    /**
     * @description: 提交进件
     * @return {*}
     */
    async submitHandle() {
      const { distributorId, orgValue } = this.formData;
      if (!distributorId && !orgValue) {
        // 提交进件”触发资料查询条件：【商户账号】、【商户ID】，若【商户账号】和【商户ID】为空，点击“提交进件”按钮提示：请输入你要选择的商户账号或商户ID
        this.showWarnMessage('请输入你要选择的商户账号或商户ID');
        return;
      }
      this.reset();
      const result = await this.merchantEntryInfo(distributorId, orgValue);
      result === 0 && (this.dialogVisible = true);
    },
    reset() {
      this.cardForm = {
        adOrgId: '',
        lyySwiftpassMerchantId: '',
        channelCodes: [],
      };
      this.availableChannelList = [];
      this.bankCardList = [];
    },
    /**
     * @description: 获取商户资料
     * @param {number} adOrgId 商户id
     * @param {number} orgValue 商户账号
     * @return {*} 接口状态码
     */
    async merchantEntryInfo(adOrgId, orgValue) {
      if (this.getInfoLoading) return;
      this.getInfoLoading = true;
      try {
        const res = await merchantEntryInfo({ adOrgId, orgValue });
        this.getInfoLoading = false;
        if (res?.result === 0) {
          this.bankCardList = res?.data?.bankCardList ?? [];
          this.cardForm.adOrgId = res?.data?.adOrgId;
        }
        return res.result;
      } catch (error) {
        this.getInfoLoading = false;
      }
    },

    /**
     * @description: 申请开通渠道进件
     * @return {*}
     */
    async applyOpneEntry() {
      const { adOrgId, lyySwiftpassMerchantId: swiftpassMerchantId, channelCodes } = this.cardForm;
      const data = {
        adOrgId,
        swiftpassMerchantId,
        channelCodes,
      };
      const res = await applyOpneEntry(data);
      res?.code === 0 && this.$message.success('操作成功');
    },

    lyySwiftpassMerchantIdChange(swiftpassMerchantId) {
      this.cardForm.channelCodes = [];
      this.availableChannelList = [];
      this.getToOpenEntry(swiftpassMerchantId);
    },
    /**
     * @description: 查询待开通的渠道
     * @param {*} swiftpassMerchantId 资料id
     * @return {*}
     */
    async getToOpenEntry(swiftpassMerchantId) {
      if (!swiftpassMerchantId) return;
      const { adOrgId } = this.cardForm;
      const data = {
        adOrgId,
        swiftpassMerchantId
      }
      const res = await getToOpenEntry(data)
      this.availableChannelList = res?.data ?? []
      const jdBlue = getChannelByKey('jdn')
      // 更新京东蓝小方区分原先京东
      this.availableChannelList.forEach((item) => {
        if (item.channelCode === jdBlue.value) {
          item.name = jdBlue.name;
        }
      });
    },
    /**
     * 确定提交进件
     */
    confirmHandler() {
      this.$confirm('你确定要帮商户提交进件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (instance.confirmButtonLoading) return;
            instance.confirmButtonLoading = true;
            await this.applyOpneEntry();
            done();
            instance.confirmButtonLoading = false;
            this.dialogVisible = false;
          } else {
            done();
          }
        },
      });
    },
    // 列表
    async getList() {
      this.refTableBase.showLoadingFun();
      this.refTableBase.reSetListFun();
      const res = await merchantEntryList(this.getParams());
      this.refTableBase.closeLoadingFun();
      if (res && res.code === 0 && res.data) {
        this.refTableBase.setListSecurity(res.data.list);
        this.refTableBase.setTotalSecurity(res.data.total);
      }
    },
    // 列表参数
    getParams() {
      const channelCodes = this.copyFormData.channelCodes.filter((item) => {
        return item !== '-1';
      });
      let params = {
        pageIndex: this.refTableBase.pageInfo.pageIndex,
        pageSize: this.refTableBase.pageInfo.pageSize,
        agentAppId: this.copyFormData.agentDistributorId, //商户归属id
        channelCodes, //排除全部-1的值
      };
      if (
        this.copyFormData.merchantType &&
        this.copyFormData.merchantType !== this.allMerchantTypeMap.value
      ) {
        params['merchantType'] = this.copyFormData.merchantType;
      }
      if (this.copyFormData.orgValue) {
        let { orgValuePrefix } = this.copyFormData;
        params['orgValue'] =
          orgValuePrefix === '不限'
            ? this.copyFormData.orgValue
            : orgValuePrefix + this.copyFormData.orgValue;
      }
      if (this.copyFormData.merchantName) {
        params['merchantName'] = this.copyFormData.merchantName;
      }
      if (this.copyFormData.wxSmid) {
        params['wxSmid'] = this.copyFormData.wxSmid;
      }
      if (
        Array.isArray(this.copyFormData.orgCreatedDate) &&
        this.copyFormData.orgCreatedDate.length === 2
      ) {
        params['startOrgCreatedDate'] = dateFormat(new Date(this.copyFormData.orgCreatedDate[0]));
        params['endOrgCreatedDate'] = dateFormat(new Date(this.copyFormData.orgCreatedDate[1]));
      }
      if (this.copyFormData.merchantApplyNo) {
        params['merchantApplyNo'] = this.copyFormData.merchantApplyNo;
      }
      if (this.copyFormData.distributorId) {
        params['distributorId'] = this.copyFormData.distributorId;
      }
      if (this.copyFormData.legalName) {
        params['legalName'] = this.copyFormData.legalName;
      }
      if (
        Array.isArray(this.copyFormData.infoCreatedDate) &&
        this.copyFormData.infoCreatedDate.length === 2
      ) {
        params['startInfoCreatedDate'] = dateFormat(new Date(this.copyFormData.infoCreatedDate[0]));
        params['endInfoCreatedDate'] = dateFormat(new Date(this.copyFormData.infoCreatedDate[1]));
      }
      if (
        Array.isArray(this.copyFormData.elCreatedDate) &&
        this.copyFormData.elCreatedDate.length === 2
      ) {
        params['startElCreatedDate'] = dateFormat(new Date(this.copyFormData.elCreatedDate[0]));
        params['endElCreatedDate'] = dateFormat(new Date(this.copyFormData.elCreatedDate[1]));
      }
      if (
        _.isNumber(this.copyFormData.elStatus) &&
        this.copyFormData.elStatus !== this.allVerifyStateMap.value
      ) {
        params['elStatus'] = [this.copyFormData.elStatus];
      }
      if (
        this.copyFormData.status &&
        this.copyFormData.status !== this.allVerifyPlatformStateMap.value
      ) {
        params['status'] = [this.copyFormData.status];
      }
      if (this.copyFormData.applyDesc) {
        params['applyDesc'] = this.copyFormData.applyDesc;
      }
      // 渠道商户号
      if (this.copyFormData.merchantCode) {
        params['merchantCode'] = this.copyFormData.merchantCode;
      }
      // 进件单状态
      if (this.copyFormData.entryStatus) {
        params['entryStatus'] = this.copyFormData.entryStatus;
      }
      // 资料ID
      if (this.copyFormData.lyySwiftpassMerchantId) {
        params['lyySwiftpassMerchantId'] = this.copyFormData.lyySwiftpassMerchantId;
      }

      return params;
    },
    // 复制到剪贴板
    copy(text) {
      if (text || text === 0) {
        const clipboard = new Clipboard('.copy', {
          text: function () {
            return text;
          },
        });
        clipboard.on('success', (e) => {
          this.$message.success('复制成功!');
          // 释放内存
          clipboard.destroy();
        });
        clipboard.on('error', (e) => {
          // 不支持复制
          this.$message.error('该浏览器不支持自动复制!');
          // 释放内存
          clipboard.destroy();
        });
      }
    },
    // 跳转到商户渠道详情页
    applyChannel(row) {
      this.$router.push({
        path: '/paymentManagement/distributorApplyChannel',
        query: {
          distributorId: row.distributorId,
          channelCode: row.channelCode,
          merchantCode: row.merchantCode,
          wxSmid: row.wxSmid,
          elMerchantName: row.elMerchantName,
          aliSmid: row.aliSmid,
          unionSmid: row.unionSmid,
          lyySwiftpassMerchantId: row.lyySwiftpassMerchantId,
          agentAppId: row.agentAppId,
          merchantType: row.elMerchantType,
        },
      });
    },
    // 进件快照
    snapshot(row) {
      // 进件快照
      this.$router.push({
        path: '/paymentManagement/distributorSnapshot',
        query: {
          lyySwiftpassMerchantId: `${row.lyySwiftpassMerchantId}`,
          merchantApplyNo: `${row.merchantApplyNo}`,
          channelApplyNo: `${row.channelApplyNo}`,
          infoData: row.infoData,
          distributorId: row.distributorId,
        },
      });
    },
    // 跳转到商户进件详情页
    gotoDetail(row) {
      this.$router.push({
        path: '/paymentManagement/distributorDetail',
        query: {
          lyySwiftpassMerchantId: row.lyySwiftpassMerchantId,
        },
      });
    },
    // 手动重试按钮
    async merchantEntryChannel(row) {
      this.$alert('确定手动重试？', '手动重试', {
        confirmButtonText: '确定',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            const params = {
              data: [
                {
                  merchantChannelEntryLogId: row.merchantChannelEntryLogId,
                  distributorId: row.distributorId,
                  currentApplyStatus: row.applyStatus,
                  channelCode: row.channelCode,
                  lyySwiftpassMerchantId: row.lyySwiftpassMerchantId,
                },
              ],
            };
            const res = await merchantEntryChannel(params);
            instance.confirmButtonLoading = false;
            if (res && res.code === 0) {
              this.$message.success(res.message || '手动重试成功');
              this.getList();
              done();
            }
          } else {
            done();
          }
        },
      });
    },
    channelCodeClickChange(item) {
      console.log('item', item);
      const {
        formData: { channelCodes },
        allChannelCodes,
      } = this;
      if (item.value === '-1' || item === '-1') {
        // 点击全部渠道、正反选
        if (!channelCodes.includes('-1')) {
          this.formData.channelCodes = [];
        } else {
          this.formData.channelCodes = allChannelCodes;
        }
      } else {
        // 点击其他先清楚全部渠道选中状态
        channelCodes.forEach((item, index) => {
          if (item === '-1') {
            this.formData.channelCodes.splice(index, 1);
          }
        });
        // 如果选中所有其他渠道，则默认选中全部渠道
        if (this.formData.channelCodes.length === allChannelCodes.length - 1) {
          this.formData.channelCodes = allChannelCodes;
        }
      }
    },
    // 渠道进件单作废重生成
    async merchantEntryDisable(row) {
      if (
        [row.merchantChannelEntryLogId, row.distributorId, row.channelCode].every(
          (item) => typeof item === 'number'
        )
      ) {
        this.$alert('确定作废重生成？', '作废重生成', {
          confirmButtonText: '确定',
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              const params = {
                data: [
                  {
                    merchantChannelEntryLogId: row.merchantChannelEntryLogId,
                    distributorId: row.distributorId,
                    channelCode: row.channelCode,
                    isCreate: true,
                  },
                ],
              };
              const res = await merchantEntryDisable(params);
              instance.confirmButtonLoading = false;
              if (res && res.code === 0) {
                this.$message.success(res.message || '作废重生成成功');
                this.getList();
                done();
              }
            } else {
              done();
            }
          },
        });
      }
    },
  },

  computed: {
    merchantEntryAgentLists() {
      return this.$store.state.payment.merchantEntryAgentLists;
    },
    isDisable() {
      // 不勾选渠道，或可选渠道为无，按钮置灰不给点击。
      return this.cardForm.channelCodes.length === 0;
    },
  },

  watch: {},
};
</script>

<style lang="less" scoped>
.distributorList {
  .apply-desc {
    /deep/ .el-button {
      width: 100%;

      span {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
.orgValue {
  // display:flex;
  .wraper {
    display: flex;
  }
  // & /deep/ .el-form-item__content{
  //   display:flex;
  // }
}
.success_color {
  color: #23d18b;
}
.error_color {
  color: red;
}
</style>
