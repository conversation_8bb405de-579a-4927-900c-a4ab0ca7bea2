<template>
  <div class="entertainment-drainage-template-config-index">
    <div class="page-title">活动模板配置</div>
    <el-button class="btn" type="primary" icon="el-icon-plus" @click="handleDialog(true)">新增活动</el-button>
    <EDTemplateConfigTable
      :loading="loading"
      :tableData="tableData"
      @reload="getTableData"
      @edit="data => handleDialog(false, data)"
    />
    <el-pagination
      class="pagination"
      @size-change="(v) => { search.pageIndex = 1; search.pageSize = v; getTableData() }"
      @current-change="getTableData"
      :current-page.sync="search.pageIndex"
      background
      :page-sizes="[10, 20, 50, 100]"
      :page-size="search.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total" />
    <EDTemplateConfigForm
      :isAdd="isAdd"
      ref="form"
      @reload="getTableData"
    />
  </div>
</template>

<script>
import EDTemplateConfigTable from './Components/Table'
import EDTemplateConfigForm from './Components/Form'
import { getList, getDetail } from '@/api/entertainmentDrainage'
import config from '@/assets/js/config'

export default {
  name: 'EntertainmentDrainageTemplateConfigIndex',
  components: { EDTemplateConfigForm, EDTemplateConfigTable },
  data () {
    return {
      isAdd: false,
      search: {
        pageIndex: 1,
        pageSize: 10
      },
      loading: true,
      tableData: [],
      total: 0,
    }
  },
  created () {

  },
  mounted () {
    this.$nextTick(() => {
      this.getTableData()
    })
  },
  methods: {
    getTableData: async function () {
      try {
        this.loading = true
        const res = await getList(this.search)
        console.log(res)
        if (res && res.result === 0 && res.data) {
          this.tableData = [...res.data.list].map(item => ({
            ...item,
            id: item.lyyDrainageTemplateConfigId,
            loading: false,
            delLoading: false,
            iconUrl: config.aliyunImgBaseUrl + item.iconUrl
          }))
          this.total = res.data.total
        }
      } catch (e) {
        this.$message.error(e)
      } finally {
        this.loading = false
      }
    },
    handleDialog: async function (bool, data) {
      this.isAdd = bool
      if (!bool && data) {
        const index = this.tableData.findIndex(item => item.id === data.id)
        try {
          this.$set(this.tableData[index], 'loading', true)
          const res = await getDetail({ id: data.id })
          if (res && res.result === 0) {
            this.$refs.form.init(res.data)
          }
        } catch (e) {

        } finally {
          this.$set(this.tableData[index], 'loading', false)
        }
        return
      }
      this.$refs.form.init()
    }
  }
}
</script>

<style scoped lang="less">
.page-title{
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}
.btn{
  margin-bottom: 15px;
}
.pagination{
  margin: 15px 0;
}
</style>
