<template>
  <div class="entertainment-drainage-template-config-table">
    <el-table
      v-loading="loading"
      :data="tableData"
      border
    >
      <el-table-column label="活动名称" align="center" prop="name" />
      <el-table-column label="开始时间" align="center" prop="beginTime" sortable />
      <el-table-column label="结束时间" align="center" prop="endTime" sortable>
        <template slot-scope="scope">
          {{ scope.row.endTime ? scope.row.endTime: '不限' }}
        </template>
      </el-table-column>
      <el-table-column label="图标" align="center" prop="iconUrl">
        <template slot-scope="scope">
          <img class="iconUrl" :src="scope.row.iconUrl" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left" min-width="150px">
        <template slot-scope="scope">
          <el-button
            type="primary"
            icon="el-icon-edit"
            @click="$emit('edit', scope.row)"
            :loading="scope.row.loading"
          >编辑</el-button>
          <el-button
            type="danger"
            icon="el-icon-delete"
            @click="del(scope.row, scope.$index)"
            :loading="scope.row.delLoading"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { del } from '../../../../api/entertainmentDrainage'

export default {
  name: 'EDTemplateConfigTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {

    }
  },
  methods: {
    del: function (data, index) {
      this.$confirm('删除后小程序不再出现此活动入口', '温馨提示', {
        type: 'warning'
      }).then(async () => {
        this.$set(this.tableData[index], 'delLoading', true)
        try {
          const res = await del({ lyyDrainageTemplateConfigId: data.id })
          if (res.result === 0) {
            this.$message.success('删除活动成功')
            this.$emit('reload')
          }
        } catch (e) {

        } finally {
          this.$set(this.tableData[index], 'delLoading', false)
        }
      })
    }
  }
}
</script>

<style scoped>
  .iconUrl{
    width: 50px;
    height: 50px;
    border-radius: 50%;
  }
</style>
