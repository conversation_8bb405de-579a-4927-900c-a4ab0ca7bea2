<template>
  <div class="entertainment-drainage-template-config-form">
    <el-dialog
      :title="isAdd ? '新增活动': '修改活动'"
      width="800px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :destory-on-close="true"
      :visible.sync="dialogVisible"
    >
      <el-form :model="form" ref="form" label-width="120px" :rules="rules">
        <el-form-item label="活动名称:" prop="name">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-input v-model="form.name" placeholder="请输入活动名称" :maxLength="5" />
            </el-col>
            <el-col :span="4"><span class="tip">(限5个字)</span></el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="开始时间:" prop="beginTime">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-date-picker
                v-model="form.beginTime"
                type="datetime"
                :pickerOptions="pickerOptions"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择开始时间"
                @change="form.endTime = ''"
              />
            </el-col>
            <el-col :span="4"></el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="结束时间:" prop="endTime">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-date-picker
                :disabled="!form.beginTime"
                v-model="form.endTime"
                :pickerOptions="endTimePickerOptions"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择结束时间" />
            </el-col>
            <el-col :span="4"><span class="tip">不填写代表持续</span></el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="图标:" prop="iconUrl">
          <el-row :gutter="10">
            <el-col :span="12">
              <upload-img
                v-model="form.iconUrl"
                :uploadApi="uploadImgUrl"
              />
            </el-col>
            <el-col :span="4"></el-col>
          </el-row>
        </el-form-item>
        <div class="tips">若跳转至第三方小程序，需要填写</div>
        <el-form-item label="小程序APPID:" prop="appId">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-input v-model="form.appId" placeholder="请输入小程序APPID"></el-input>
            </el-col>
            <el-col :span="4"></el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="小程序活动路径:" prop="miniAppUrl">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-input v-model="form.miniAppUrl" placeholder="请输入活动路径"></el-input>
            </el-col>
            <el-col :span="4"></el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <div class="footer">
          <el-button @click="close">关闭</el-button>
          <el-button type="primary" @click="submit" :loading="loading">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import UploadImg from '../../../../components/UploadImg'
import { uploadImgUrl, add, update } from '@/api/entertainmentDrainage'

export default {
  name: 'EDTemplateConfigForm',
  components: { UploadImg },
  props: {
    isAdd: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      uploadImgUrl,
      dialogVisible: false,
      form: {
        name: '',
        beginTime: '',
        endTime: '',
        iconUrl: '',
        appId: '',
        miniAppUrl: ''
      },
      pickerOptions: {
        disabledDate: time => {
          const date = new Date()
          return time.getTime() < (date.getTime() - 24 * 60 * 60 * 1000)
        }
      },
      rules: {
        name: [{ required: true, message: '请输入活动名称' }],
        beginTime: [{ required: true, message: '请选择活动开始时间' }],
        iconUrl: [{ required: true, message: '请上传icon图片' }]
      },
      loading: false
    }
  },
  created () {
  },
  computed: {
    endTimePickerOptions: function () {
      return {
        disabledDate: time => {
          const date = new Date(this.form.beginTime)
          return time.getTime() < date.getTime()
        }
      }
    }
  },
  methods: {
    init(data) {
      this.form = data ? {...data}: {...this.$options.data().form}
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    },
    submit: function () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.loading = false
          const func = this.isAdd ? add: update
          try {
            const res = await func(this.form)
            if (res.result === 0) {
              this.$message.success(`${this.isAdd ? '新增活动成功': '修改活动成功'}`)
              this.close()
              this.$emit('reload')
            }
          } catch (e) {}
          finally {
            this.loading = false
          }
        }
      })
    },
    close: function () {
      this.form = {...this.$options.data().form}
      this.$refs.form.resetFields()
      this.$nextTick(() => {
        this.dialogVisible = false
      })
    }
  }
}
</script>

<style scoped lang="less">
  .tips{
    margin-bottom: 15px;
  }
  /deep/ .el-date-editor--datetime{
    width: 100% !important;
  }
</style>
