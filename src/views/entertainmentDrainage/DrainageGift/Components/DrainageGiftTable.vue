<template>
  <div class="DrainageGiftTable">
    <el-table :data="tableData" border stripe :loading="loading">
      <el-table-column label="兑换礼品" width="200px" align="center">
        <template slot-scope="scope">56CM软萌小鲨鱼</template>
      </el-table-column>
      <el-table-column label="编号" prop="cdkey" width="200px" align="center" />
      <el-table-column label="姓名" prop="receiverName" width="150"></el-table-column>
      <el-table-column label="电话" prop="receiverPhone" width="200"></el-table-column>
      <el-table-column label="地址" prop="receiverAddress" width="300" />
      <el-table-column label="状态" sortable prop="status" width="100" align="center">
        <template slot-scope="scope">
          <span :class="scope.row.status_class">{{ scope.row.status_text }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <!-- $emit('handleReceived', scope.row) -->
          <el-button
            :loading="scope.row.loading"
            type="text"
            v-if="scope.row.status === 2"
            @click="handleDeliver(scope.row, scope.$index)"
          >发货</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { gift } from '../../../../api/entertainmentDrainage'

export default {
  name: 'DrainageGiftTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {}
  },
  methods: {
    handleDeliver: function (data, index) {
      this.$confirm('确定设为已发货状态', '提示', {
        type: 'warning'
      }).then(async () => {
        this.$set(this.tableData[index], 'loading', true)
        const res = await gift.updateShipping({
          lyyDrainageGiftOrderId: data.lyyDrainageGiftOrderId
        })
        if (res && res.result === 0) {
          this.$message.success('操作成功')
          this.$emit('reload')
        }
      }).finally(() => {
        this.$set(this.tableData[index], 'loading', false)
      })
    }
  }
}
</script>

<style scoped>

</style>
