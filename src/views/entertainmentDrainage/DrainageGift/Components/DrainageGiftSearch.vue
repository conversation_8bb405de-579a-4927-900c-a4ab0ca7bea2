<template>
  <div class="DrainageGiftSearch">
    <el-form inline ref="search">
      <el-form-item label="兑换码" prop="cdkey"></el-form-item>
      <el-form-item label="用户id" prop="lyyUserId"></el-form-item>
      <el-form-item label="订单号" prop="lyyUserId"></el-form-item>
      <el-form-item label="收件人姓名" prop="lyyUserId"></el-form-item>
      <el-form-item label="手机号" prop="lyyUserId"></el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="search.status" multiple>
          <el-option label=""></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'DrainageGiftSearch',
  data () {
    return {
      search: {
        cdkey: '',
        lyyUserId: '',
        outTradeNo: '',
        receiverName: '',
        receiverPhone: '',
        status: []
      }
    }
  }
}
</script>

<style scoped>

</style>
