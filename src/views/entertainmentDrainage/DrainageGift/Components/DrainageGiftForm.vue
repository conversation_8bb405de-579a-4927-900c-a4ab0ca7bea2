<template>
  <div class="DrainageGiftForm">
    <el-dialog
      title="发货"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form ref="form" label-width="120px" :model="row" :rules="rules">
        <el-form-item label="收件人姓名" prop="receiverName">
          {{ row.receiverName }}
        </el-form-item>
        <el-form-item label="收件人电话" prop="receiverPhone">
          {{ row.receiverPhone }}
        </el-form-item>
        <el-form-item label="收件人地址" prop="receiverAddress">
          {{ row.receiverAddress }}
        </el-form-item>
        <el-form-item label="快递公司名称" prop="expressCompany">
          <el-input v-model="row.expressCompany" placeholder="请输入快递公司名称"></el-input>
        </el-form-item>
        <el-form-item label="快递单号" prop="expressNo">
          <el-input v-model="row.expressNo" placeholder="请输入快递单号"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: right">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { gift } from '../../../../api/entertainmentDrainage'

export default {
  name: 'DrainageGiftForm',
  data () {
    return {
      dialogVisible: false,
      row: {
        expressCompany: '',
        expressNo: ''
      },
      rules: {
        expressCompany: [{ required: true, message: '请输入快递公司名称' }],
        expressNo: [{ required: true, message: '请输入快递单号' }],
      }
    }
  },
  methods: {
    submit: function () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          try {
            this.loading = true
            const res = await gift.updateShipping({...this.row})
          } finally {
            this.loading = false
          }
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
