<template>
  <div class="drainageGiftIndex">
    <div class="page-title">
      礼品兑换
    </div>
    <!--<DrainageGiftSearch ref="search" @handleSearch="handleSearch" />-->
    <DrainageGiftTable
      :tableData="tableData"
      @reload="getList"
      :loading="loading"
    />
    <el-pagination
      class="pagination"
      @size-change="(v) => { search.pageIndex = 1; search.pageSize = v; getList() }"
      @current-change="getList"
      :current-page.sync="search.pageIndex"
      background
      :page-sizes="[10, 20, 50, 100]"
      :page-size="search.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total" />
  </div>
</template>

<script>
import DrainageGiftTable from './Components/DrainageGiftTable'
import { gift } from '../../../api/entertainmentDrainage'

export default {
  name: 'DrainageGiftIndex',
  components: { DrainageGiftTable },
  data () {
    return {
      search: {
        pageIndex: 1,
        pageSize: 10
      },
      loading: true,
      status: [
        { key: 1, className: 'gray', name: '未兑换' },
        { key: 2, className: 'primary', name: '待发货' },
        { key: 3, className: 'success', name: '已发货' }
      ],
      tableData: [],
      total: 0
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // handleSearch: function (data) {
    //   this.search = {
    //     ...data,
    //     pageIndex: 1
    //   }
    //   this.getList()
    // },
    getList: async function () {
      try {
        this.loading = true
        const res = await gift.getList(this.search)
        if (res && res.result === 0) {
          this.tableData = [...res.data.list].map(item => {
            const index = this.status.findIndex(st => st.key === item.status)
            return {
              ...item,
              status_text: index === -1 ? '未知状态': this.status[index].name,
              status_class: index === -1 ? '': this.status[index].className,
              loading: false
            }
          })
          this.total = res.data.total
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.page-title{
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}
.pagination {
  margin: 20px auto;
  text-align: center;
}
</style>
