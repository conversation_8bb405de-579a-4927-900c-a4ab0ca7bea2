<template>
  <div class="ShopSearch">
    <el-form inline ref="search" :model="search" label-width="120px">
      <el-form-item label="">
        <el-radio-group v-model="type">
          <el-radio :label="1">商户ID</el-radio>
          <el-radio :label="2">商户账户</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="">
        <el-input v-model.trim="search.value" placeholder="请输入" />
      </el-form-item>
      <el-form-item>
        <el-button
          :disabled="!search.value"
          type="primary"
          icon="el-icon-search"
          @click="handleSearch"
          :loading="searchLoading"
        >搜索</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'ShopSearch',
  props: {
    searchLoading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      type: 2, // 1 商户id; 2 商户账户
      search: {
        value: ''
      }
    }
  },
  methods: {
    handleSearch: function () {
      switch (this.type) {
        case 1:
          this.$emit('search', { distributorId: Number(this.search.value) || '' })
          break
        case 2:
          this.$emit('search', { name: this.search.value || '' })
          break
      }
    }
  }
}
</script>

<style scoped>

</style>
