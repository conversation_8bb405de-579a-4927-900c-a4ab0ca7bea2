<template>
  <div class="entertainmentShopSwitchIndex">
    <div class="page-title">商家引流功能开关设置</div>
    <ShopSearch @search="handleSearch" :searchLoading="searchLoading" />
    <el-form v-if="shopDetail" :model="shopDetail" label-width="120px">
      <el-form-item label="商户账号：">{{ shopDetail.phone }}</el-form-item>
      <el-form-item label="商户ID：">{{ shopDetail.distributorId }}</el-form-item>
      <el-form-item label="商户名称：">{{ shopDetail.adOrgName }}</el-form-item>
      <el-form-item label="设备明细：">{{ shopDetail.equipmentDetail }}</el-form-item>
      <ShopFeeSetting :shopDetail="shopDetail" @reloadDetail="handleSearch" />
    </el-form>
    <el-divider />
    <ShopSwitchGroupList
      v-if="shopDetail"
      :shop-detail="shopDetail"
      ref="groupList"
      @reloadDetail="handleSearch"
    />
  </div>
</template>

<script>
import ShopSearch from '../ComComponents/ShopSearch'
import { switchSetting } from '@/api/entertainmentDrainage'
import ShopSwitchGroupList from './Components/ShopSwitchGroupList'
import ShopFeeSetting from './Components/ShopFeeSetting'

export default {
  name: 'entertainmentShopSwitchIndex',
  components: { ShopFeeSetting, ShopSwitchGroupList, ShopSearch },
  data () {
    return {
      searchLoading: false,
      shopDetail: null,
      searchData: {}
    }
  },
  methods: {
    handleSearch: async function (data = {}) {
      try {
        this.searchLoading = true
        if (data && JSON.stringify(data) !== '{}') {
          this.searchData = {...data}
        }
        const res = await switchSetting.getDrainageSwitch({
          type: 1,
          ...this.searchData
        })
        if (res && res.result === 0 && res.data) {
          const data = {...res.data}
          const equipmentList = [
            data.wwjCount ? `娃娃机${data.wwjCount}台`: '',
            data.dbjCount ? `兑币机${data.dbjCount}台`: ''
          ]
          this.shopDetail = {
            ...res.data,
            equipmentDetail: equipmentList.filter(item => !!item).join('、'),
            openAllGroupsFlag: res.data.openAllGroups,
            openAllGroups: res.data.openAllGroups ? 1: 2,
            setBetaEndTime: ''
          }
          this.$nextTick(() => {
            this.$refs.groupList.operateType = 1
            this.$refs.groupList.search.groupName = ''
            this.$refs.groupList.handleSearch()
          })
        } else {
          this.shopDetail = null
          this.$message.warning('暂无商户信息 请重新搜索')
        }
      } finally {
        this.searchLoading = false
      }
    }
  }
}
</script>

<style scoped>
.page-title{
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}
</style>
