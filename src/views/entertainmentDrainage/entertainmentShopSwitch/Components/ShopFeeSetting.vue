<template>
  <div class="ShopFeeSetting">
    <el-form-item label="开启状态">
      <el-tag :type="shopDetail.openAtLeastOneGroup ? 'success': 'info'">{{ shopDetail.openAtLeastOneGroup ? '已开启': '未开启' }}</el-tag>
    </el-form-item>
    <el-form-item label="开启功能时间">
      {{ shopDetail.updated }}
    </el-form-item>
    <el-form-item label="开始收费时间">
      {{ shopDetail.betaEndTime }}
      <el-button type="text" :disabled="!shopDetail.openAtLeastOneGroup" @click="visible = true">调整收费时间</el-button>
      <!--<el-popover
        placement="right"
        v-model="visible">
        <div style="margin: 0">
          <div class="content">
            <div class="title">收费时间</div>
            <el-date-picker
              v-model="shopDetail.setBetaEndTime"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              :picker-options="pickerOptions"
            />
          </div>
          <div class="footer">
            <el-button size="mini" type="text" @click="visible = false">取消</el-button>
            <el-button type="primary" size="mini" :disabled="!shopDetail.setBetaEndTime" :loading="loading" @click="saveFeeDate">确定</el-button>
          </div>
        </div>
        <el-button type="text" slot="reference" :disabled="!shopDetail.openAtLeastOneGroup">调整收费时间</el-button>
      </el-popover>-->
    </el-form-item>

    <el-dialog
      :visible.sync="visible"
      title="开始收费时间"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="500px"
    >
      <el-form :model="shopDetail" ref="form" label-width="120px">
        <el-form-item label="收费时间" prop="setBetaEndTime">
          <el-date-picker
            v-model="shopDetail.setBetaEndTime"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            :picker-options="pickerOptions"
          />
        </el-form-item>
      </el-form>
      <div class="footer" slot="footer">
        <el-button size="mini" type="text" @click="visible = false">取消</el-button>
        <el-button type="primary" size="mini" :disabled="!shopDetail.setBetaEndTime" :loading="loading" @click="saveFeeDate">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { switchSetting } from '@/api/entertainmentDrainage'

export default {
  name: 'ShopFeeSetting',
  props: {
    shopDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      visible: false,
      loading: false
    }
  },
  computed: {
    pickerOptions: function () {
      const that = this
      return {
        disabledDate (time) {
          if (that.shopDetail && that.shopDetail.updated) {
            return time.getTime() <= new Date(that.shopDetail.updated).getTime() - 24 * 3600 * 1000;
          }
          return false
        }
      }
    }
  },
  methods: {
    saveFeeDate: async function () {
      try {
        this.loading = true
        const res = await switchSetting.updateBetaEndTime({
          betaEndTime: this.shopDetail.setBetaEndTime,
          promotionActivityId: this.shopDetail.promotionActivityId
        })
        if (res && res.result === 0) {
          this.$message.success('保存成功')
          this.$emit('reloadDetail')
          this.visible = false
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
  .content {

  }
  .content .title {
    margin: 10px 0;
  }
  .footer{
    text-align: right;
    margin-top: 10px;
  }
</style>
