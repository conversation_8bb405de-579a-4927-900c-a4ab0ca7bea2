<template>
  <div class="ShopSwitchGroupList">
    <el-form ref="form" inline :model="search" label-width="120px">
      <el-form-item label="场地开启状态">
        <el-switch
          v-model="shopDetail.openAllGroups"
          :active-value="1"
          :inactive-value="2"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="开启"
          inactive-text="关闭"
          @change="batchHandle"
          v-loading="batchLoading"
        />
      </el-form-item>
      <el-form-item label="场地名称">
        <el-input v-model.tirm="search.groupName" placeholder="请输入场地名称" style="width: 200px" />
        <el-button type="primary" icon="el-icon-search" @click="handleSearch" style="margin-left: 30px">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" stripe>
      <el-table-column label="场地名称" prop="groupName" />
      <el-table-column label="娃娃机" prop="wwjCount" sortable />
      <el-table-column label="兑币机" prop="dbjCount" sortable />
      <el-table-column label="状态" prop="openDrainage" sortable>
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.openDrainage"
            :active-value="1"
            :inactive-value="0"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="开启"
            inactive-text="关闭"
            @change="v => changeStatus(scope.row, scope.$index)"
            v-loading="scope.row.loading"
          />
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      @size-change="(v) => { search.pageIndex = 1; search.pageSize = v; getTableData() }"
      @current-change="getTableData"
      :current-page.sync="search.pageIndex"
      background
      :page-sizes="[10, 20, 50, 100]"
      :page-size="search.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total" />
  </div>
</template>

<script>
import { switchSetting } from '@/api/entertainmentDrainage'

export default {
  name: 'ShopSwitchGroupList',
  props: {
    shopDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      search: {
        groupName: '', // 场地名称
        pageIndex: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      operateType: 1, // 1 全部开启 2 全部关闭 3 开启指定场地 4 关闭指定场地
      loading: true,
      batchLoading: false
    }
  },
  created () {
    // this.getTableData()
  },
  methods: {
    handleSearch: function () {
      this.search.pageIndex = 1
      this.getTableData()
    },
    getTableData: async function () {
      try {
        this.loading = true
        const res = await switchSetting.getGroupList({
          ...this.search,
          distributorId: this.shopDetail.distributorId
        })
        if (res && res.result === 0 && res.data) {
          this.tableData = [...res.data.items]
          this.total = res.data.total
        }
      } finally {
        this.loading = false
      }
    },
    changeStatus: async function (row, index) {
      const { openDrainage } = row
      const operateType = openDrainage === 1 ? 3 : 4
      try {
        this.$set(this.tableData[index], 'loading', true)
        const res = await switchSetting.openGroup({
          operateType,
          groupIds: [row.groupId],
          distributorId: this.shopDetail.distributorId
        })
        if (res && res.result === 0) {
          this.$message.success('操作成功')
          this.$emit('reloadDetail')
        } else {
          this.$message.warning('设置失败')
          this.$set(this.tableData[index], 'openDrainage', openDrainage === 1 ? 0: 1)
        }
      } catch (e) {
        this.$set(this.tableData[index], 'openDrainage', openDrainage === 1 ? 0: 1)
      } finally {
        this.$set(this.tableData[index], 'loading', false)
      }
    },
    batchHandle: function (v) {
      let text = ''
      switch (v) {
        case 1:
          text = '开启所有场地'
          break
        case 2:
          text = '关闭所有场地'
          break
      }
      this.$confirm(`确定${text}`, '提示', {
        type: 'warning'
      }).then(async () => {
        try {
          this.batchLoading = true
          const res = await switchSetting.openGroup({
            operateType: v,
            groupIds: [],
            distributorId: this.shopDetail.distributorId
          })
          if (res && res.result === 0) {
            this.$message.success('操作成功')
            await this.getTableData()
            this.$emit('reloadDetail')
          } else {
            this.shopDetail.openAllGroups = v === 1 ? 2: 1
          }
        } catch (e) {
          this.shopDetail.openAllGroups = v === 1 ? 2: 1
        } finally {
          this.batchLoading = false
        }
      }).catch(() => {
        this.shopDetail.openAllGroups = v === 1 ? 2: 1
      })
    }
  }
}
</script>

<style scoped>
  .pagination{
    margin-top: 15px;
  }
</style>
