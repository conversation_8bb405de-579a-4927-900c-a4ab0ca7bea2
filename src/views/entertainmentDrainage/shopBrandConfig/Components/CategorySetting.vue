<template>
  <div class="CategorySetting">
    <el-dialog
      title="分类"
      :visible.sync="visible"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="false"
      append-to-body
    >
      <el-table :data="tableData" border v-loading="loading">
        <el-table-column label="名称" prop="name">
          <template slot-scope="scope">
            <el-input v-if="scope.row.is_edit" v-model="tableData[scope.$index].name" placeholder="请输入分类名称" />
            <span v-else>{{ scope.row.temp_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="250">
          <template slot-scope="scope">
            <el-button v-if="scope.row.is_edit" type="primary" icon="el-icon-files" @click="save(scope.row, scope.$index)">{{ scope.row.name === scope.row.temp_name ? '关闭': '保存' }}</el-button>
            <el-button v-else type="primary" icon="el-icon-edit" @click="scope.row.is_edit = true">编辑</el-button>
            <el-button type="danger" icon="el-icon-delete" @click="del(scope.row)">删除</el-button>
          </template>
        </el-table-column>
        <template slot="append">
          <el-form class="append" inline :model="form" ref="form" :rules="rules">
            <el-form-item label="分类名称:" prop="name">
              <el-input v-model.tirm="form.name" placeholder="请输入分类名称" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="addSave" :loading="form.subLoading" :disabled="!form.name">添加</el-button>
            </el-form-item>
          </el-form>
        </template>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { shopBrandConfig } from '@/api/entertainmentDrainage'

export default {
  name: 'CategorySetting',
  props: {
    categoryType: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      visible: false,
      loading: true,
      tableData: [],
      form: {
        name: '',
        subLoading: false
      },
      rules: {
        name: [{ required: true, message: '请输入分类名称' }]
      },
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList: async function () {
      try {
        this.loading = true
        const res = await shopBrandConfig.getCategoryList(this.categoryType)
        if (res && res.result === 0 && res.data) {
          this.tableData = [...res.data].map(item => ({
            ...item,
            temp_name: item.name,
            is_edit: false
          }))
          this.$emit('setCategoryList', this.tableData)
        }
      } finally {
        this.loading = false
      }
    },
    open: function () {
      this.visible = true
    },
    save: async function (data, index) {
      const { name, temp_name, lyyDrainageCategoryId } = data
      if (name === temp_name && lyyDrainageCategoryId) {
        this.$set(this.tableData[index], 'is_edit', false)
        return
      }
      if (!name || name.replace(/\s/ig, '') === '') {
        this.$message.warning('请输入分类名称')
        return
      }
      const _list = this.tableData.filter((item, i) => item.name === name && index !== i)
      if (_list.length >= 1) {
        this.$message.warning('分类已存在')
        return
      }
      try {
        this.loading = true
        const res = await shopBrandConfig.saveCategory({
          lyyDrainageCategoryId,
          name,
          categoryType: this.categoryType
        })
        if (res.result === 0) {
          this.$message.success(`${lyyDrainageCategoryId ? '修改成功': '新增成功'}`)
          await this.getList()
          this.form = {...this.$options.data().form}
          this.$refs.form.resetFields()
        }
      } finally {
        this.loading = false
      }
    },
    del: async function (data) {
      try {
        const { lyyDrainageCategoryId } = data
        this.loading = true
        const res = await shopBrandConfig.delCategory({
          lyyDrainageCategoryId
        })
        if (res.result === 0) {
          this.$message.success('删除成功')
          await this.getList()
        }
      } finally {

      }
    },
    addSave: function () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.save({
            name: this.form.name,
            temp_name: ''
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
  .append{
    padding: 20px 0 0 20px;
  }
</style>
