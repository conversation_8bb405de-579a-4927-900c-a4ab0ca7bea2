<template>
  <div class="ShopConfig">
    <el-form class="form" inline ref="form">
      <el-form-item label="搜索">
        <el-input v-model="search.lyyDistributorName" placeholder="请输入商户名称" />
      </el-form-item>
      <el-form-item label="">
        <el-input v-model="search.phone" placeholder="请输入商户手机号" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">查询</el-button>
      </el-form-item>
    </el-form>
    <ShopTable
      :loading="loading"
      :table-data="tableData"
      @reload="getTableData"
    />
    <el-pagination
      class="pagination"
      @size-change="(v) => { search.pageIndex = 1; search.pageSize = v; getTableData() }"
      @current-change="getTableData"
      :current-page.sync="search.pageIndex"
      background
      :page-sizes="[10, 20, 50, 100]"
      :page-size="search.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total" />
  </div>
</template>

<script>
import { shopBrandConfig } from '@/api/entertainmentDrainage'
import ShopTable from './ShopTable'
import config from '../../../../assets/js/config'
import { imgDir } from '../dict'

export default {
  name: 'ShopConfig',
  components: { ShopTable },
  data () {
    return {
      search: {
        pageIndex: 1,
        pageSize: 10,
        // lyyDistributorName: ''
      },
      loading: true,
      tableData: [],
      total: 0
    }
  },
  created () {
    this.getTableData()
  },
  methods: {
    handleSearch: function () {
      this.search.pageIndex = 1
      this.getTableData()
    },
    getTableData: async function () {
      try {
        this.loading = true
        const res = await shopBrandConfig.getShopConfigList(this.search)
        if (res && res.result === 0 && res.data) {
          this.tableData = [...res.data.list].map(item => {
            return {
              ...item,
              logoImage: item.logoImageList && item.logoImageList.length > 0 ? `${config.aliyunImgBaseUrl}${item.logoImageList[0].imageUrl}`: ''
            }
          })
          this.total = res.data.total
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped lang="less">
  .ShopConfig {
    .pagination {
      margin-top: 15px
    }
  }
</style>
