<template>
  <div class="SelectPic">
    <el-dialog
      title="选择图片"
      :visible.sync="dialogVisible"
      width="900px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="false"
    >
      <el-form class="tool" inline :model="search">
        <el-form-item label="分类:">
          <el-select v-model="search.lyyDrainageCategoryId" filterable @change="handleSearch">
            <el-option label="不限" value="" />
            <el-option v-for="(item, index) in categoryList" :key="item.lyyDrainageCategoryId" :label="item.name" :value="item.lyyDrainageCategoryId" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-checkbox-group v-if="limit > 1" v-model="ids" @change="changeIds" :max="limit">
        <el-card class="card" shadow="hover" :body-style="{ padding: '0px' }" v-for="(item, index) in tableData" :key="item.lyyDrainageImageId">
          <el-image :src="item.imageUrlFull" class="image" />
          <el-checkbox class="checkbox" :label="item.lyyDrainageImageId" />
        </el-card>
      </el-checkbox-group>
      <el-radio-group v-else v-model="id" @change="changeRadioIds" :max="limit">
        <el-card class="card" shadow="hover" :body-style="{ padding: '0px' }" v-for="(item, index) in tableData" :key="item.lyyDrainageImageId">
          <el-image :src="item.imageUrlFull" class="image" />
          <el-radio class="checkbox" :label="item.lyyDrainageImageId" />
        </el-card>
      </el-radio-group>
      <el-pagination
        class="pagination"
        style="margin-top: 15px"
        @size-change="(v) => { search.pageIndex = 1; search.pageSize = v; getTableData() }"
        @current-change="getTableData"
        :current-page.sync="search.pageIndex"
        background
        :page-sizes="[10, 20, 50, 100]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total" />
      <template slot="footer">
        <div style="text-align: end">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="submit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { categoryType, defaultAttachType, imgDir } from '../dict'
import { shopBrandConfig } from '@/api/entertainmentDrainage'
import config from '@/assets/js/config'

export default {
  name: 'SelectPic',
  data () {
    return {
      shopBrandConfig,
      dialogVisible: false,
      categoryList: [],
      search: {
        pageIndex: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      type: '',
      id: '',
      ids: [],
      limit: 1,
      selectObjArr: []
    }
  },
  async created () {
    try {
      const res = await shopBrandConfig.getCategoryList(categoryType.pic)
      if (res && res.result === 0 && res.data) {
        this.categoryList = [...res.data]
      }
    } catch (e) {

    } finally {
      await this.getTableData()
    }
  },
  methods: {
    open: function (data, limit) {
      this.limit = limit
      this.ids = data.map(item => item.lyyDrainageImageId)
      if (limit === 1) {
        this.id = data.length > 0 ? data[0].lyyDrainageImageId: ''
      }
      this.selectObjArr = [...data]
      this.dialogVisible = true
    },
    handleSearch: function () {
      this.search.pageIndex = 1
      this.getTableData()
    },
    getTableData: async function () {
      try {
        this.loading = true
        const res = await shopBrandConfig.getPicList({
          ...this.search
        })
        if (res && res.result === 0 && res.data) {
          this.tableData = [...res.data.list].map(item => ({
            ...item,
            imageUrlFull: config.aliyunImgBaseUrl + item.imageUrl
          }))
          // console.log('123213', this.tableData)
          this.total = res.data.total
        }
      } catch (e) {

      } finally {
        this.loading = false
      }
    },
    cancel: function () {
      this.dialogVisible = false
    },
    changeRadioIds: function (v) {
      this.changeIds([v])
    },
    changeIds: function (arr) {
      arr.forEach(id => {
        this.tableData.forEach(item => {
          if (item.lyyDrainageImageId === id) {
            const index = this.selectObjArr.findIndex(obj => obj.lyyDrainageImageId === id)
            if (index < 0) {
              // 不存在 则新增
              this.selectObjArr.push(item)
            }
          }
        })
      })
      // 去掉删除的
      const list = [...this.selectObjArr]
      list.forEach((item, index) => {
        if (!arr.includes(item.lyyDrainageImageId)) {
          this.selectObjArr.splice(index, 1)
        }
      })
    },
    submit: function () {
      this.$emit('setPicValue', this.selectObjArr)
      this.cancel()
    }
  }
}
</script>

<style scoped lang="less">
  .card {
    display: inline-block;
    width: 200px;
    margin: 0 10px 10px 0;
    position: relative;
    .image {
      width: 200px;
      height: 200px;
    }
    .checkbox {
      position: absolute;
      top: 5px;
      right: 5px;
      /deep/ .el-checkbox__label,
      /deep/ .el-radio__label{
        display: none;
      }
      /deep/ .el-checkbox__inner,
      /deep/ .el-radio__inner {
        transform: scale(1.5);
      }
    }
  }
</style>
