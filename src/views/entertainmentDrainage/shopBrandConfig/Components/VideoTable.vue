<template>
  <div class="VideoTable">
    <el-table :data="tableData" v-loading="loading">
      <el-table-column label="视频封面" width="200" align="center">
        <template slot-scope="scope">
          <el-image class="poster" :src="scope.row.coverUrlFull" />
        </template>
      </el-table-column>
      <el-table-column label="视频名称" prop="videoName" />
      <el-table-column label="视频分类" prop="lyyDrainageCategoryName"></el-table-column>
      <el-table-column label="上传时间" prop="created" sortable></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="edit(scope.row)">编辑</el-button>
          <el-button type="text" class="danger" icon="el-icon-delete" @click="del(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { shopBrandConfig } from '@/api/entertainmentDrainage'

export default {
  name: 'VideoTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {

    }
  },
  methods: {
    edit: function (data) {
      this.$emit('edit', data)
    },
    del: async function (data) {
      try {
        const res = await shopBrandConfig.delVideo({
          lyyDrainageVideoId: data.lyyDrainageVideoId
        })
        if (res && res.result === 0) {
          this.$message.success('操作成功')
          this.$emit('reload')
        }
      } finally {

      }
    }
  }
}
</script>

<style scoped lang="less">
  .VideoTable {
    margin-bottom: 15px;
  }
  .poster{
    width: 75px;
    height: 50px;
    object-fit: cover;
    cursor: pointer;
  }
  .danger {
    color: red !important;
  }
</style>
