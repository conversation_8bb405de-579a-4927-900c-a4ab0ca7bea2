<template>
  <div class="LabelSetting">
    <el-dialog
      title="标签"
      :visible.sync="visible"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="false"
      append-to-body
    >
      <el-checkbox-group v-model="ids">
        <el-table :data="tableData" border v-loading="loading">
          <el-table-column label="选择" width="80" align="center">
            <template slot-scope="scope">
              <el-checkbox class="checkbox" :label="scope.row.lyyDrainageLabelId" />
            </template>
          </el-table-column>
          <el-table-column label="名称" prop="labelName">
            <template slot-scope="scope">
              <el-input v-if="scope.row.is_edit" v-model="tableData[scope.$index].labelName" placeholder="请输入分类名称" />
              <span v-else>{{ scope.row.temp_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" :width="250">
            <template slot-scope="scope">
              <el-button v-if="scope.row.is_edit" type="primary" icon="el-icon-files" @click="save(scope.row, scope.$index)">{{ scope.row.labelName === scope.row.temp_name ? '关闭': '保存' }}</el-button>
              <el-button v-else type="primary" icon="el-icon-edit" @click="scope.row.is_edit = true">编辑</el-button>
              <el-button type="danger" icon="el-icon-delete" @click="del(scope.row)">删除</el-button>
            </template>
          </el-table-column>
          <template slot="append">
            <el-form class="append" inline :model="form" ref="form" :rules="rules">
              <el-form-item label="标签名称:" prop="labelName">
                <el-input v-model.tirm="form.labelName" placeholder="请输入分类名称" />
              </el-form-item>
              <el-form-item label="默认:" prop="isDefault">
                <el-radio-group v-model="form.isDefault">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="addSave" :loading="form.subLoading" :disabled="!form.labelName">添加</el-button>
              </el-form-item>
            </el-form>
          </template>
        </el-table>
      </el-checkbox-group>
      <template slot="footer">
          <div style="text-align: end">
            <el-button @click="close">关闭</el-button>
            <el-button type="primary" @click="submit">确定</el-button>
          </div>
        </template>
    </el-dialog>
  </div>
</template>

<script>
import { shopBrandConfig } from '@/api/entertainmentDrainage'
import { defaultAttachType } from '../dict'

export default {
  name: 'LabelSetting',
  props: {
  },
  data () {
    return {
      visible: false,
      loading: true,
      tableData: [],
      total: 0,
      search: {
        pageIndex: 1,
        pageSize: 999
      },
      form: {
        labelName: '',
        isDefault: 0,
        subLoading: false
      },
      rules: {
        labelName: [{ required: true, message: '请输入分类名称' }]
      },
      ids: [] // 已选

    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList: async function () {
      try {
        this.loading = true
        const res = await shopBrandConfig.getLabelList(this.search)
        if (res && res.result === 0 && res.data) {
          this.tableData = [...res.data.list].map(item => ({
            ...item,
            temp_name: item.labelName,
            is_edit: false
          }))
          this.total = res.data.total
          this.$emit('setCategoryList', this.tableData)
        }
      } finally {
        this.loading = false
      }
    },
    open: function (data) {
      this.ids = [...data].map(item => item.lyyDrainageLabelId)
      this.visible = true
    },
    save: async function (data, index) {
      const { labelName, temp_name, lyyDrainageLabelId, isDefault } = data
      if (labelName === temp_name && lyyDrainageLabelId) {
        this.$set(this.tableData[index], 'is_edit', false)
        return
      }
      if (!labelName || labelName.replace(/\s/ig, '') === '') {
        this.$message.warning('请输入分类名称')
        return
      }
      const _list = this.tableData.filter((item, i) => item.name === name && index !== i)
      if (_list.length >= 1) {
        this.$message.warning('分类已存在')
        return
      }
      try {
        this.loading = true
        const res = await shopBrandConfig.saveLabel([
          {
            lyyDrainageLabelId,
            labelName,
            isDefault,
            categoryType: defaultAttachType.label
          }
        ])
        if (res.result === 0) {
          this.$message.success(`${lyyDrainageLabelId ? '修改成功': '新增成功'}`)
          await this.getList()
          this.form = {...this.$options.data().form}
          this.$refs.form.resetFields()
        }
      } finally {
        this.loading = false
      }
    },
    del: async function (data) {
      try {
        const { lyyDrainageLabelId } = data
        this.loading = true
        const res = await shopBrandConfig.delLabel({
          lyyDrainageLabelId
        })
        if (res.result === 0) {
          this.$message.success('删除成功')
          await this.getList()
        }
      } finally {

      }
    },
    addSave: function () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.save(this.form)
        }
      })
    },
    close: function () {
      this.visible = true
    },
    submit: function () {
      const arr = [...this.tableData].filter(item => this.ids.includes(item.lyyDrainageLabelId))
      this.$emit('setLabelValue', arr)
      this.visible = false
    }
  }
}
</script>

<style scoped lang="less">
  .checkbox {
    /deep/ .el-checkbox__label {
      display: none;
    }
  }
  .append{
    padding-left: 20px;
    padding-top: 20px;
  }
</style>
