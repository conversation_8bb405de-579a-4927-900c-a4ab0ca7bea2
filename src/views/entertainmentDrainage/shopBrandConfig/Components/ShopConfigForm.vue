<template>
  <div class="ShopConfigForm">
    <el-dialog
      title="商户配置"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="false"
    >
      <el-form v-if="dialogVisible" class="form" :model="form" ref="form" :rules="rules" label-width="120px">
        <el-form-item label="商家logo" prop="logoImageList">
          <div class="btn">
            <el-button type="primary" @click="openSelectPic('logoImageList')">选择图片</el-button>
          </div>
          <div class="list">
            <el-card class="item pic-item" shadow="hover" v-for="(item, index) in form.logoImageList" :key="index">
              <el-image class="pic" :src="prefix + item.imageUrl" lazy />
              <!--<el-button class="btn" type="danger" @click="delImg(1)">删除</el-button>-->
            </el-card>
          </div>
          <!--<upload-img-->
          <!--  v-model="form.logoImageUrl"-->
          <!--  :uploadApi="uploadImgUrl"-->
          <!--/>-->
        </el-form-item>
        <el-form-item label="图片" prop="shopImageList">
          <div class="btn">
            <el-button type="primary" @click="openSelectPic('shopImageList')">选择图片</el-button>
          </div>
          <div class="list">
            <el-card class="item pic-item" shadow="hover" v-for="(item, index) in form.shopImageList" :key="index">
              <el-image class="pic" :src="prefix + item.imageUrl" lazy />
              <el-button class="btn" type="danger" @click="delImg(index)">删除</el-button>
            </el-card>
          </div>
        </el-form-item>
        <el-form-item label="店铺视频" prop="shopVideoList">
          <div class="btn">
            <el-button type="primary" @click="openVideoSelect('shopVideoList')">选择视频</el-button>
          </div>
          <div class="list">
            <el-card class="item video-item" shadow="hover" v-for="(item, index) in form.shopVideoList" :key="index">
              <el-image class="video" :src="prefix + item.coverUrl" lazy />
              <el-button class="btn" type="danger" @click="delVideo(index, 'shopVideoList')">删除</el-button>
            </el-card>
          </div>
        </el-form-item>
        <el-form-item label="达人推荐视频" prop="fashionVideoList">
          <div class="btn">
            <el-button type="primary" @click="openVideoSelect('fashionVideoList')">选择视频</el-button>
          </div>
          <div class="list">
            <el-card class="item video-item" shadow="hover" v-for="(item, index) in form.fashionVideoList" :key="index">
              <el-image class="video" :src="prefix + item.coverUrl" lazy />
              <el-button class="btn" type="danger" @click="delVideo(index, 'fashionVideoList')">删除</el-button>
            </el-card>
          </div>
      </el-form-item>
        <el-form-item label="商家标签" prop="labelList">
          <el-tag class="tag" closable v-for="(item, index) in form.labelList" :key="index" @close="delLabel(index)">{{ item.labelName }}</el-tag>
          <el-button type="text" @click="openLabel">管理</el-button>
        </el-form-item>
        <el-form-item label="人均金额" prop="avgCost">
          ￥<el-input-number v-model="form.avgCost" :min="0" />&nbsp;元
        </el-form-item>
      </el-form>
      <template slot="footer">
        <div style="text-align: end">
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="submit" :loading="subLoading">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <SelectPic ref="pic" @setPicValue="setPicValue" />
    <SelectVideo ref="video" @setValue="setVideoValue" />
    <LabelSetting ref="labelSetting" @setLabelValue="setLabelValue" />
  </div>
</template>

<script>
import UploadImg from '@/components/UploadImg'
import { shopBrandConfig } from '@/api/entertainmentDrainage'
import SelectPic from './SelectPic'
import SelectVideo from './SelectVideo'
import LabelSetting from './LabelSetting'
import config from '../../../../assets/js/config'
import { imgDir } from '../dict'

export default {
  name: 'ShopConfigForm',
  components: { LabelSetting, SelectVideo, SelectPic, UploadImg },
  data () {
    return {
      shopBrandConfig,
      prefix: config.aliyunImgBaseUrl,
      imgDir,
      form: {},
      dialogVisible: false,
      categoryList: [],
      rules: {
        logoImageList: [{ required: true, message: '请选择logo' }],
        shopImageList: [{ required: true, message: '请选择图片' }],
        shopVideoList: [{ required: true, message: '请选择店铺视频' }],
        fashionVideoList: [{ required: true, message: '请选择达人推荐视频' }],
        labelList: [{ required: true, message: '请选择商家标签' }],
      },
      picType: '',
      subLoading: false
    }
  },
  methods: {
    init: function (data) {
      this.form = {...data}
      console.log('....', this.form)
      this.$nextTick(() => {
        this.dialogVisible = true
      })
    },
    delImg: function (index) {
      this.form.shopImageList.splice(index, 1)
    },
    delVideo: function (index, key) {
      this.form[key].splice(index, 1)
    },
    delLabel: function (index) {
      this.form.labelList.splice(index, 1)
    },
    openLabel: function () {
      this.$refs.labelSetting.open(this.form.labelList)
    },
    openSelectPic: function (type) {
      let limit = 1
      this.picType = type
      switch (type) {
        case 'logoImageList':
          limit = 1
          break
        case 'shopImageList':
          limit = 999
          break
        default:
          limit = 1
          break
      }
      this.$refs.pic.open(this.form[type], limit)
    },
    openVideoSelect: function (type) {
      const arr = [...this.form[type]]
      this.$refs.video.open({
        type,
        value: arr
      })
    },
    setPicValue: function (arr) {
      this.form[this.picType] = [...arr]
    },
    setVideoValue: function ({ type, value }) {
      this.form[type] = value
      console.log(this.form)
    },
    setLabelValue: function (arr) {
      this.form.labelList = [...arr]
      console.log('this.form.labelList', arr)
    },
    close: function () {
      this.form = {...this.$options.data().form}
      this.dialogVisible = false
    },
    submit: function () {
      // console.log(this.form)
      this.$refs.form.validate(async valid => {
        if (valid) {
          try {
            this.subLoading = true
            const {
              lyyDrainageShopConfigId, lyyDistributorId, avgCost, logoImageList,
              shopImageList, shopVideoList, fashionVideoList, labelList
            } = this.form
            const params = {
              lyyDrainageShopConfigId,
              lyyDistributorId,
              avgCost,
              shopImageList,
              shopVideoList,
              fashionVideoList,
              labelList,
              logoImageList
            }
            const res = await shopBrandConfig.saveShopConfig(params)
            if (res && res.result === 0) {
              this.$message.success('保存成功')
              this.close()
              this.$emit('reload')
            }
          } finally {
            this.subLoading = false
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
  .form {
    max-height: 600px;
    overflow-y: auto;
    .list {
      margin-top: 10px;
    }
    .item {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
      position: relative;
      /deep/ .el-card__body {
        padding: 0;
        position: relative;
      }
      .btn {
        width: 100%;
        position: absolute;
        bottom: 12px;
        display: none;
      }
      &.pic-item {
        width: 100px;
        height: 100px;
        .pic {
          width: 100px;
          height: 100px;
        }
      }
      &.video-item {
        width: 200px;
        height: 120px;
        .video {
          width: 200px;
          height: 120px;
        }
      }
      &:hover {
        .btn {
          display: block;
        }
      }
    }
    .tag + .tag {
      margin-left: 10px;
    }
  }
</style>
