<template>
  <div class="ShopTable">
    <el-table :data="tableData" v-loading="loading">
      <el-table-column label="商户ID" prop="lyyDistributorId"></el-table-column>
      <el-table-column label="商户名称" prop="lyyDistributorName"></el-table-column>
      <el-table-column label="商家logo" prop="logoImage">
        <template slot-scope="scope">
          <el-image class="img" :src="scope.row.logoImage" />
        </template>
      </el-table-column>
      <el-table-column label="图片" prop="imageCount" sortable></el-table-column>
      <el-table-column label="店铺视频" prop="shopVideoCount" sortable></el-table-column>
      <el-table-column label="达人视频" prop="fashionVideoCount" sortable></el-table-column>
      <el-table-column label="人均金额" prop="avgCost" sortable></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" :loading="scope.row.loading" @click="edit(scope.row, scope.$index)">编辑</el-button>
          <el-popover
            placement="top"
            width="160"
            v-model="scope.row.visible">
            <p style="margin-bottom: 10px">确定删除此商家配置吗</p>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" type="text" @click="scope.row.visible = false">取消</el-button>
              <el-button type="danger" size="mini" @click="del(scope.row)">确定</el-button>
            </div>
            <el-button slot="reference" type="text" class="danger" icon="el-icon-delete">删除</el-button>
          </el-popover>

        </template>
      </el-table-column>
    </el-table>

    <ShopConfigForm
      ref="shopConfigForm"
      @reload="$emit('reload')"
    />
  </div>
</template>

<script>
import { shopBrandConfig } from '../../../../api/entertainmentDrainage'
import ShopConfigForm from './ShopConfigForm'

export default {
  name: 'ShopTable',
  components: { ShopConfigForm },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: () => []
    },
  },
  methods: {
    edit: async function (data, index) {
      try {
        this.$set(this.tableData[index], 'loading', true)
        const res = await shopBrandConfig.getShopConfigDetail(data.lyyDrainageShopConfigId)
        if (res && res.result === 0 && res.data) {
          this.$refs.shopConfigForm.init(res.data)
        }
      } finally {
        this.$set(this.tableData[index], 'loading', false)
      }

      // this.$refs.shopConfigForm.init(data)
    },
    del: async function (data) {
      const res = await shopBrandConfig.delShopConfig({
        lyyDrainageShopConfigId: data.lyyDrainageShopConfigId
      })
      if (res && res.result === 0) {
        this.$message.success('操作成功')
        this.$emit('reload')
      }
    }
  }
}
</script>

<style scoped>
  .danger{
    color: red !important;
  }
  .img {
    width: 50px;
    height: 50px;
  }
</style>
