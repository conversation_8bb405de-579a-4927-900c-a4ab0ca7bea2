<template>
  <div class="PicConfig">
    <el-form class="tool" inline :model="search">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="uploadImg">上传图片</el-button>
      </el-form-item>
      <el-form-item label="分类:">
        <el-select v-model="search.lyyDrainageCategoryId" filterable @change="handleSearch">
          <el-option label="不限" value="" />
          <el-option v-for="(item, index) in categoryList" :key="item.lyyDrainageCategoryId" :label="item.name" :value="item.lyyDrainageCategoryId" />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="content" v-loading="loading">
      <template v-if="!loading && tableData.length > 0">
        <div class="pic-list">
          <div class="pic-item" v-for="(item, index) in tableData" :key="item.lyyDrainageImageId">
            <el-card shadow="hover">
              <el-image class="pic" :src="prefix + item.imageUrl" lazy />
              <el-button class="btn" type="danger" @click="delImg(item, index)">删除</el-button>
            </el-card>
          </div>
        </div>
        <el-pagination
          class="pagination"
          @size-change="(v) => { search.pageIndex = 1; search.pageSize = v; getList() }"
          @current-change="getList"
          :current-page.sync="search.pageIndex"
          background
          :page-sizes="[10, 20, 50, 100]"
          :page-size="search.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total" />
      </template>
      <div v-else class="empty">暂无数据</div>
    </div>

    <el-dialog
      title="上传图片"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="false"
    >
      <el-form :model="form" ref="form" v-if="dialogVisible" label-width="120px" :rules="rules">
        <el-form-item label="分类:" prop="lyyDrainageCategoryId">
          <el-select v-model="form.lyyDrainageCategoryId" filterable>
            <el-option v-for="(item, index) in categoryList" :key="item.lyyDrainageCategoryId" :label="item.name" :value="item.lyyDrainageCategoryId" />
          </el-select>
          <el-button type="text" @click="openCategory">管理分类</el-button>
        </el-form-item>
        <el-form-item label="默认:" prop="isDefault">
          <el-radio-group v-model="form.isDefault">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
         <el-form-item label="类型:" prop="defaultAttachType" v-if="form.isDefault === 1">
            <el-radio-group v-model="form.defaultAttachType">
              <el-radio :label="defaultAttachType.pic">图片</el-radio>
              <el-radio :label="defaultAttachType.logo">logo</el-radio>
            </el-radio-group>
          </el-form-item>
        <el-form-item label="上传图片:" prop="picList">
          <UploadImg
            v-model="form.picList"
            :uploadApi="shopBrandConfig.uploadImgUrl"
            :limit="999"
            :imgDir="imgDir"
          />
        </el-form-item>
      </el-form>
      <template slot="footer">
        <div style="text-align: end">
          <el-button @click="close">关闭</el-button>
          <el-button type="primary" @click="submit" :loading="subLoading">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <category-setting
      ref="categorySetting"
      :categoryType="categoryType.pic"
      @setCategoryList="setCategoryList"
    />
  </div>
</template>

<script>
import UploadImg from '@/components/UploadImg'
import { shopBrandConfig } from '@/api/entertainmentDrainage'
import { categoryType, defaultAttachType, imgDir, videoDir } from '../dict'
import CategorySetting from './CategorySetting'
import config from '@/assets/js/config'

export default {
  name: 'PicConfig',
  components: { CategorySetting, UploadImg },
  data () {
    return {
      prefix: config.aliyunImgBaseUrl,
      shopBrandConfig,
      categoryType,
      defaultAttachType,
      imgDir,
      videoDir,
      categoryList: [],
      search: {
        pageIndex: 1,
        pageSize: 10,
        lyyDrainageCategoryId: ''
      },
      loading: true,
      tableData: [],
      total: 0,
      dialogVisible: false,
      form: {
        isDefault: 0, // 是否默认
        defaultAttachType: defaultAttachType.pic, // 附件类型
        lyyDrainageCategoryId: '', // 分类
        picList: []
      },
      rules: {
        lyyDrainageCategoryId: [{ required: true, message: '请选择分类' }],
        picList: [{ required: true, message: '请上传图片' }],
      },
      subLoading: false
    }
  },
  created () {
    this.getList()
  },
  methods: {
    handleSearch: function () {
      this.search.pageIndex = 1
      this.getList()
    },
    getList: async function () {
      try {
        this.loading = true
        const res = await shopBrandConfig.getPicList(this.search)
        if (res && res.result === 0 && res.data) {
          this.tableData = [...res.data.list]
          this.total = res.data.total
        }
      } finally {
        this.loading = false
      }
    },
    uploadImg: function () {
      this.form = {...this.$options.data().form}
      this.$nextTick(() => {
        this.dialogVisible = true
      })
    },
    delImg: async function (data, index) {
      try {
        const res = await shopBrandConfig.delPic({ lyyDrainageImageId: data.lyyDrainageImageId })
        if (res && res.result === 0) {
          this.$message.success('删除成功')
          await this.getList()
        }
      } finally {

      }
    },
    submit: function () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          try {
            this.subLoading = true
            console.log(this.form.picList)
            const params = this.form.picList.map(url => ({
              imageUrl: url,
              isDefault: this.form.isDefault,
              defaultAttachType: this.form.defaultAttachType,
              lyyDrainageCategoryId: this.form.lyyDrainageCategoryId
            }))
            const res = await shopBrandConfig.savePic(params)
            if (res && res.result === 0) {
              this.$message.success('保存成功')
              await this.getList()
              this.close()
            }
          } finally {
            this.subLoading = false
          }
        }
      })
    },
    close: function () {
      this.form = {...this.$options.data().form}
      this.$refs.form.resetFields()
      this.$nextTick(() => {
        this.dialogVisible = false
      })
    },
    setCategoryList: function (arr) {
      this.categoryList = [...arr]
    },
    openCategory: function () {
      this.$refs.categorySetting.open()
    }
  }
}
</script>

<style scoped lang="less">
  .content {
    max-height: 800px;
    overflow-y: auto;
    .pic-list {
      .pic-item {
        display: inline-block;
        width: 200px;
        height: 200px;
        position: relative;
        margin-right: 14px;
        margin-bottom: 14px;
        /deep/ .el-card__body {
          padding: 0;
        }
        .pic {
          width: 200px;
          height: 200px;
          display: block;
        }
        .btn {
          width: 100%;
          position: absolute;
          bottom: 0;
          display: none;
        }
        &:hover {
          .btn {
            display: block;
          }
        }
      }
    }
  }
</style>
