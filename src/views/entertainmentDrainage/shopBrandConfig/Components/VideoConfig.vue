<template>
  <div class="VideoConfig">
    <el-form class="tool" inline :model="search">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="uploadFile">上传视频</el-button>
      </el-form-item>
      <el-form-item label="分类:">
        <el-select v-model="search.lyyDrainageCategoryId" filterable @change="handleSearch">
          <el-option label="不限" value="" />
          <el-option v-for="(item, index) in categoryList" :key="item.lyyDrainageCategoryId" :label="item.name" :value="item.lyyDrainageCategoryId" />
        </el-select>
      </el-form-item>
    </el-form>
    <VideoTable
      :tableData="tableData"
      :loading="loading"
      @reload="getList"
      @edit="edit"
    />
    <el-pagination
      class="pagination"
      @size-change="(v) => { search.pageIndex = 1; search.pageSize = v; getList() }"
      @current-change="getList"
      :current-page.sync="search.pageIndex"
      background
      :page-sizes="[10, 20, 50, 100]"
      :page-size="search.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total" />
    <VideoForm
      ref="form"
      @reload="getList"
      @openCategory="openCategory"
      :categoryList="categoryList"
    />
    <category-setting
      ref="categorySetting"
      :categoryType="categoryType.video"
      @setCategoryList="setCategoryList"
    />
  </div>
</template>

<script>
import { shopBrandConfig } from '@/api/entertainmentDrainage'
import VideoForm from './VideoForm'
import CategorySetting from './CategorySetting'
import { categoryType, imgDir, videoDir } from '../dict'
import VideoTable from './VideoTable'
import config from '../../../../assets/js/config'

export default {
  name: 'VideoConfig',
  components: { VideoTable, CategorySetting, VideoForm },
  data () {
    return {
      shopBrandConfig,
      categoryType,
      search: {
        pageIndex: 1,
        pageSize: 10,
        lyyDrainageCategoryId: ''
      },
      categoryList: [],
      loading: true,
      tableData: [],
      total: 0,
      dialogVisible: false,
      rules: {
        lyyDrainageCategoryId: [{ required: true, message: '请选择分类' }],
        videoUrl: [{ required: true, message: '请上传视频' }],
        coverUrl: [{ required: true, message: '请上传封面' }],
        videoName: [{ required: true, message: '请输入视频名称' }]
      },
      subLoading: false
    }
  },
  created () {
    this.getList()
  },
  methods: {
    handleSearch: function () {
      this.search.pageIndex = 1
      this.getList()
    },
    getList: async function () {
      try {
        this.loading = true
        const res = await shopBrandConfig.getVideoList(this.search)
        if (res && res.result === 0 && res.data) {
          this.tableData = [...res.data.list].map(item => ({
            ...item,
            coverUrlFull: `${config.aliyunImgBaseUrl}${item.coverUrl}`,
            videoUrlFull: `${config.aliyunImgBaseUrl}${item.videoUrl}`,
            // videoUrlFull: `http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4`,
            lyyDrainageCategoryName: this.findCategoryName(item.lyyDrainageCategoryId)
          }))
          this.total = res.data.total
        }
      } finally {
        this.loading = false
      }
    },
    findCategoryName: function (id) {
      const obj = this.categoryList.find(item => item.lyyDrainageCategoryId === id)
      return obj ? obj.name: '未知分类'
    },
    uploadFile: function () {
      this.$refs.form.$nextTick(() => {
        this.$refs.form.init()
      })
    },
    setCategoryList: function (arr) {
      this.categoryList = [...arr]
    },
    openCategory: function () {
      this.$refs.categorySetting.open()
    },
    edit: function (data) {
      this.$refs.form.init(data)
    }
  }
}
</script>

<style scoped>

</style>
