<template>
  <div class="SelectVideo">
    <el-dialog
      title="标签"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="false"
    >
      <el-form class="tool" inline :model="search">
        <el-form-item label="分类:">
          <el-select v-model="search.lyyDrainageCategoryId" filterable @change="handleSearch">
            <el-option label="不限" value="" />
            <el-option v-for="(item, index) in categoryList" :key="item.lyyDrainageCategoryId" :label="item.name" :value="item.lyyDrainageCategoryId" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-checkbox-group v-model="form.ids" @change="changeIds">
        <el-table :data="tableData" border max-height="600px">
          <el-table-column label="视频封面" prop="coverUrlFull">
            <template slot-scope="scope">
              <el-image class="img" :src="scope.row.coverUrlFull" />
            </template>
          </el-table-column>
          <el-table-column label="视频名称" prop="videoName"></el-table-column>
          <el-table-column label="视频分类" prop="lyyDrainageCategoryId">
            <template slot-scope="scope">
              {{ getLyyDrainageCategoryName(scope.row.lyyDrainageCategoryId) }}
            </template>
          </el-table-column>
          <el-table-column label="选中" :width="80" align="center">
            <template slot-scope="scope">
              <el-checkbox class="checkbox" :label="scope.row.lyyDrainageVideoId" />
            </template>
          </el-table-column>
        </el-table>
      </el-checkbox-group>
      <el-pagination
        class="pagination"
        style="margin-top: 15px"
        @size-change="(v) => { search.pageIndex = 1; search.pageSize = v; getTableData() }"
        @current-change="getTableData"
        :current-page.sync="search.pageIndex"
        background
        :page-sizes="[10, 20, 50, 100]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total" />
      <template slot="footer">
        <div style="text-align: end">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="submit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { shopBrandConfig } from '@/api/entertainmentDrainage'
import { categoryType, imgDir } from '../dict'
import config from '@/assets/js/config'

export default {
  name: 'SelectVideo',
  data () {
    return {
      shopBrandConfig,
      dialogVisible: false,
      categoryList: [],
      form: {
        type: '',
        value: [],
        ids: []
      },
      search: {
        pageIndex: 1,
        pageSize: 10,
        lyyDrainageCategoryId: ''
      },
      tableData: [],
      total: 0
    }
  },
  async created () {
    try {
      const res = await shopBrandConfig.getCategoryList(categoryType.video)
      if (res && res.result === 0 && res.data) {
        this.categoryList = [...res.data]
      }
    } catch (e) {

    } finally {
      await this.getTableData()
    }
  },
  computed: {
    getLyyDrainageCategoryName: function () {
      return id => {
        const index = this.categoryList.findIndex(item => item.lyyDrainageCategoryId === id)
        return index === -1 ? '未知类型': this.categoryList[index].name
      }
    }
  },
  methods: {
    open: function (data) {
      this.form = {
        ...data,
        ids: data.value.map(item => item.lyyDrainageVideoId)
      }
      // console.log('this.form', this.form)
      this.dialogVisible = true
    },
    handleSearch: function () {
      this.search.pageIndex = 1
      this.getTableData()
    },
    getTableData: async function () {
      try {
        this.loading = true
        const res = await shopBrandConfig.getVideoList(this.search)
        if (res && res.result === 0 && res.data) {
          this.tableData = [...res.data.list].map(item => ({
            ...item,
            coverUrlFull: config.aliyunImgBaseUrl + item.coverUrl
          }))
          console.log(this.tableData)
          this.total = res.data.total
        }
      } catch (e) {

      } finally {
        this.loading = false
      }
    },
    cancel: function () {
      this.form = {...this.$options.data().form}
      this.dialogVisible = false
    },
    changeIds: function (arr) {
      arr.forEach(id => {
        this.tableData.forEach(item => {
          if (item.lyyDrainageVideoId === id) {
            const index = this.form.value.findIndex(obj => obj.lyyDrainageVideoId === id)
            if (index < 0) {
              // 不存在 则新增
              this.form.value.push(item)
            }
          }
        })
      })
      // 去掉删除的
      const list = [...this.form.value]
      list.forEach((item, index) => {
        if (!arr.includes(item.lyyDrainageVideoId)) {
          this.form.value.splice(index, 1)
        }
      })
    },
    submit: function () {
      // console.log(this.form)
      this.$emit('setValue', this.form)
      this.cancel()
    }
  }
}
</script>

<style scoped lang="less">
  .img{
    width: 75px;
    height: 50px;
  }
  .SelectVideo {

    .checkbox {
      /deep/ .el-checkbox__label {
        display: none !important;
      }
    }
  }
</style>
