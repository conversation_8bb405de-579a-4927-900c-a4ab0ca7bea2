<template>
  <div class="VideoForm">
     <el-dialog
       :title="form.lyyDrainageVideoId ? '编辑视频': '上传视频'"
       :visible.sync="dialogVisible"
       width="800px"
       :close-on-click-modal="false"
       :close-on-press-escape="false"
       :destroy-on-close="false"
     >
        <el-form v-if="dialogVisible" label-width="120px" :model="form" ref="form" :rules="rules">
          <el-form-item label="分类:" prop="lyyDrainageCategoryId">
            <el-select v-model="form.lyyDrainageCategoryId" filterable style="width: 300px">
              <el-option v-for="(item, index) in categoryList" :key="item.lyyDrainageCategoryId" :label="item.name" :value="item.lyyDrainageCategoryId" />
            </el-select>
            <el-button type="text" @click="openCategory">管理分类</el-button>
          </el-form-item>
          <el-form-item label="是否默认:" prop="isDefault">
            <el-radio-group v-model="form.isDefault">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="类型:" prop="defaultAttachType" v-if="form.isDefault === 1">
            <el-radio-group v-model="form.defaultAttachType">
              <el-radio :label="defaultAttachType.video">店铺视频</el-radio>
              <el-radio :label="defaultAttachType.recommend">达人推荐视频</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="视频名称:" prop="videoName">
            <el-input v-model="form.videoName" placeholder="请输入视频名称" style="width: 400px" />
          </el-form-item>
          <el-form-item label="视频封面:" prop="coverUrl">
            <upload-img
              v-model="form.coverUrl"
              :uploadApi="shopBrandConfig.uploadImgUrl"
              :imgDir="imgDir"
            />
          </el-form-item>
          <el-form-item label="上传视频:" prop="videoUrl">
            <upload-file
              style="width: 400px"
              v-model="form.videoUrl"
              :uploadApi="shopBrandConfig.uploadVideo"
              accept=".mp4,.3gp,.m3u8"
              :autoUpload="true"
              :fileName="form.videoName"
              :fileDir="videoDir"
            />
          </el-form-item>
        </el-form>
        <template slot="footer">
          <div style="text-align: end">
            <el-button @click="close">关闭</el-button>
            <el-button type="primary" @click="submit" :loading="subLoading">保存</el-button>
          </div>
        </template>
     </el-dialog>
  </div>
</template>

<script>
import { defaultAttachType, imgDir, videoDir } from '../dict'
import { uploadImgUrl } from '@/api/entertainmentDrainage'
import { shopBrandConfig } from '@/api/entertainmentDrainage'
import UploadImg from '@/components/UploadImg'
import UploadFile from '@/components/UploadFile'

export default {
  name: 'VideoForm',
  components: { UploadFile, UploadImg },
  props: {
    categoryList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      dialogVisible: false,
      uploadImgUrl,
      shopBrandConfig,
      defaultAttachType,
      imgDir,
      videoDir,
      form: {
        isDefault: 0, // 是否默认
        defaultAttachType: defaultAttachType.video, // 附件类型
        lyyDrainageCategoryId: '', // 分类
        coverUrl: '', // 封面图
        videoUrl: '', // 视频链接
        videoName: '' // 视频名称
      },
      rules: {
        lyyDrainageCategoryId: [{ required: true, message: '请选择视频分类' }],
        coverUrl: [{ required: true, message: '请上传封面图' }],
        videoUrl: [{ required: true, message: '请上传视频' }],
        videoName: [{ required: true, message: '请输入视频名称' }],
      },
      subLoading: false
    }
  },
  methods: {
    openCategory: function () {
      this.$emit('openCategory')
    },
    init: function (data) {
      if (!data) {
        this.form = {...this.$options.data().form}
        this.dialogVisible = true
        this.$nextTick(() => {
          this.$refs.form.resetFields()
        })
      } else {
        this.form = {...data, defaultAttachType: defaultAttachType.video}
        console.log(this.form)
        this.dialogVisible = true
      }
    },
    close: function () {
      this.form = {...this.$options.data().form}
      this.$refs.form.resetFields()
      this.$nextTick(() => {
        this.dialogVisible = false
      })
    },
    submit: function () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          try {
            this.subLoading = true
            const res = await shopBrandConfig.saveVideo([this.form])
            if (res && res.result === 0) {
              this.$message.success('保存成功')
              this.close()
              this.$emit('reload')
            }
          } finally {
            this.subLoading = false
          }
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
