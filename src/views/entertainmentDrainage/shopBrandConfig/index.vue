<template>
  <div class="shopBrandConfigIndex">
    <div class="page-title">商家品牌配置</div>
    <el-tabs type="border-card" v-model="active">
      <el-tab-pane label="商户管理" lazy name="shop">
        <ShopConfig v-if="active === 'shop'" />
      </el-tab-pane>
      <el-tab-pane label="图片管理" lazy name="pic">
        <PicConfig v-if="active === 'pic'" />
      </el-tab-pane>
      <el-tab-pane label="视频管理" lazy name="video">
        <VideoConfig v-if="active === 'video'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ShopConfig from './Components/ShopConfig'
import PicConfig from './Components/PicConfig'
import VideoConfig from './Components/VideoConfig'
export default {
  name: 'shopBrandConfigIndex',
  components: { VideoConfig, PicConfig, ShopConfig },
  data () {
    return {
      active: 'shop'
    }
  }
}
</script>

<style scoped lang="less">
  .page-title{
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
  }
</style>
