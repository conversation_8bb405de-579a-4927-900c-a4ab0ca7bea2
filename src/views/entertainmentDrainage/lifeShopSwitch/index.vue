<template>
  <div class="lifeShopSwitchIndex">
    <div class="page-title">生活设备导流开关设置</div>
    <ShopSearch @search="handleSearch" :searchLoading="searchLoading" />
     <el-form v-if="detail" :model="detail" label-width="120px">
      <el-form-item label="商户账号：">{{ detail.phone }}</el-form-item>
      <el-form-item label="商户ID：">{{ detail.distributorId }}</el-form-item>
      <el-form-item label="商户名称：">{{ detail.adOrgName }}</el-form-item>
      <el-form-item label="生活设备派券：">
        <el-switch
          style=""
          v-model="detail.drainageDistributeSwitch"
          active-value="1"
          inactive-value="0"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="开启"
          inactive-text="关闭"
          @change="changeStatus"
          :loading="postLoading"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { switchSetting } from '@/api/entertainmentDrainage'
import ShopSearch from '../ComComponents/ShopSearch'

export default {
  name: 'lifeShopSwitchIndex',
  components: { ShopSearch },
  data () {
    return {
      searchLoading: false,
      detail: null,
      postLoading: false
    }
  },
  methods: {
    handleSearch: async function (data) {
      try {
        this.searchLoading = true
        const res = await switchSetting.getDrainageSwitch({
          ...data,
          type: 0,
        })
        if (res && res.result === 0 && res.data) {
          this.detail = {...res.data}
        } else {
          this.detail = null
          this.$message.warning('暂无商户信息 请重新搜索')
        }
      } finally {
        this.searchLoading = false
      }
    },
    changeStatus: function (v) {
      console.log(v)
      const text = v === '1' ? '开启': '关闭'
      this.$confirm(`确定${text}此商户生活设备派券`, '提示', {
        type: 'warning'
      }).then(async () => {
        try {
          this.postLoading = true
          const res = await switchSetting.openLifeDrainageSwitch({
            distributorId: this.detail.distributorId,
            drainageDistributeSwitch: this.detail.drainageDistributeSwitch
          })
          if (res && res.result === 0) {
            this.$message.success(`${text}成功`)
          } else {
            this.detail.drainageDistributeSwitch = v === '1' ? '0': '1'
          }
        } catch (e) {
          this.detail.drainageDistributeSwitch = v === '1' ? '0': '1'
        } finally {
          this.postLoading = false
        }
      }).catch(e => {
        this.detail.drainageDistributeSwitch = v === '1' ? '0': '1'
      })
    }
  }
}
</script>

<style scoped>
.page-title{
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}
</style>
