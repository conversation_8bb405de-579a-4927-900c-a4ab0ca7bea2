<!-- 设备离线跟踪 -->
<template>
  <div class="equipmentOfflineSituation">
    <p class="page-tit">设备离线跟踪</p>
    <el-card class="queryList">
      <searchModule :search-list="searchList" @returnData="returnData" :searchFormData="searchFormData"></searchModule>
      <div class="btn-box">
        <el-button type="primary" @click="searchFun(1)">查询</el-button>
        <!-- <el-button type="primary">刷新</el-button>
        <el-button type="primary" @click="exportEquipmentOfflineSituationFn">导出</el-button> -->
      </div>
      <p class="card-tit">设备近{{dateList.length}}天离线次数
        <el-button class="right" type="text" size="medium" @click="exportEquipmentOfflineSituationFn()">导出数据</el-button>
        <span class="right line">|</span>
        <el-button type="text" size="medium" class="refreshBtn right" @click="searchFun()">刷新</el-button>
      </p>
      <el-table
        ref="multipleTable"
        :data="list"
        height="600"
        border
        v-loading="pager.loading"
        tooltip-effect="dark"
        style="width: 100%;margin-top:10px">
        <template v-for="item in tableData">
          <el-table-column
            :label="item.label"
            :width="item.width?item.width:'80'">
            <template slot-scope="scope">
              <template v-if="item.solt">
                <span v-if="item.solt=='cardStatus'">
                  {{ cardStatusList[scope.row.cardStatus]}}
                </span>
                <span v-if="item.solt=='iccidUseAmount'">
                  {{ scope.row.iccidUseAmount?Math.round(scope.row.iccidUseAmount/10.24)/100:''}}
                </span>
              </template>
              <span @click="item.fn?itemClick(scope.row,item.fn):''" :class="item.fn?'blue':''" v-else>{{ item.key=='offlineCheck'?'查看':scope.row[item.key] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          :label="this.dateList.length+'天累计离线次数'"
          >
          <template slot-scope="scope">
            {{scope.row.totalOfflineCount}}
          </template>
        </el-table-column>
        <template v-for="item in dateList">
          <el-table-column
            :label="item"
            width="80">
            <template slot-scope="scope">
              {{scope.row[item]}}
            </template>
          </el-table-column>
        </template>
      </el-table>
      <div class="pagination margin-top-10">
        <el-pagination
          background
          @size-change="handleSizeChange"
          :current-page="pager.page"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="currentChange"
          :page-size="pager.size"
          :total="pager.total"
        ></el-pagination>
      </div>
    </el-card>
    <equipmentOfflineDetail ref="equipmentOfflineDetail" :itemData="itemData"/>
  </div>
</template>

<script>
import searchModule from "../trafficCard/child/searchModule";
import { getEquipmentOfflineSituation,exportEquipmentOfflineSituation} from '@/api/equipmentManage/equipmentManage.js';
import { materielUploadEquipmentType} from '@/api/materielUpload';
import { parseTime } from '@/utils/index';
import equipmentOfflineDetail from "./child/equipmentOfflineDetail";
export default {
  name:'equipmentOfflineSituation',
  data () {
    return {
      searchFormData:{
        equipmentValueOrEquipmentRemark:null,
        equipmentTypeId:null,
        equipmentLabelName:null,
        distributorId:null,
        distributorAccount:null,
        iccid:null,
        date:null,
        cardStatus:null,
        provinceId:null,
        cityId:null,
        districtId:null
      },
      cardStatusList:{
        1:'待激活',
        2:'已激活',
        4:'停机',
        6:'可测试',
        7:'库存',
        8:'预销户'
      },
      searchList: [
        {
          type: 'input', // 检索类型,输入框
          label: '设备编号', // label
          placeholder: '请输入', // 占位符
          valueName: 'equipmentValueOrEquipmentRemark', // 返回对应值名
        },{
          type: 'select', // 检索类型,输入框
          label: '设备类型', // label
          placeholder: '请选择', // 占位符
          valueName: 'equipmentTypeId', // 返回对应值名
          options:this.getEquType()
        },{
          type: 'area', // 检索类型,输入框
          label: '设备省市区', // label
          placeholder: '', // 占位符
          province: 'provinceId', // 返回对应值名
          city: 'cityId', // 返回对应值名
          district: 'districtId' // 返回对应值名
        },{
          type: 'input', // 检索类型,输入框
          label: '设备标签', // label
          placeholder: '请输入', // 占位符
          valueName: 'equipmentLabelName', // 返回对应值名
        },{
          type: 'date', // 检索类型,输入框
          label: '时间段', // label
          placeholder: '', // 占位符
          valueName: 'date', // 返回对应值名
          // 禁用当前之后的日期
          pickerOptions: {
            disabledDate(time) {
              const item_time = time.getTime();
              const now_time = Date.now();
              return item_time > now_time||item_time<now_time-3600 * 1000 * 24 * 30;
            }
          }
        },{
          type: 'input', // 检索类型,输入框
          label: '商家账号', // label
          placeholder: '请输入', // 占位符
          valueName: 'distributorAccount', // 返回对应值名
        },{
          type: 'input', // 检索类型,输入框
          label: '商家ID', // label
          placeholder: '请输入', // 占位符
          valueName: 'distributorId', // 返回对应值名
        },{
          type: 'input', // 检索类型,输入框
          label: 'ICCID', // label
          placeholder: '请输入', // 占位符
          valueName: 'iccid', // 返回对应值名
        },{
          type: 'select', // 检索类型,输入框
          label: '流量卡状态', // label
          placeholder: '', // 占位符
          valueName: 'cardStatus', // 返回对应值名
          options: [
            { label: "全部", value: null },
            { label: "待激活", value: 1 },
            { label: "已激活", value: 2 },
            { label: "停机", value: 4 },
            { label: "可测试", value: 6 },
            { label: "库存", value: 7 },
            { label: "预销户", value: 8 }
          ]
        }
      ],
      list:[],
      pager:{
        page:1,
        size:20,
        total:0,
        loading:false
      },
      tableData:[{
        label:'设备类型',
        width:'80',
        key:'equipmentTypeName'
      },{
        label:'设备编号',
        width:'80',
        key:'equipmentValue'
      },
      // {
      //   label:'子设备数',
      //   width:'80',
      //   key:'equipmentValue'
      // },
      {
        label:'设备备注',
        width:'80',
        key:'equipmentRemark'
      },{
        label:'设备所属商家',
        width:'80',
        key:'distributorName'
      },{
        label:'商家账号',
        width:'80',
        key:'distributorAccount'
      },{
        label:'商家ID',
        width:'80',
        key:'distributorId'
      },{
        label:'设备标签',
        width:'80',
        key:'equipmentLabelName'
      },{
        label:'设备所在省市区',
        width:'80',
        key:'equipmentAddress'
      },{
        label:'当前场地名称',
        width:'80',
        key:'equipmentGroupName'
      },{
        label:'当前流量卡(ICCID)',
        width:'80',
        key:'iccid'
      },{
        label:'流量卡状态',
        width:'80',
        key:'cardStatus',
        solt:'cardStatus'
      },{
        label:'流量池编码',
        width:'80',
        key:'iccidGroupId'
      },{
        label:'当月流量使用情况(M)',
        width:'80',
        key:'iccidUseAmount',
        solt:'iccidUseAmount'
      },{
        label:'离线时间点',
        width:'80',
        key:'offlineCheck',
        fn:'offlineCheck'
      }],
      dateList:[],
      selectList:[],
      itemData:null
    };
  },

  created(){
    const date = new Date();
    this.searchFormData.date=[
      parseTime(date.getTime() - 3600 * 1000 * 24 * 6, '{y}-{m}-{d}'),
      parseTime(date, '{y}-{m}-{d}')
    ];
    this.searchFun(1);
  },

  mounted(){
  },

  methods: {
    // 显示批量查询
    triggerClick(name){
      this.$refs[name].show=true;
    },
    // 搜索框输入的数据
    returnData(e){
      this.searchFormData=e;
    },
    // 获取设备类型
    async getEquType(){
      let res=await materielUploadEquipmentType();
      if (res.result == 0) {
        var list=res.data.map(e=>{
          return {label:e.typeName,value:e.lyyEquipmentTypeId};
        });
        this.searchList[1].options=list;
      };
    },
    // 改变条数
    handleSizeChange(val){
      this.pager.size = val;
      this.searchFun();
    },
    // 翻页
    currentChange(val){
      this.pager.page = val;
      this.searchFun();
    },
    // 导出
    exportEquipmentOfflineSituationFn(){
      let isOne=true;
      let obj=JSON.parse(JSON.stringify(this.searchFormData));
      Object.keys(obj).forEach(key=>{
        if(this.searchFormData[key]||this.searchFormData[key]===0){
          isOne=false;
        };
        if(this.searchFormData[key]==''){
          this.searchFormData[key]=null;
        };
      });
      if(isOne){
        this.$message.warning('请至少输入一个查询条件');
        return;
      };
      let searchFormData=Object.assign({},this.searchFormData);
      if(searchFormData.date){
        searchFormData.startDate=searchFormData.date[0];
        searchFormData.endDate=searchFormData.date[1];
        delete searchFormData.date;
      }else{
        this.$message.warning('请输入时间查询');
        return;
      };
      const loading = this.$loading({
        lock: true,
        text: '导出中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportEquipmentOfflineSituation(searchFormData).then(res=>{
        loading.close();
        console.log(res, '======测试')
        const blob = new Blob([res.data], {type: 'application/vnd.ms-excel;charset=utf-8'});
        let fileName = decodeURI(escape(res.headers['content-disposition'].split('=')[1]));
        if ('download' in document.createElement('a')) { // 非IE下载
            const elink = document.createElement('a')
            elink.download = fileName
            elink.style.display = 'none'
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            elink.click()
            URL.revokeObjectURL(elink.href) // 释放URL 对象
            document.body.removeChild(elink)
        } else { // IE10+下载
            navigator.msSaveBlob(blob, fileName)
        }
        this.$message({
          type: "success",
          message: "导出成功"
        });
      })
    },
    // 搜索
    searchFun(page){
      if(page){
        this.pager.page=1;
      };
      let isOne=true;
      let obj=JSON.parse(JSON.stringify(this.searchFormData));
      Object.keys(obj).forEach(key=>{
        if(this.searchFormData[key]||this.searchFormData[key]===0){
          isOne=false;
        };
        if(this.searchFormData[key]==''){
          this.searchFormData[key]=null;
        };
      });
      if(isOne){
        this.$message.warning('请至少输入一个查询条件');
        return;
      };
      let searchFormData=Object.assign({
        pageIndex:this.pager.page,
        pageSize:this.pager.size
      },this.searchFormData);
      if(searchFormData.date){
        searchFormData.startDate=searchFormData.date[0];
        searchFormData.endDate=searchFormData.date[1];
        delete searchFormData.date;
      }else{
        this.$message.warning('请输入时间查询');
        return;
      };
      // if(searchFormData.equipmentValue){
      //   searchFormData.equipmentValueList=[searchFormData.equipmentValue];
      //   delete searchFormData.equipmentValue;
      // };
      this.pager.loading=true;
      getEquipmentOfflineSituation(searchFormData).then(res => {
        if (res.result == 0) {
          this.dateList=[];
          res.data.items.forEach(e=>{
            e.equipmentOfflineCountDetailList.forEach(a=>{
              e[a.statisticsDate]=a.offlineCount;
              if(this.dateList.indexOf(a.statisticsDate)<0){
                this.dateList.push(a.statisticsDate);
              };
            });
          });
          this.list = res.data.items;
          this.pager.total = res.data.total;
          this.$nextTick(() => {
            this.$refs.multipleTable.doLayout();
          });
          this.pager.loading=false;
        };
      });
    },
    // 点击元素的时候
    itemClick(e,type){
      this.itemData=e;
      if(type=='offlineCheck'){
        this.$refs.equipmentOfflineDetail.getDate(e);
        this.$refs.equipmentOfflineDetail.show=true;
      }
    }
  },

  components: {
    searchModule,
    equipmentOfflineDetail
  },
}
</script>
<style scoped lang='less'>
::v-deep .el-message-box{
  .msg-box{
    .msg1{
      width:320px;
      margin:10px auto 20px;
    }
    .red-text{
      width:360px;
      margin:0 auto;
      color:#FF0000;
    }
  }
} 
.blue{
  color:#409EFF;
  cursor: pointer;
}
.equipmentOfflineSituation{
  .page-tit{
    font-weight: bold;
    margin-top:-20px;
    font-size:16px;
    line-height: 40px;
  }
  .card-tit{
    line-height: 16px;
    margin-top:15px;
    font-weight: bold;
  }
}
.line{
  line-height: 34px;
  margin:0 10px;
  font-weight: normal;
}
</style>