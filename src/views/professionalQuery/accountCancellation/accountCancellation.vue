<!--
 * @Author: your name
 * @Date: 2019-12-12 11:05:31
 * @LastEditTime: 2020-06-04 10:06:22
 * @LastEditors: 邹新许
 * @Description: In User Settings Edit
 * @FilePath: \rbac_html\src\views\professionalQuery\accountCancellation\accountCancellation.vue
 -->
<template>
  <div v-loading="pageInfo.loading">
    <el-card>
      <div class="operation">
        <el-form :inline="true"
                 label-position="right"
                 label-width="100px">
          <el-form-item label="商户名"
                        prop="merchantName">
            <el-input class="inline-input"
                      v-model.trim="formData.merchantName"
                      placeholder="请输入商户名"
                      clearable></el-input>
          </el-form-item>
          <el-form-item label="账号"
                        prop="merchantAccount">
            <el-input class="inline-input"
                      v-model.trim="formData.merchantAccount"
                      placeholder="请输入账号"
                      clearable></el-input>
          </el-form-item>
          <!--按钮-->
          <el-form-item>
            <el-button type="primary"
                       icon="el-icon-search"
                       @click="queryBtn">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <!--列表 start-->
        <el-table :data="pageInfo.list"
                  border
                  highlight-current-row
                  style="width: 100%;margin-bottom: 20px;"
                  >
          <template v-for="(item, index) in colums">
            <el-table-column v-if="item.key === 'operation'"
                             :key="index"
                             :prop="item.key"
                             :label="item.label"
                             :width="item.width"
                             :sortable="item.sortable"
                             align="center">
            </el-table-column>
            <el-table-column v-else-if="item.key === 'status'"
                             :key="index"
                             :prop="item.key"
                             :label="item.label"
                             :width="item.width"
                             :sortable="item.sortable"
                             align="center">
              <template slot-scope="scope">
                <div>
                  <p>{{scope.row.status | arrGetValue(statusArr, 'key', 'value')}}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-else-if="item.key === 'accountType'"
                             :key="index"
                             :prop="item.key"
                             :label="item.label"
                             :width="item.width"
                             :sortable="item.sortable"
                             align="center">
              <template slot-scope="scope">
                <div>
                  <p>{{scope.row.accountType | arrGetValue(accountTypeArr, 'key', 'value')}}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-else-if="item.key === 'created'"
                             :key="index"
                             :prop="item.key"
                             :label="item.label"
                             :width="item.width"
                             :sortable="item.sortable"
                             align="center">
              <template slot-scope="scope">
                <div>
                  <p v-if="scope.row.created">{{dateFormat(new Date(scope.row.created), 'yyyy-MM-dd HH:mm:ss')}}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-else
                             :key="index"
                             :prop="item.key"
                             :label="item.label"
                             :width="item.width"
                             :sortable="item.sortable"
                             align="center" />
          </template>
        </el-table>
        <el-pagination :page-sizes="[10, 20, 30, 40]"
                       :page-size="pageInfo.pageSize"
                       :total="pageInfo.total"
                       :current-page="pageInfo.pageIndex"
                       background
                       layout="total, prev, pager, next, sizes, jumper"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange" />
        <!--列表 end-->
      </div>
    </el-card>
  </div>
</template>

<script>
import { dateFormat } from "@js/utils";
import { accountCancellation } from "@api/professionalQuery/accountCancellation";
export default {
  name: 'accountCancellation',
  props: {},
  components: {},
  data () {
    return {
      formData: {
        merchantName: '',
        merchantAccount: '',
      },
      pageInfo: {
        total: 0,
        pageSize: 10,
        pageIndex: 1,
        loading: false,
        list: [],
      },
      // 列表每一列参数
      colums: [
        { key: 'accountType', label: '账号类型' },
        { key: 'merchantName', label: '商户名称' },
        { key: 'merchantAccount', label: '商户账号' },
        { key: 'logoutReason', label: '注销原因' },
        { key: 'status', label: '注销状态' },
        { key: 'created', label: '创建时间', sortable: true },
      ],
      statusArr: [
        { key: 1, value: '已注销' },
      ],
      accountTypeArr: [
        { key: 1, value: '主账号' },
      ],
      dateFormat: dateFormat,
    };
  },

  beforeCreate () {
  },

  created () {
  },

  beforeMount () {
  },

  mounted () {
    this.getList();
  },

  beforeUpdate () {
  },

  updated () {
  },

  activated () {
  },

  deactivated () {
  },

  beforeDestroy () {
  },

  destroyed () {
  },

  errorCaptured () {
  },

  methods: {
    queryBtn () {
      this.pageInfo.pageIndex = 1;
      this.getList();
    },
    getList () {
      this.pageInfo.loading = true;
      accountCancellation(Object.assign(this.getPagination(), this.getParamsSearch())).then(res => {
        this.pageInfo.loading = false;
        if (res.result === 0) {
          this.pageInfo.list = res.data.items;
          this.pageInfo.total = res.data.total;
        }
      })
    },
    getPagination () {
      return {
        pageIndex: this.pageInfo.pageIndex,
        pageSize: this.pageInfo.pageSize,
      }
    },
    getParamsSearch () {
      let params = {
        merchantName: '',
        merchantAccount: '',
      };
      if (this.formData.merchantName) {
        params['merchantName'] = this.formData.merchantName;
      }
      if (this.formData.merchantAccount) {
        params['merchantAccount'] = this.formData.merchantAccount;
      }
      return params;
    },
    // 改变列表每页条数
    handleSizeChange (val) {
      this.pageInfo.pageSize = val;
      this.getList();
    },
    // 改变页码
    handleCurrentChange (val) {
      this.pageInfo.pageIndex = val;
      this.getList();
    },
  },

  computed: {},

  watch: {}

}

</script>
<style lang="less" scoped>
.operation {
  padding: 20px;
}
</style>
