<template>
  <div class="pd-b-20" style="position: relative;">
    <el-button style="position: absolute; top: 20px; right: 40px;"
      size="small"
      type="primary"
      @click="gotoLiShi">延期变更记录</el-button>
    <el-form class="form-page" ref="form" label-width="70px">
      <el-form-item label-width="96px" label="设备类型" prop="factoryBrand">
        <el-select
          v-model="lyyEquipmentTypeId"
          class="w-200"
          placeholder="请选择设备厂家"
          size="small"
        >
          <el-option
            :loading="getDeviceTypesing"
            v-for="item in deviceTypes"
            :value="item.lyyEquipmentTypeId"
            :key="item.lyyEquipmentTypeId"
            :label="item.typeName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label-width="96px" label="过期延长时间" prop="time">
        <div class="flex-box">
          <el-input
            style="width: 200px"
            v-model="time"
            placeholder="请输入过期延长时间"
          ></el-input>
          <div class="ml-20">月（每月为30日）</div>
        </div>
      </el-form-item>
    </el-form>
    <div class="uploadBox">
      <el-upload
        ref="upload"
        :on-success="handleSuccess"
        :on-error="handleError"
        :file-list="fileList"
        :data="{ importType: 3 }"
        :on-change="onChange"
        :on-remove="removeFile"
        :auto-upload="false"
        :before-upload="beforeUpload"
        :disabled="disabled"
        :on-exceed="handleExceed"
        :limit="1"
        accept=".xlsx, .xls"
        class="upload-demo"
        action="/gw/admin/charge/equipment/setting/close/newImport"
        :headers="headerRamToken"
      >
      <!-- /gw/admin/charge/equipment/setting/close/import -->
        <el-row slot="trigger">
          <el-col>
            <el-button
              :disabled="disabled || throttle"
              size="small"
              type="primary"
              >选择excel文件</el-button
            >
            <!-- <el-button @click="downTemplate" size="small">上传模板下载</el-button> -->
          </el-col>
          <el-col>
            <ul class="el-upload-list el-upload-list--text" />
          </el-col>
        </el-row>
        <div style="padding: 8px" />
        <el-button
          class="min-w-108"
          :disabled="disabled || throttle"
          size="small"
          type="success"
          @click="submitUpload"
          >上传</el-button
        >
      </el-upload>
    </div>
    <el-table
      :data="list"
      v-loading="getListing"
      border
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column prop="equipmentValue" label="设备编号" width="180">
        <template slot-scope="{ row }">
          <span>{{ row.equipmentValue || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="equipmentTypeName" label="设备类型" width="180">
        <template slot-scope="{ row }">
          <span>{{ row.equipmentTypeName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="ruleName" label="收费规则名称">
        <template slot-scope="{ row }">
          <span>{{ row.ruleName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="networkType" label="设备通讯方式">
        <template slot-scope="{ row }">
          <span>{{ row.networkType || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="productModel" label="产品型号">
        <template slot-scope="{ row }">
          <span>{{ row.productModel || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="firstRegistrationDate" label="初始注册时间">
        <template slot-scope="{ row }">
          <span>{{ row.firstRegistrationDate || '--' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="createDate" label="收费生效时间">
        <template slot-scope="{ row }">
          <span>{{ row.createDate || '--' }}</span>
        </template>
      </el-table-column> -->
      <el-table-column prop="endDate" label="过期时间">
        <template slot-scope="{ row }">
          <span>{{ row.endDate || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="adOrgName" label="归属商户">
        <template slot-scope="{ row }">
          <span>{{ row.adOrgName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="agentUserName" label="归属工厂">
        <template slot-scope="{ row }">
          <span>{{ row.agentUserName || '--' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            @click="deleteItem(scope.row.chargeEquipmentImportLogId)"
            type="text"
            class="color-red"
            size="small"
            >删除</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>
    <div class="pd-t-20">
      <el-pagination
        layout="total, sizes, prev, pager, next"
        :page-size="listParams.pageSize"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :page-sizes="[10, 20, 30, 50, 100]"
        :total="total"
      >
      </el-pagination>
    </div>
    <div class="pd-t-20">
      <el-button
        @click="creatBill"
        size="small"
        class="min-w-108"
        type="primary"
        >修改账单时间</el-button
      >
    </div>
  </div>
</template>
<script>
import {
  importDetails,
  // getPrice,
  detailsDetails,
  closeOreditEquipment,
  newCloseBill,
  importNewDetails
} from "@/api/commercialize";
import { getDeviceType } from "@/api/admin/mainBoardConfig/index";
import { headerRamToken } from '@/utils/menu'
export default {
  name: "theBillChargeSeting",
  data() {
    return {
      postForm: "",
      priceList: [],
      getPriceListing: false,
      lyyEquipmentTypeId: null,
      list: [],
      listParams: {
        pageIndex: 1,
        pageSize: 20,
        importNo: "",
      },
      getListing: false,
      total: 0,
      deviceTypes: [],
      getDeviceTypesing: false,
      fileList: [],
      file: null,
      throttle: false,
      disabled: false,
      uploading: false,
      actionType: 1,
      typeDisaled: false,
      typeDisabled: false,
      time: "",
      headerRamToken,
    };
  },
  async created() {
    await this.getDeviceTypeFn();
  },
  methods: {
    // 模板下载
    downTemplate() {
      window.location.href =
        "https://oss.lyypublic.leyaoyao.com/abutmentImportExample.xlsx";
    },
    // 删除
    deleteItem(chargeEquipmentImportLogId) {
      this.$confirm("是否删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteItemFn(chargeEquipmentImportLogId);
        })
        .catch(() => {});
    },
    deleteItemFn(chargeEquipmentImportLogId) {
      detailsDetails([chargeEquipmentImportLogId]).then((res) => {
        if (res && res.result === 0) {
          this.getList();
          this.$message({
            type: "success",
            message: "删除成功",
          });
        }
      });
    },
    // 生成账单
    creatBill() {
      this.$confirm("确定修改设备账单过期时间？", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.creatBillFn2();
        })
        .catch(() => {});
    },
    creatBillFn2() {
      newCloseBill({
        // 导入类型;1:导入生成账单;2:导入删除账单,3:导入修改账单时间
        importType: 3,
        freeDays: this.time,
        importNo: this.listParams.importNo,
      }).then((res) => {
        const that = this;
        if (res && res.result === 0) {
          this.$message({
            type: "success",
            message: res.description,
          });
        }
        that.getList();
      });
    },
    gotoLiShi() {
      this.$router.push({
        // path: '/commercialize/deveiceOptionLog',
        path: '/1215378089929334784',
        query: {type: 3}
      })
    },
    creatBillFn() {
      closeOreditEquipment({
        // 导入类型;1:导入生成账单;2:导入删除账单,3:导入修改账单时间
        importType: 3,
        freeDays: this.time,
        importNo: this.listParams.importNo,
      }).then((res) => {
        const that = this;
        if (res && res.result === 0) {
          this.$message({
            type: "success",
            message: res.description,
          });
        }
        that.getList();
      });
    },
    submitUpload() {
      if (!this.file) {
        this.$message.error("请选择上传文件！");
        this.throttle = false;
        return;
      }
      this.uploading = true;
      this.$refs.upload.submit();
    },
    beforeUpload(file) {
      // file.size / 1024>20
      const size = file.size / 1024;
      if (size > 200) {
        this.throttle = false;
        this.$message.error("文件过大，请控制在200kb以内");
      }
      return size < 100;
    },
    onChange(file) {
      this.file = file;
    },
    handleSuccess(res) {
      this.uploading = false;
      if (res && res.result === 0) {
        if (res.data) {
          this.throttle = false;
          this.fileList = [];
          const json = res.data;
          this.listParams.importNo = json.importNo;
          this.getList();
        } else {
          this.$message.error("未找到对应设备，请检查导入文件");
          this.throttle = false;
        }
      } else {
        this.$message.error(res.description || "上传失败");
        this.throttle = false;
      }
    },
    getList() {
      this.getListing = true;
      importNewDetails(this.listParams).then((res) => {
        if (res && res.data) {
          const json = res.data;
          this.total = json.total;
          this.list = json.items || [];
        }
        this.getListing = false;
      });
    },
    // getList() {
    //   this.getListing = true;
    //   importDetails(this.listParams).then((res) => {
    //     if (res && res.data) {
    //       const json = res.data;
    //       this.total = json.total;
    //       this.list = json.items || [];
    //     }
    //     this.getListing = false;
    //   });
    // },
    handleError() {
      this.throttle = false;
      this.uploading = false;
      this.$message.error("导入失败！");
    },
    removeFile() {
      this.file = null;
    },
    handleExceed() {
      this.$message.warning(`当前限制选择 1 个文件，一次只能上传一个文件`);
      this.throttle = false;
    },
    // 获取设备品类列表
    async getDeviceTypeFn() {
      this.getDeviceTypesing = true;
      const res = await getDeviceType();
      if (res.result === 0 && res.data && res.data.length > 0) {
        let list = res.data;
        if (list[0].lyyEquipmentTypeId == 0) {
          list.shift();
        }
        // 目前只有充电桩所以把充电桩筛选出来 后续扩展请自行修改
        for (let i = 0; i < list.length; i++) {
          if (list[i].value === "CDZ") {
            list = [list[i]];
            this.lyyEquipmentTypeId = list[0].lyyEquipmentTypeId;
            break;
          }
        }
        this.deviceTypes = list;
      }
      this.getDeviceTypesing = false;
    },
    handleSizeChange(pageSize) {
      this.listParams.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageIndex) {
      this.listParams.pageIndex = pageIndex;
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
.min-w-108 {
  min-width: 108px;
}
.uploadBox {
  width: 350px;
}
.pd-t-20 {
  padding-top: 20px;
}
.pd-b-20 {
  padding-bottom: 20px;
}
.ml-20 {
  margin-left: 20px;
}
.flex-box {
  display: flex;
}
</style>
