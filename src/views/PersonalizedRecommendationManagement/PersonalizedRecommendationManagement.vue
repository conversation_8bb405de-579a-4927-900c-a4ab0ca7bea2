<!--
 * @Description: 个性化推荐管理
 * @Author:
 * @Date:
 * @LastEditors:
 * @LastEditTime:
 -->
<template>
  <div class="PersonalizedRecommendationManagement">
    <el-card>
      <div slot="header" class="clearfix">
        <div class="tips">
          <el-alert
            title="为确保用户体验，请及时处理。"
            :closable="false"
            show-icon
            type="warning">
          </el-alert>
        </div>
        <el-form :inline="true" label-position="right" label-width="100px">
          <el-form-item label="用户提交时间 :" label-width="110px">
            <el-date-picker
              v-model="formData.createDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              :editable="false"
              clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="记录处理时间 :" label-width="110px">
            <el-date-picker
              v-model="formData.handleDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              :editable="false"
              clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="用户类型 :">
            <el-select placeholder="请选择" v-model="formData.userTypeCode" clearable>
              <el-option
                v-for="(item, index) in userTypeCodeList"
                :key="index"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="申请类型 :">
            <el-select placeholder="请选择" v-model="formData.optType" clearable>
              <el-option
                v-for="(item, index) in optTypeList"
                :key="index"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="个性化推荐服务内容 :" label-width="150px">
            <el-select placeholder="请选择" v-model="formData.serviceCode" clearable multiple collapse-tags>
              <el-option
                v-for="(item, index) in serviceCodeList"
                :key="index"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="处理状态 :">
            <el-select placeholder="请选择" v-model="formData.status" clearable>
              <el-option
                v-for="(item, index) in statusList"
                :key="index"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户ID :">
            <el-input v-model.trim="formData.userId" placeholder="请输入" clearable></el-input>
            <p>*B端商户的用户ID为商户ID，C端消费者的用户ID为userID</p>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search()">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <table-base ref="refTableBase" @callBack="getList()">
          <div slot="created" slot-scope="scope">
            <p v-if="scope.row.created">
              {{dateFormat(new Date(scope.row.created), 'yyyy-MM-dd HH:mm:ss')}}
            </p>
          </div>
          <div slot="handleTime" slot-scope="scope">
            <p v-if="scope.row.handleTime">
              {{dateFormat(new Date(scope.row.handleTime), 'yyyy-MM-dd HH:mm:ss')}}
            </p>
            <p v-else>--</p>
          </div>
          <div slot="userTypeCode" slot-scope="scope">
            <p v-if="scope.row.userTypeCode === 'MERCHANT'">
              B端商户
            </p>
            <p v-else-if="scope.row.userTypeCode === 'CUSTOMER'">
              C端消费者
            </p>
          </div>
          <div slot="optType" slot-scope="scope">
            <p v-if="scope.row.optType">
              <span v-if="scope.row.optType === '0'">关闭</span>
              <span v-else-if="scope.row.optType === '1'">开启</span>
              个性化
            </p>
          </div>
          <div slot="status" slot-scope="scope">
            <p v-if="scope.row.status">
              <span v-if="scope.row.status === '0'">待处理</span>
              <span v-else-if="scope.row.status === '1'">已处理</span>
            </p>
          </div>
          <div slot="handleUser" slot-scope="scope">
            <p v-if="scope.row.handleUser">{{scope.row.handleUser}}</p>
            <p v-else>--</p>
          </div>
          <div slot="operation" slot-scope="scope">
            <el-button v-if="scope.row.status === '0'" type="text"
                       @click="handleRecord(scope.row.userServiceSwitchApplyId)">已处理
            </el-button>
          </div>
        </table-base>
      </div>
    </el-card>
  </div>
</template>

<script>
  import TableBase from "@components/tableBase/tableBase";
  import {
    serviceSwitchHandleApply,
    serviceSwitchRecordPage
  } from "@api/PersonalizedRecommendationManagement";
  import {dateFormat} from "@js/utils";
  import {isNotEmptyArray} from "@components/tableBase/utilsTableBase";

  export default {
    name: "PersonalizedRecommendationManagement",

    props: {},

    components: {TableBase},

    mixins: [],

    data() {
      const cleanServiceCodeList = [
        {label: '升单服务', value: 'up_order'},
        {label: '升购储值', value: 'up_buy_save_money'},
        {label: '新客宝', value: 'new_guest_treasure'},
        {label: '智慧营销', value: 'wisdom_market'},
        {label: '第三方引流', value: 'third_recommend'},
        {label: '幸运购币', value: 'lucky_buy_coin'},
      ];
      return {
        refTableBase: null,
        columns: [
          {key: "created", label: "用户提交时间", slot: true},
          {key: "handleTime", label: "记录处理时间", slot: true},
          {key: "userTypeCode", label: "用户类型", slot: true},
          {key: "optType", label: "申请类型", slot: true},
          {key: "service", label: "个性化推荐服务内容"},
          {key: "userId", label: "用户ID"},
          {key: "status", label: "处理状态", slot: true},
          {key: "handleUser", label: "记录操作人", slot: true},
          {key: "operation", label: "操作", slot: true},
        ],
        pageSize: 20,
        pageSizeArr: [20, 30, 40, 50],
        dateFormat: dateFormat,
        formData: {},
        statusList: [
          {label: '全部', value: '-1'},
          {label: '未处理', value: '0'},
          {label: '已处理', value: '1'},
        ],
        serviceCodeList: [
          {label: '全部', value: '-1'},
          ...cleanServiceCodeList
        ],
        cleanServiceCodeList,
        optTypeList: [
          {label: '全部', value: '-1'},
          {label: '关闭个性化', value: '0'},
          {label: '开启个性化', value: '1'},
        ],
        userTypeCodeList: [
          {label: '全部', value: '-1'},
          {label: 'B端商户', value: 'MERCHANT'},
          {label: 'C端消费者', value: 'CUSTOMER'},
        ],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.refTableBase = this.$refs.refTableBase;
      this.refTableBase.setColumnsSecurity(this.columns);
      this.refTableBase.setPageSizeArrSecurity(this.pageSizeArr);
      this.refTableBase.setPageSizeSecurity(this.pageSize);
      this.search();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      search() {
        this.refTableBase.setPageIndexSecurity(1);
        this.getList();
      },
      async getList() {
        this.refTableBase.showLoadingFun();
        this.refTableBase.reSetListFun();
        const params = {
          pageIndex: this.refTableBase.pageInfo.pageIndex,
          pageSize: this.refTableBase.pageInfo.pageSize,
          ...this.getParams(),
        };
        const res = await serviceSwitchRecordPage(params);
        this.refTableBase.closeLoadingFun();
        if (res && res.result === 0 && res.data) {
          this.refTableBase.setListSecurity(res.data.records);
          this.refTableBase.setTotalSecurity(res.data.total);
        }
      },
      getParams() {
        let params = {};
        if (isNotEmptyArray(this.formData.createDate)) {
          params["createStartDate"] = this.formData.createDate[0] + ' 00:00:00';
          params["createEndDate"] = dateFormat(new Date(this.formData.createDate[1]).getTime() + 1 * 24 * 60 * 60 * 1000) + ' 00:00:00';
        }
        if (isNotEmptyArray(this.formData.handleDate)) {
          params["handleStartDate"] = this.formData.handleDate[0] + ' 00:00:00';
          params["handleEndDate"] = dateFormat(new Date(this.formData.handleDate[1]).getTime() + 1 * 24 * 60 * 60 * 1000) + ' 00:00:00';
        }
        if (this.formData.userTypeCode && this.formData.userTypeCode !== '-1') {
          params["userTypeCode"] = this.formData.userTypeCode;
        }
        if (this.formData.optType && this.formData.optType !== '-1') {
          params["optType"] = parseInt(this.formData.optType);
        }
        if (Array.isArray(this.formData.serviceCode) && this.formData.serviceCode.length > 0) {
          if (this.formData.serviceCode.some(item => item === '-1')) {
            params["serviceCode"] = (this.cleanServiceCodeList.map(item => item.value) || []).join();
          } else {
            params["serviceCode"] = this.formData.serviceCode.join();
          }
        }
        if (this.formData.status && this.formData.status !== '-1') {
          params["status"] = parseInt(this.formData.status);
        }
        if (this.formData.userId) {
          params["userId"] = this.formData.userId;
        }
        return params;
      },
      handleRecord(userServiceSwitchApplyId) {
        this.$alert('确认已处理？', '提示信息', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              const res = await serviceSwitchHandleApply({userServiceSwitchApplyId: userServiceSwitchApplyId});
              instance.confirmButtonLoading = false;
              if (res && res.result === 0) {
                this.$message.success('已处理成功');
                this.getList();
                done();
              }
            } else {
              instance.confirmButtonLoading = false;
              done();
            }
          }
        });
      },
    },

    computed: {},

    watch: {},
  }
</script>

<style lang="less" scoped>
  .PersonalizedRecommendationManagement {
    .tips {
      width: 300px;
      margin-bottom: 20px;
    }
  }
</style>
