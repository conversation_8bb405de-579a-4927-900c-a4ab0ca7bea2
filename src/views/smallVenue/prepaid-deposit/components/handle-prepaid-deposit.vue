<template>
  <div>
    <LyyDialog :visible.sync="isShowDialog" title="预存款管理" width="1000px" :showFooter="false">
      <div>
        <TablePageToolbar>
          <div>商户ID: {{ rowData.merchantId }}</div>
          <div>商户名称: {{ rowData.merchantName }}</div>
          <div>门店ID: {{ rowData.storeId }}</div>
          <div>门店名称: {{ rowData.storeName }}</div>
          <div>预存款余额: {{ rowData.balanceAmount }}</div>
          <el-button type="text" @click="recharge()">充值</el-button>
          <el-button type="text" @click="refund()">退款</el-button>
        </TablePageToolbar>

        <TablePageSearch>
          <el-form inline label-width="110px">
            <!-- 操作日期 -->
            <el-form-item label="操作日期">
              <lyy-date-picker v-model="opDate" @change="handleDateChange" />
            </el-form-item>
            <!-- 订单编号 -->
            <el-form-item label="订单编号">
              <el-input v-model="form.orderNo" placeholder="请输入" />
            </el-form-item>
            <!-- 团购单号 -->
            <el-form-item label="团购单号">
              <el-input v-model="form.channelOrderNo" placeholder="请输入" />
            </el-form-item>
            <!-- 核销渠道 -->
            <el-form-item label="核销渠道">
              <LyySelect v-model="form.channelType" :options="ChannelTypeList" />
            </el-form-item>
            <!-- 变更类型 -->
            <el-form-item label="变更类型">
              <LyySelect v-model="form.changeType" :options="ChangeTypeList" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :loading="isLoading" @click="handleSearch()">
                查询
              </el-button>
            </el-form-item>
          </el-form>
        </TablePageSearch>

        <lyy-data-table
          ref="dataTable"
          :columns="columns"
          :params="params"
          :req-fn="queryChannelDepositChangeLogPage"
          pageKey="pageNum"
        />
      </div>
    </LyyDialog>

    <!-- 充值弹窗 -->
    <RechargeDialog
      v-if="isShowRechargeDialog"
      :show.sync="isShowRechargeDialog"
      :id="rowData.id"
      @confirm="handleSuccess"
    />

    <!-- 退款弹窗 -->
    <RefundDialog
      v-if="isShowRefundDialog"
      :show.sync="isShowRefundDialog"
      :id="rowData.id"
      :balanceAmount="rowData.balanceAmount"
      @confirm="handleSuccess"
    />
  </div>
</template>

<script>
import LyyDialog from '../../components/lyy-dialog/index.vue';
import LyyDataTable from '@/views/smallVenue/components/lyy-data-table/index.vue';
import RechargeDialog from './recharge-dialog.vue';
import RefundDialog from './refund-dialog.vue';
import { queryChannelDepositChangeLogPage } from '@/api/smallVenue/merchant';
import { tablePageMixin } from '@/views/smallVenue/mixins';
import { mergeDeep } from '@/views/smallVenue/utils';
import dayjs from 'dayjs';
import { ChangeTypeEnum, ChangeTypeList, ChannelTypeEnum, ChannelTypeList } from '../../enums';

export default {
  name: 'handle-store-dialog',
  components: {
    LyyDialog,
    LyyDataTable,
    RechargeDialog,
    RefundDialog,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: () => ({}),
    },
    merchantId: {
      type: String,
      default: '',
    },
  },
  mixins: [tablePageMixin],
  data() {
    const startTime = dayjs().format('YYYY-MM-DD 00:00:00');
    const endTime = dayjs().add(1, 'day').format('YYYY-MM-DD 23:59:59');
    return {
      ChannelTypeList,
      ChangeTypeList,
      isLoading: false,
      form: {
        orderNo: '',
        channelOrderNo: '',
        changeType: '', // 变更类型 1充值 2退款 3核销 4退核销
        channelType: '', // 渠道类型
        changeBeginTime: startTime,
        changeEndTime: endTime,
      },
      opDate: [startTime, endTime],
      columns: [
        { label: '变更时间', prop: 'createTime', minWidth: '' },
        {
          label: '变更类型',
          formatter: (row) => {
            return ChangeTypeEnum[row?.changeType] || '';
          },
          minWidth: '',
        },
        {
          label: '渠道类型',
          prop: 'channelType',
          formatter: (row) => {
            return ChannelTypeEnum[row?.channelType] || '';
          },
          minWidth: '',
        },
        { label: '订单编号', prop: 'orderNo', minWidth: '' },
        { label: '团购单号', prop: 'channelOrderNo', minWidth: '' },
        { label: '核销订单金额', prop: 'channelOrderAmount', minWidth: '' },
        { label: '变更前余额', prop: 'beforeBalance', minWidth: '' },
        { label: '变更金额', prop: 'changeAmount', minWidth: '' },
        { label: '变更后余额', prop: 'afterBalance', minWidth: '' },
      ],
      isShowRechargeDialog: false, // 是否展示充值弹窗
      isShowRefundDialog: false, // 是否展示退款弹窗
    };
  },
  computed: {
    isShowDialog: {
      get({ show }) {
        return show;
      },
      set(status) {
        this.$emit('update:show', status);
      },
    },
    params({ form, rowData }) {
      return {
        // 过滤掉空的
        orderNo: form.orderNo || undefined,
        channelOrderNo: form.channelOrderNo || undefined,
        changeType: form.changeType || undefined,
        channelType: form.channelType || undefined,
        changeBeginTime: form.changeBeginTime || undefined,
        changeEndTime: form.changeEndTime || undefined,
        merchantId: rowData?.merchantId,
        storeId: rowData?.storeId,
      };
    },
  },
  mounted() {
    if (this.rowData) {
      this.form = mergeDeep(this.form, this.rowData);
    }
  },
  methods: {
    queryChannelDepositChangeLogPage,
    handleSearch() {
      this.$refs?.dataTable?.reload();
    },
    handleDateChange(val) {
      if (val) {
        const [startTime, endTime] = val;
        this.form.changeBeginTime = startTime;
        this.form.changeEndTime = endTime;
      } else {
        this.form.changeBeginTime = null;
        this.form.changeEndTime = nul;
      }
    },
    recharge() {
      this.isShowRechargeDialog = true;
    },
    refund() {
      this.isShowRefundDialog = true;
    },
    handleSuccess() {
      this.isShowDialog = false;
      this.$emit('confirm');
    },
  },
};
</script>

<style lang="less" scoped>
.l-tablePage__toolbar {
  div {
    margin-bottom: 10px;
  }
}
</style>
