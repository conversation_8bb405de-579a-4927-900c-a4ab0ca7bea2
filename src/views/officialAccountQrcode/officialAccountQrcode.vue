<!--
 * @Author: lijinrong
 * @Date: 2019-12-23 13:47:00
 * @LastEditors  : lijinrong
 * @LastEditTime : 2019-12-25 14:56:35
 * @Description: file content
 -->
<template>
  <div class="official-qccount-qrcode">
    <el-card shadow="always">
      <div class="header">
        <div class="intro" style="font-size: 12px;">
          <p>功能介绍:</p>
          <p>
            （1）在此页面创建任务后，可以批量生成设备带参二维码，然后将带参二维码下载并发给商家。
          </p>
          <p>（2）商家将带参二维码贴在线下机器上以后，用户扫描带参二维码即可直接进入商家的公众号。在商家公众号中点击系统推送的消息，即可跳转到对应设备的C端首页。</p>
          <p>
            （3）“原有的C端二维码” 和 此处生成的 “带参二维码” 支持同时使用。
          </p>
          <p>（4）注意：仅支持已认证的服务号。请务必确保商家授权的是“已认证的服务号”，否则将会导致功能无法正常使用。</p>
        </div>
        <el-button type="primary" class="add-btn" @click="onAddBtnClick">添加任务</el-button>
        <el-button type="primary" class="add-btn" @click="onRecordBtnClick">生成记录</el-button>
      </div>

      <div class="content">
        <el-table :data="table.datas" border style="width: 100%" v-loading="table.loading">
          <el-table-column prop="merchantName" width="150px" label="商家名称"> </el-table-column>
          <el-table-column prop="name" width="150px" label="商家账号"> </el-table-column>
          <el-table-column prop="officialAccoutName" width="150px" label="公众号名称">
          </el-table-column>
          <el-table-column prop="isauthorized" width="120px" label="授权状态">
            <template slot-scope="scope">
              <span v-if="scope.row.isauthorized === 'Y'">已授权</span>
              <span v-else>未授权</span>
            </template>
          </el-table-column>
          <el-table-column prop="remarks" label="备注"> </el-table-column>
          <el-table-column prop="created" width="150px" label="创建时间"> </el-table-column>
          <el-table-column prop="taskState" width="120px" label="任务状态">
            <template slot-scope="scope">
              <span v-if="scope.row.taskState === 1">进行中</span>
              <span v-else>已终止</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="280px">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.taskState === 1"
                @click="onReAuthBtnClick(scope.row)"
                type="text"
                size="small"
                >重新授权</el-button
              >
              <el-button
                v-if="scope.row.taskState === 1"
                @click="onEditBtnClick(scope.row)"
                type="text"
                size="small"
                >编辑</el-button
              >
              <el-button
                v-if="scope.row.taskState === 1"
                @click="onStopBtnClick(scope.row)"
                type="text"
                size="small"
                >终止</el-button
              >
              <el-button
                v-if="scope.row.taskState === 1"
                @click="onGenBtnClick(scope.row)"
                type="text"
                size="small"
                >生成带参二维码</el-button
              >
              <span v-if="scope.row.taskState === 2">——</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="table.pagination.page"
          :page-sizes="[20, 50, 100]"
          :page-size="table.pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="table.pagination.total"
        >
        </el-pagination>
      </div>

      <!-- 添加任务弹窗 -->
      <el-dialog :title="addForm.title" :visible.sync="addForm.visible">
        <el-form :model="addForm.formData" :rules="addForm.rules" label-width="80px">
          <el-form-item label="商家账号" v-if="addForm.title === '编辑'">
            <span>{{ addForm.formData.name }}</span>
          </el-form-item>
          <el-form-item label="商家账号" prop="name" v-else>
            <el-input v-model="addForm.formData.name"></el-input>
          </el-form-item>
          <el-form-item label="备注信息">
            <el-input
              type="textarea"
              :rows="2"
              placeholder="仅乐摇摇可见，仅作记录用途，20字以内"
              v-model="addForm.formData.remarks"
              maxlength="20"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="公众号" v-if="addForm.title === '编辑'">
            <span>{{ addForm.formData.officialAccoutName }}</span>
          </el-form-item>
          <el-form-item label="公众号" prop="lyyOfficialAccoutId" v-else>
            <el-button type="text" @click="onAddOfficialAccounts">添加公众号</el-button>
            <el-select
              v-model="addForm.formData.lyyOfficialAccoutId"
              filterable
              reserve-keyword
              placeholder="请选择"
            >
              <el-option
                v-for="item in addForm.officialAccounts"
                :key="item.lyyOfficialAccoutId"
                :label="item.officialAccoutName"
                :value="item.lyyOfficialAccoutId"
              >
              </el-option>
            </el-select>
            <el-button type="text" :loading="addForm.searchLoading" @click="onGetOfficialAccounts">查询</el-button>
          </el-form-item>
          <el-form-item label="推送消息" prop="messageDescribe">
            <el-radio-group
              v-model="addForm.formData.messageType"
              @change="handleMessageTypeChange"
            >
              <el-radio :label="'text'">文本消息</el-radio>
              <el-radio :label="'news'">图文消息</el-radio>
            </el-radio-group>
            <el-input
              v-if="addForm.formData.messageType === 'text'"
              type="textarea"
              :rows="2"
              placeholder="30字以内，如：点击此处前往启动设备"
              v-model="addForm.formData.messageDescribe"
              maxlength="30"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="标题" prop="title" v-if="addForm.formData.messageType === 'news'">
            <el-input v-model="addForm.formData.title" maxlength="30"></el-input>
          </el-form-item>
          <el-form-item label="图片" prop="pic" v-if="addForm.formData.messageType === 'news'">
            <el-upload
              class="avatar-uploader"
              action="/gw/admin/rest/officialAccounts/uploadPicture?maxSize=0"
              :headers="headerRamToken"
              :show-file-list="false"
              accept="image/jpeg, image/png"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <div v-loading="addForm.imageLoading">
                <img
                  v-if="addForm.formData.pictureUrl"
                  :src="addForm.formData.pictureUrl"
                  class="avatar"
                />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </div>
            </el-upload>
            <span style="color: #999999;font-size: 12px"
              >2M以内，支持JPG、PNG格式，建议尺寸360*200 或 200*200</span
            >
          </el-form-item>
          <el-form-item
            label="描述"
            prop="messageDescribe"
            v-if="addForm.formData.messageType === 'news'"
          >

            <p>【微信昵称】您好，您扫码的设备编号为【设备编号】场地名称，欢迎使用</p>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addForm.visible = false">取 消</el-button>
          <el-button type="primary" @click="onConfirm" :loading="addForm.loading">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 生成带参二维码弹窗 -->
      <el-dialog title="生成带参二维码" :visible.sync="genForm.visible">
        <el-form :model="genForm.formData" label-width="120px">
          <el-form-item label="商家账号">
            <span>{{ genForm.name }}</span>
          </el-form-item>
          <el-form-item label="二维码类型">
            <el-radio-group v-model="genForm.formData.qrcodeType">
              <el-radio :label="1">永久二维码</el-radio>
              <el-radio :label="2">临时二维码（有效期30天）</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="生成设备二维码">
            <el-radio-group v-model="genForm.formData.type">
              <el-radio :label="1">上传设备</el-radio>
              <el-radio :label="2">按场地生成</el-radio>
            </el-radio-group>
            <p
              v-if="genForm.formData.type == 1"
              style="color: #999999;font-size: 12px;line-height: 20px;"
            >
              提示：将给所上传的设备生成带参二维码。请确保上传的设备是该商家的设备，否则后果自负。(目前仅支持兑币机设备)
            </p>
            <p v-if="genForm.formData.type == 1" style="color: #999999;font-size: 12px;line-height: 20px;">
              请上传含设备编号的excle表，一行一个设备编号。
              <el-button
                type="text"
                @click="
                  jumpTo(
                    'https://oss.lyypublic.leyaoyao.com/assets/lyy/merchant/files/%E4%B8%8A%E4%BC%A0%E8%AE%BE%E5%A4%87%E7%BC%96%E5%8F%B7excel%E8%A1%A8%E6%A8%A1%E6%9D%BF.xlsx'
                  )
                "
                >下载模板</el-button
              >
            </p>
            <p
              v-if="genForm.formData.type == 2"
              style="color: #999999;font-size: 12px;line-height: 20px;"
            >
              提示：将给所选场地下的当前全部设备生成带参二维码。(目前仅支持兑币机设备)
            </p>
            <el-upload
              v-if="genForm.formData.type == 1"
              action="https://jsonplaceholder.typicode.com/posts/"
              :multiple="false"
              :file-list="genForm.fileList"
              :on-change="handleFileChange"
              :auto-upload="false"
            >
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
            <el-popover
              v-if="genForm.formData.type == 2"
              placement="bottom"
              width="300"
              @after-leave="
                genForm.searchSite = '';
                querySearchAsync();
              "
              trigger="click"
            >
              <div class="group-content">
                <div class="search">
                  <el-input
                    size="mini"
                    @keyup.native="querySearchAsync"
                    v-model="genForm.searchSite"
                    placeholder="请输入场地名称"
                  ></el-input>
                </div>
                <div class="nodata" v-show="showNoData">暂无数据</div>
                <el-checkbox
                  v-show="!genForm.searchSite"
                  v-model="genForm.checkAll"
                  :checked="genForm.groupIds.length === genForm.groups.length"
                  @change="handleSelectAll"
                  >全选</el-checkbox
                >
                <div class="group-select-checkbox">
                  <el-checkbox-group v-model="genForm.groupIds" @change="handleCheckedSitesChange">
                    <el-checkbox
                      v-show="!item.hide"
                      v-for="item in genForm.groups"
                      :label="item.lyyEquipmentGroupId"
                      :value="item.lyyEquipmentGroupId"
                      :key="item.lyyEquipmentGroupId"
                      >{{ item.name }}</el-checkbox
                    >
                  </el-checkbox-group>
                </div>
              </div>
              <div slot="reference" class="l group-select">
                <span class="text"
                  >{{ genForm.groupIds.length ? "" : "请选择场地" }}
                  <el-tag disable-transitions size="mini" v-show="genForm.groupIds.length">{{
                    genForm.groupIds.length == 1
                      ? selectSiteListName
                      : "已选中" + genForm.groupIds.length + "个"
                  }}</el-tag></span
                >
                <span class="el-icon-arrow-down"></span>
              </div>
            </el-popover>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="genForm.visible = false">取 消</el-button>
          <el-button type="primary" @click="onGenerateQrcode" :loading="genForm.loading"
            >生成带参二维码</el-button
          >
        </div>
      </el-dialog>

      <!-- 生成记录弹窗 -->
      <el-dialog width="80%" title="带参二维码生成记录" :visible.sync="recordTable.visible">
        <el-table :data="recordTable.datas" style="width: 100%" v-loading="recordTable.loading">
          <el-table-column prop="name" label="商家账号"></el-table-column>
          <el-table-column prop="officialAccoutName" label="公众号名称"></el-table-column>
          <el-table-column prop="" label="二维码类型">
            <template slot-scope="scope">
              <span v-if="scope.row.qrcodeType == 1">永久二维码</span>
              <span v-else>临时二维码</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="操作方式">
            <template slot-scope="scope">
              <span v-if="scope.row.type == 1">上传设备</span>
              <span v-else>按场地生成</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="状态">
            <template slot-scope="scope">
              <span v-if="scope.row.state == 1">生成中</span>
              <span v-else>已生成</span>
            </template>
          </el-table-column>
          <el-table-column prop="successNum" label="成功设备数量"></el-table-column>
          <el-table-column prop="failNum" label="失败设备数量"></el-table-column>
          <el-table-column prop="created" label="生成时间" width="150"></el-table-column>
          <el-table-column prop="" label="操作" width="200" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="
                  downloadFileRamToken(
                    '/rest/officialAccounts/downloadQRCodes?officialAccoutTaskRecordId=' +
                      scope.row.officialAccoutTaskRecordId
                  )
                "
                v-if="scope.row.state === 2 && scope.row.successNum && scope.row.successNum > 0"
                >下载带参二维码</el-button
              >
              <el-button
                type="text"
                @click="
                  downloadFileRamToken(
                    '/rest/officialAccounts/downloadEquipmentRecord?officialAccoutTaskRecordId=' +
                      scope.row.officialAccoutTaskRecordId
                  )
                "
                v-if="scope.row.state === 2"
                >导出设备编号</el-button
              >
              <span v-if="scope.row.state === 1">——</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleRecordSizeChange"
          @current-change="handleRecordCurrentChange"
          :current-page="recordTable.pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="recordTable.pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordTable.pagination.total"
        >
        </el-pagination>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  componentAuthorizerList,
  getAuthUrl,
  uploadFile,
  getOfficialAccountsTaskList,
  saveOrUpdateTask,
  modifyTaskState,
  againAuthUrl,
  getGroupsInfoByAdOrgId,
  createQRCodes,
  getOfficialAccountsTaskById,
  getQRCodeCreateRecordList
} from "../../api/officialAccount";
import { headerRamToken, downloadFileRamToken } from '@/utils/menu'


const ossImgUrl = "http://lyy-public.oss-cn-shenzhen.aliyuncs.com/";

export default {
  data() {
    return {
      headerRamToken,
      addForm: {
        searchLoading: false,
        loading: false,
        visible: false,
        officialAccounts: [],
        imageLoading: false,
        title: "添加",
        formData: {
          officialAccoutTaskId: "",
          lyyOfficialAccoutId: "",
          officialAccoutName: "",
          name: "",
          adOrgId: "",
          remarks: "",
          title: "",
          messageType: "text",
          pictureUrl: "",
          messageDescribe: ""
        },
        rules: {
          name: [{ required: true, message: " ", trigger: "blur" }],
          lyyOfficialAccoutId: [{ required: true, message: " ", trigger: "blur" }],
          title: [{ required: true, message: " ", trigger: "blur" }],
          pic: [{ required: true, message: " ", trigger: "blur" }],
          messageDescribe: [{ required: true, message: " ", trigger: "blur" }]
        }
      },
      table: {
        loading: false,
        datas: [],
        pagination: {
          page: 1,
          pageSize: 20,
          total: 0
        }
      },
      genForm: {
        loading: false,
        visible: false,
        name: "",
        fileList: [],
        groups: [],
        groupIds: [],
        checkAll: false,
        searchSite: "",
        formData: {
          officialAccoutTaskId: "",
          qrcodeType: 1,
          type: 1,
          groupIds: "",
          file: null
        }
      },
      recordTable: {
        visible: false,
        loading: false,
        datas: [],
        pagination: {
          page: 1,
          pageSize: 10,
          total: 0
        }
      }
    };
  },
  computed: {
    showNoData() {
      let result = true;
      this.genForm.groups.forEach(item => {
        if (!item.hide) {
          result = false;
        }
      });
      return result;
    },
    selectSiteListName() {
      if (!this.genForm.groupIds.length) return "";
      var result = "";
      this.genForm.groups.forEach(item => {
        if (this.genForm.groupIds[0] == item.lyyEquipmentGroupId) {
          result = item.name;
        }
      });
      return result;
    }
  },
  created() {
    this.onGetList();
  },
  methods: {
    downloadFileRamToken,
    async onGetList() {
      this.table.loading = true;
      var params = {
        pageSize: this.table.pagination.pageSize,
        pageIndex: this.table.pagination.page
      };
      var res = await getOfficialAccountsTaskList(params);
      if (res.result === 0 && res.data && res.data.items) {
        this.table.datas = res.data.items;
        this.table.pagination.total = res.data.total;
      }
      this.table.loading = false;
    },
    onAddBtnClick() {
      this.addForm.title = "添加";
      this.addForm.visible = true;
      this.addForm.formData = {
        officialAccoutTaskId: "",
        lyyOfficialAccoutId: "",
        name: "",
        adOrgId: "",
        remarks: "",
        title: "",
        messageType: "text",
        pictureUrl: "",
        messageDescribe: ""
      };
    },
    async onGetOfficialAccounts() {
      if (!this.addForm.formData.name) {
        this.$message({
          type: "error",
          message: "请先输入商家账号"
        });
        return;
      }
      this.addForm.searchLoading = true
      var res = await componentAuthorizerList({ adOrgName: this.addForm.formData.name });

      if (res.result === 0 && res.data) {
        this.addForm.officialAccounts = res.data.list;
        if(this.addForm.officialAccounts.length && !this.addForm.formData.lyyOfficialAccoutId) {
          this.addForm.formData.lyyOfficialAccoutId = this.addForm.officialAccounts[0].lyyOfficialAccoutId
        }
      }
      this.addForm.searchLoading = false
    },
    async onAddOfficialAccounts() {
      if (!this.addForm.formData.name) {
        this.$message({
          type: "error",
          message: "请先输入商家账号"
        });
        return;
      }
      const location = window.location
      var res = await getAuthUrl({
        adOrgName: this.addForm.formData.name,
        referer: location.origin + location.pathname,
        path: location.href.split('#')[1]
      });

      if (res && res.code === '0000000' && res.body) {
        window.location.href = res.body;
      }
    },
    async onConfirm() {
      if (!this.addForm.formData.name) {
        this.$message({
          type: "error",
          message: "商户名称"
        });
        return;
      }
      if (!this.addForm.formData.lyyOfficialAccoutId) {
        this.$message({
          type: "error",
          message: "请选择公众号"
        });
        return;
      }
      if (this.addForm.formData.messageType == "news") {
        this.addForm.formData.messageDescribe =
          "【微信昵称】您好，您扫码的设备编号为【设备编号】场地名称，欢迎使用";
        if (!this.addForm.formData.title) {
          this.$message({
            type: "error",
            message: "请选择公众号"
          });
          return;
        }
        if (!this.addForm.formData.pictureUrl) {
          this.$message({
            type: "error",
            message: "请上传图片"
          });
          return;
        }
      } else if (this.addForm.formData.messageType == "text") {
        this.addForm.formData.pictureUrl = "";
        this.addForm.formData.title = "";
        if (!this.addForm.formData.messageDescribe) {
          this.$message({
            type: "error",
            message: "请输入描述信息"
          });
          return;
        }
      }
      this.addForm.loading = true;
      var res = await saveOrUpdateTask(this.addForm.formData);
      if (res.result === 0) {
        this.$message({
          type: "success",
          message: this.addForm.title + "任务成功"
        });
      }
      this.addForm.visible = false;
      this.addForm.loading = false;

      this.table.pagination.page = 1;
      this.onGetList();
    },
    onReAuthBtnClick(row) {
      this.$confirm(
        "重新授权公众号后，若该任务下、线下已经打印了带参二维码且正在使用的，则可能会导致正在使用的带参二维码无法使用，请谨慎操作。确定要重新授权吗？",
        "提示",
        {
          confirmButtonText: "确定要重新授权",
          callback: async action => {
            if (action == "confirm") {
              var res = await againAuthUrl({ adOrgId: row.adOrgId });
              if (res.result == 0) {
                window.location.href = res.data;
              }
            }
          }
        }
      );
    },
    async onEditBtnClick(row) {
      this.table.loading = true;
      var res = await getOfficialAccountsTaskById({
        officialAccoutTaskId: row.officialAccoutTaskId
      });
      this.table.loading = false;
      if (res.result === 0) {
        if (res.data.taskState === 2) {
          this.onGetList();
          this.$message({
            type: "error",
            message: "请上传文件"
          });
          return;
        }
        this.addForm.visible = true;
        this.addForm.title = "编辑";
        this.addForm.formData = res.data;
      }
    },
    onStopBtnClick(row) {
      this.$confirm(
        "终止后，用户扫描原有的带参二维码时，系统将不再自动推送消息。确定要终止该任务吗？",
        "提示",
        {
          callback: async action => {
            if (action == "confirm") {
              var res = await modifyTaskState({
                officialAccoutTaskId: row.officialAccoutTaskId,
                taskState: 2
              });
              if (res.result == 0) {
                this.$message({
                  type: "success",
                  message: "终止任务成功"
                });
              }

              this.onGetList();
            }
          }
        }
      );
    },
    async onGenBtnClick(row) {
      this.genForm.groupIds = [];
      this.genForm.formData = {
        officialAccoutTaskId: "",
        qrcodeType: 1,
        type: 1,
        groupIds: "",
        file: null
      };
      this.genForm.visible = true;
      this.genForm.name = row.name;
      this.genForm.formData.officialAccoutTaskId = row.officialAccoutTaskId;
      var res = await getGroupsInfoByAdOrgId({ adOrgId: row.adOrgId });
      if (res.result === 0) {
        this.genForm.checkAll = false
        this.genForm.groups = res.data;
      }
    },
    async onGenerateQrcode() {
      console.log(this.genForm.formData);
      if (this.genForm.formData.type == 1) {
        delete this.genForm.formData.groupIds;
        if (!this.genForm.formData.file) {
          this.$message({
            type: "error",
            message: "请上传文件"
          });
          return;
        }
      }
      if (this.genForm.formData.type == 2) {
        delete this.genForm.formData.file;
        this.genForm.fileList = [];
        if (!this.genForm.groupIds.length) {
          this.$message({
            type: "error",
            message: "请选择场地"
          });
          return;
        }
        this.genForm.formData.groupIds = this.genForm.groupIds.join(",");
      }

      this.genForm.loading = true;
      var formData = new window.FormData();
      for (var key in this.genForm.formData) {
        formData.append(key, this.genForm.formData[key]);
      }

      var res = await createQRCodes(formData);
      if (res.result == 0) {
        this.$confirm(
          "已向微信接口发送请求生成带参二维码。此过程可能需要几分钟时间。请稍后点击列表左上角的 “生成记录” 下载带参二维码。",
          "提示",
          {
            showCancelButton: false,
            confirmButtonText: "知道了"
          }
        );
      }
      this.genForm.visible = false;
      this.genForm.loading = false;
      this.genForm.fileList = [];
    },
    handleSizeChange(val) {
      this.table.pagination.page = 1;
      this.table.pagination.pageSize = val;
      this.onGetList();
    },
    handleCurrentChange(val) {
      this.table.pagination.page = val;
      this.onGetList();
    },
    beforeAvatarUpload(file) {
      if (file.size > 1024 * 1024 * 2) {
        this.$message({
          type: "error",
          message: "文件大小必须小于2M"
        });
        return false;
      }
      this.addForm.imageLoading = true;
    },
    handleAvatarSuccess(res, file) {
      this.addForm.imageLoading = false;
      if (res.result === 1) {
        this.addForm.formData.pictureUrl =
          "http://lyy-public.oss-cn-shenzhen.aliyuncs.com/" + res.para.split(";")[0];
      } else {
        this.$message.error(res.description);
      }
    },
    handleFileChange(file, fileList) {
      console.log(file, fileList);
      this.genForm.formData.file = file.raw;
      this.genForm.fileList = [file];
    },
    handleRecordSizeChange(val) {
      this.recordTable.pagination.page = 1;
      this.recordTable.pagination.pageSize = val;
      this.onGetRecordList();
    },
    handleRecordCurrentChange(val) {
      this.recordTable.pagination.page = val;
      this.onGetRecordList();
    },
    async onGetRecordList() {
      this.recordTable.loading = true;
      var params = {
        pageSize: this.recordTable.pagination.pageSize,
        pageIndex: this.recordTable.pagination.page
      };
      var res = await getQRCodeCreateRecordList(params);
      if (res.result === 0) {
        this.recordTable.datas = res.data.items;
        this.recordTable.pagination.total = res.data.total;
      }

      this.recordTable.loading = false;
    },
    async onRecordBtnClick() {
      this.recordTable.visible = true;
      this.onGetRecordList();
    },
    handleMessageTypeChange(val) {
      if (val == "text") {
        this.addForm.formData.messageDescribe = "";
      }
    },
    jumpTo(url) {
      window.open(url);
    },
    querySearchAsync() {
      console.log(this.genForm.groups);
      let temp = [];
      this.genForm.groups.forEach(item => {
        if (item.name.indexOf(this.genForm.searchSite) == -1) {
          item.hide = true;
        } else {
          item.hide = false;
        }
        temp.push(item);
      });
      this.genForm.groups = temp;
      this.$set(this.genForm, "groups", this.genForm.groups);
    },
    handleSelectAll(val) {
      if (this.genForm.groupIds.length == this.genForm.groups.length) {
        this.genForm.groupIds = [];
        this.genForm.checkAll = false;
      } else if (this.genForm.groupIds.length != this.genForm.groups.length) {
        this.genForm.groupIds = this.genForm.groups.map(item => {
          return item.lyyEquipmentGroupId;
        });
        this.genForm.checkAll = true;
      }
    },
    handleCheckedSitesChange(val) {
      console.log(this.genForm.groupIds, this.genForm.groups);
      if (this.genForm.groupIds.length == this.genForm.groups.length) {
        this.genForm.checkAll = true;
      } else {
        this.genForm.checkAll = false;
      }
    }
  }
};
</script>

<style lang="less">
.official-qccount-qrcode {
  .header {
    .add-btn {
      margin-top: 10px;
    }
  }
  .content {
    margin-top: 10px;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    min-width: 120px;
    height: 120px;
    display: block;
  }
}
.group-content {
  max-height: 400px;
  overflow-y: scroll;
  overflow-x: hidden;
  .nodata {
    padding: 20px;
    text-align: center;
  }
  .search {
    margin-bottom: 10px;
  }
  .el-checkbox+.el-checkbox {
    margin-left: 0;
  }
  .el-checkbox {
    margin: 3px 0;
  }
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
  }
}
.group-select {
  display: inline-block;
  width: 200px;
  height: 26px;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  line-height: 26px;
  position: relative;
  padding: 0 5px;
  .el-icon-arrow-down {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
