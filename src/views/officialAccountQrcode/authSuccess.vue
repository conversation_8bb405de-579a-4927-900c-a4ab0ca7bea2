<!--
 * @Author: lijinrong
 * @Date: 2019-12-23 15:13:38
 * @LastEditors  : lijinrong
 * @LastEditTime : 2019-12-24 14:51:16
 * @Description: file content
 -->
<template>
  <div class="authsuccess" style="position: absolute;left:0;top: 0;width: 100%;height: 100%;">
    <div class="sucImgPanel">
      <img id="successImg" src="../../assets/images/success.png" height="200" width="200" />
      <p>授权成功，正在跳转...</p>
    </div>
  </div>
</template>

<script>
import { associateOfficial } from "../../api/officialAccount";
export default {
  methods: {
    async postAuthCodeData() {
      var params = {};
      var authCode = this.getQueryString("auth_code");
      var adOrgId = this.getQueryString("adOrgId");
      params.authCode = decodeURIComponent(authCode);
      params.adOrgId = adOrgId;
      // 上传授权结果
      var res = await associateOfficial(params);
      if (res.result === 0) {
        this.$router.push("/officialAccountQrcode");
      }
    },
    getQueryString(name) {
      let query = window.location.href.split("?")[1]; // 这里query的结果是：   promotion_code=ZB06AKXFSR&sku=100
      let vars = query.split("&"); // 这里vars的结果是：   ["promotion_code=ZB06AKXFSR", "sku=100"]
      for (let i = 0; i < vars.length; i++) {
        //然后循环
        let pair = vars[i].split("="); //  循环第一次结果pair ["promotion_code", "ZB06AKXFSR"]  循环第二次结果pair ["sku", "100"]
        if (pair[0] === name) {
          // 做判断 如果参数名和我们的实参一样
          return pair[1]; // 就返回对应的值
        }
      }
      return "";
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.postAuthCodeData();
    });
  }
};
</script>

<style scoped>
.sucImgPanel {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -100px;
  text-align: center;
}
</style>
