import searchModule from "@components/searchModule/searchModule";
import DetailDialog from '@/components/DetailDialog';
let channelLists =  [
  {label: '全部渠道',value: ''},
  {label: '汇付', value: 4},
  {label: '小微', value: 6},
  {label: '京东', value: 9},
  {label: '乐刷', value: 14},
];
const tableMixin = {
  components: {
    searchModule,
    DetailDialog,
  },
  computed:{
    channel() {
      return function(data) {
        if (!data.channelCode) {
          return '';
        }
        const list = channelLists.filter(item => {
          return +item.value === +data.channelCode;
        });
        return list.length ? list[0].label : data.channelCode;
      }
    }
  },
  data() {
    return {
      pageInfo: {
        total: 0,
        pageSize: 10,
        pageIndex: 1,
        loading: false,
        list: [],
      },
    }
  },
  methods: {
// 改变列表每页条数
    handleSizeChange(val) {
      this.pageInfo.pageIndex = 1;
      this.pageInfo.pageSize = val;
      this.getTableData();
    },
    // 改变页码
    handleCurrentChange(val) {
      this.pageInfo.pageIndex = val;
      this.getTableData();
    },
  }
};

export {
  channelLists,
  tableMixin,
}
