<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="商户变更简称" name="check">
      <div v-if="activeName === 'check'">
        <check-fail></check-fail>
      </div>
    </el-tab-pane>

    <el-tab-pane label="三要素缓存" name="element">
      <div v-if="activeName === 'element'">
        <tree-element-cache></tree-element-cache>
      </div>
    </el-tab-pane>

    <el-tab-pane label="渠道商户号替换" name="replace">
      <div v-if="activeName === 'replace'">
        <merchant-number-replace></merchant-number-replace>
      </div>
    </el-tab-pane>

    <el-tab-pane label="同名卡变更" name="change">
      <div v-if="activeName === 'change'">
        <same-name-card-change></same-name-card-change>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
  import checkFail from "./children/checkFail";
  import merchantNumberReplace from "./children/merchantNumberReplace";
  import sameNameCardChange from "./children/sameNameCardChange";
  import treeElementCache from "./children/treeElementCache";

  export default {
    name: "index",
    components: {
      checkFail,
      merchantNumberReplace,
      sameNameCardChange,
      treeElementCache
    },
    data() {
      return {
        activeName: 'check'
      }
    },
    mounted() {
      this.activeName = this.$route.query.activeName || 'check';
    },
    methods: {
      handleClick(tab, event) {
        this.activeName = tab.name;
        this.$router.push({
          path: '/importManagement',
          query: {activeName: this.activeName}
        })
      },
    }
  }
</script>

<style scoped>

</style>
