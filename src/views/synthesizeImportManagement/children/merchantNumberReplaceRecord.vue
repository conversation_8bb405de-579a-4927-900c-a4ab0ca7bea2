<template>
  <div>
    <el-button type="primary" size="small" class="return_btn" @click="handleReturn">返回</el-button>
    <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;">
      <el-table-column
        prop="orgValue"
        label="商户账号"
        align="center"
      />
      <el-table-column
        label="渠道"
        align="center">
        <template slot-scope="scope">
          {{channel(scope.row)}}
        </template>
      </el-table-column>
      <el-table-column
        label="变更前渠道号"
        align="center">
        <template slot-scope="scope">
          {{scope.row.oldValueObj && scope.row.oldValueObj.merchantCode}}
        </template>
      </el-table-column>
      <el-table-column
        label="变更后渠道号"
        align="center">
        <template slot-scope="scope">
          {{scope.row.newValueObj && scope.row.newValueObj.merchantCode}}
        </template>
      </el-table-column>
      <el-table-column
        prop="createdbyName"
        label="操作人"
        align="center"
      />
      <el-table-column
        label="操作时间"
        align="center"
      >
        <template slot-scope="scope">
          {{scope.row.updated | parseTime}}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="[10, 20, 50]"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      :current-page="pageInfo.pageIndex"
      background
      layout="total, prev, pager, next, sizes, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>

<script>
  import {queryLogsPageList} from '@/api/synthesizeImportManagement';
  import {tableMixin, channelLists} from "../mixin";

  export default {
    name: "merchantNumberReplaceRecord",
    mixins: [tableMixin],
    data() {
      return {
        orgValue: '',
      }
    },
    mounted() {
      this.orgValue = this.$route.query.orgValue;
      this.getTableData();
    },
    methods: {
      async getTableData() {
        this.pageInfo.loading = true;
        this.pageInfo.list = [];
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
          orgValue: this.orgValue || '',
          type: 902,
        };
        const res = await queryLogsPageList(params);
        this.pageInfo.loading = false;
        if (res && res.result === 0) {
          if (res.data && res.data.items) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        }
      },
      handleReturn() {
        this.$router.push({
          path: '/importManagement',
          query: {activeName: 'replace'}
        })
      }
    }
  }
</script>

<style scoped lang="less">
.return_btn {
  margin-bottom: 20px;
  width: 88px;
}
</style>
