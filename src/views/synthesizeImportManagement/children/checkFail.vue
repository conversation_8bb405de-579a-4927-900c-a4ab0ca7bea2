<template>
  <div>
    <searchModule
      :un-limit="true"
      :search-list="searchList"
      :search-form-data="searchFormData"
      @searchFun="handleSearchDataList">
      <el-form-item>
        <el-button @click="searchRecord">变更记录</el-button>
      </el-form-item>
    </searchModule>
    <div v-loading="pageInfo.loading">
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;">
        <el-table-column
          prop="orgValue"
          label="商家账号"
          align="center"
        />
<!--        <el-table-column-->
<!--          prop="account"-->
<!--          label="进件渠道"-->
<!--          align="center"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            {{channel(scope.row)}}-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column
          prop="merchantType"
          label="商户类型"
          align="center"
        >
          <template slot-scope="scope">
            {{merchant(scope.row)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="merchantName"
          label="商家名称"
          align="center"
        />
        <el-table-column
          label="操作"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handleUpdateName(scope.row)">修改名称</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 50]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <detail-dialog
      title="输入新名称"
      :visible="visible"
      confirmBtn="保存"
      :cancelFn="() => visible = false"
      :confirmFn="updateMerchantName"
    >
      <div slot="detail-container" class="container">
        <input type="text" placeholder="请输入商户名称" class="_input" v-model="popupInfo.merchantName"/>
      </div>
    </detail-dialog>
  </div>
</template>

<script>
import { tableMixin } from "../mixin";
import { queryAuditFailPageList, updateMerchantName } from '@/api/synthesizeImportManagement';

export default {
    name: "checkFail",
  mixins: [tableMixin],
    data() {
      return {
        searchList: [
          {
            type: 'input', // 检索类型,输入框
            label: '商户账号：', // label
            placeholder: '请输入商户账号', // 占位符
            valueName: 'orgValue', // 返回对应值名
            changeDataFun: (data) => {
              this.formData.orgValue = data || '';
            }
          },
          {
            type: 'input',
            label: '商户名称：',
            placeholder: '请输入商户名称',
            valueName: 'merchantName',
          },
        ],
        searchFormData: {},
        formData: {
          orgValue: '',
          merchantName: '',
        },
        popupInfo: {
          merchantName: '',
          lyySwiftpassMerchantId: '',
        },
        visible: false,
      }
    },
  computed:{
    merchant() {
      return function(data) {
        if (!data.merchantType) {
          return '';
        }
        switch (data.merchantType) {
          case '1':
            return '企业';
            break;
          case '2':
            return '个体工商户';
            break;
          case '3':
            return '个人';
            break;
          default:
            return '';
            break;
        }
      }
    }
  },
    mounted() {
      this.init();
    },

    methods: {
      init() {
        this.pageInfo.pageIndex = 1;
        this.popupInfo = {
          merchantName: '',
          lyySwiftpassMerchantId: '',
        };
        this.visible = false;
      },

      searchRecord() {
        if (!this.formData.orgValue) {
          this.$message.error('请输入商户账号');
          return;
        }
        this.$router.push({
          path: '/changeMerchantNameRecord',
          query: {orgValue: this.formData.orgValue}
        });
      },

      // 查询审核未通过
      handleSearchDataList(datas) {
        this.formData = Object.assign({},{
          orgValue: datas.formData.orgValue || '', // 先享卡卡号
          merchantName: datas.formData.merchantName || '', // 先享卡名称
          //channelCode: datas.formData.channelCode,
        });
        this.pageInfo.pageIndex = 1;
        this.getTableData();
      },

      async getTableData() {
        this.pageInfo.loading = true;
        this.pageInfo.list = [];
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
          ...this.formData
        };
        const res = await queryAuditFailPageList(params);
        this.pageInfo.loading = false;
        if (res && res.result === 0) {
          if (res.data && res.data.items) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        }
      },
      // 修改名称
      handleUpdateName(data) {
        this.popupInfo.merchantName = data.merchantName || '';
        this.popupInfo.lyySwiftpassMerchantId = data.lyySwiftpassMerchantId || '';
        this.visible = true;
      },
      async updateMerchantName() {
        const params = Object.assign({}, this.popupInfo);
        const res = await updateMerchantName(params);
        if (res && res.result === 0) {
          this.init();
          await this.getTableData();
        } else {
          this.$message.error("修改商户姓名失败");
        }
      },
    },
  }
</script>

<style scoped lang="less">
  /deep/ .container {
    ._input {
      border-radius: 4px;
      border: 1px solid #DCDFE6;
      height: 34px;
      line-height: 34px;
      width: 88%;
      padding-left: 15px;
    }
  }
</style>
