<template>
  <div>
    <searchModule
      :un-limit="true"
      :search-list="searchList"
      :search-form-data="searchFormData"
      @searchFun="handleSearchDataList">
    </searchModule>
    <div v-loading="pageInfo.loading">
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;">
        <el-table-column
          prop="name"
          label="名称"
          align="center"
        />
        <el-table-column
          prop="idCard"
          label="身份证号"
          align="center"
        />
        <el-table-column
          prop="accountCode"
          label="银行卡号"
          align="center"
        />
        <el-table-column
          prop="created"
          label="资料提交时间"
          align="center"
        >
          <template slot-scope="scope">
            {{scope.row.created && scope.row.created | parseTime}}
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="验证状态"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="+scope.row.status === 1" class="green">已通过</span>
            <span v-else-if="+scope.row.status === 0" class="red">未通过</span>
            <span v-else>未验证</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              v-if="+scope.row.status !== -1"
              @click="handleChangeStatus(scope.row)"
              type="text"
              :class="+scope.row.status === 1 ? 'red' : 'green'">
              变更为{{+scope.row.status === 1 ? '未通过' : '已通过'}}
            </el-button>
            <el-button type="text" @click="handleClearRecord(scope.row)">清除记录</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 50]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
  import { tableMixin } from "../mixin";
  import { queryCardVerifyLogPageList, updateVerifyLogStatus, deleteVerifyLog } from '@/api/synthesizeImportManagement';
  import {formatDate} from "@/utils/utils";

  export default {
    name: "treeElementCache",
    mixins: [ tableMixin ],
    data() {
      return {
        searchList: [
          {
            type: 'date', // 检索类型,时间选择器
            label: '资料提交时间：', // label
            valueName: 'dateArr', // 返回对应值名
            dateType: 'datetimerange', // 时间检索特有,时间选择器类型，使用element-ui中的el-date-picker，默认daterange
            startPlaceholder: '', // 开始占位符
            endPlaceholder: '', // 结束占位符
            valueFormat: 'yyyy-MM-dd HH:mm:ss', // 输出格式
            pickerOptions: {
              disabledDate(time) {
                let curDate = new Date().getTime()-86400000;
                let endDate = 365 * 86400000;
                let lastYear = curDate - endDate;
                return time.getTime() > Date.now() || time.getTime() < lastYear;
              },
            },
          },
          {
            type: 'input',
            label: '名称：',
            placeholder: '请输入名称',
            valueName: 'name',
          },
          {
            type: 'input',
            label: '身份证号：',
            placeholder: '请输入身份证号',
            valueName: 'idCard',
          },
          {
            type: 'input',
            label: '银行卡号：',
            placeholder: '请输入银行卡号',
            valueName: 'accountCode',
          },
        ],
        searchFormData: {
          dateArr: [new Date(new Date().setHours(0, 0, 0, 0)),new Date(new Date().getTime())]
        },
        formData: {
          name: '',
          idCard: '',
          accountCode: '',
          startTime: new Date().setHours(0, 0, 0, 0),
          endTime: new Date().getTime(),
        },
      }
    },
    methods: {
      async getTableData() {
        this.pageInfo.loading = true;
        this.pageInfo.list = [];
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
          ...this.formData
        };
        const res = await queryCardVerifyLogPageList(params);
        this.pageInfo.loading = false;
        if (res && res.result === 0) {
          if (res.data && res.data.items) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        }
      },

      handleSearchDataList(data) {
        this.pageInfo.pageIndex = 1;
        this.formData = Object.assign({}, {
          name: data.formData.name || '',
          accountCode: data.formData.accountCode || '',
          idCard: data.formData.idCard || '',
          startTime: new Date(data.formData.dateArr[0]).getTime(),
          endTime: new Date(data.formData.dateArr[1]).getTime()
        });
        this.getTableData();
      },
      handleChangeStatus({status, lyyCardVerifyLogId}) {
        let obj = {};
        if (+status === 1) {
          obj.content = '是否确认将验证状态改为未通过？';
          obj.params = {
            lyyCardVerifyLogId,
            status: 0,
          };
        } else if(+status === 0) {
          obj.content = '是否确认将验证状态改为已通过？'
          obj.params = {
            lyyCardVerifyLogId,
            status: 1,
          };
        }
        obj.methods = updateVerifyLogStatus;
        this.doConfirm(obj);
      },
      handleClearRecord({lyyCardVerifyLogId}) {
        let obj = {};
        obj.content = '是否确认要删除该记录？';
        obj.methods = deleteVerifyLog;
        obj.params = {
          lyyCardVerifyLogId
        };
        obj.sign = 'toDelete';
        this.doConfirm(obj);
      },
      doConfirm(obj) {
        this.$confirm(obj.content, '温馨提示',{
          confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
        })
        .then(async () => {
          const res = await obj.methods(obj.params);
          if (res && res.result === 0) {
            this.pageInfo.pageIndex = 1;
            await this.getTableData();
          }
        })
      }
    }
  }
</script>

<style scoped>
  .red {
    color: #F56C6C;
  }
  .green {
    color: #67C23A;
  }
</style>
