<template>
  <div>
    <searchModule
      :un-limit="true"
      :search-list="searchList"
      :search-form-data="searchFormData"
      @searchFun="handleSearchDataList">
      <el-form-item  v-loading="pageInfo.loading">
        <el-button @click="searchRecord">变更记录</el-button>
      </el-form-item>
    </searchModule>
    <div>
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;">
        <el-table-column
          prop="orgValue"
          label="商家账号"
          align="center"
        />
        <el-table-column
          prop="merchantName"
          label="商家名称"
          align="center"
        />
        <el-table-column
          label="渠道名称"
          align="center"
        >
          <template slot-scope="scope">
            {{channel(scope.row)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="merchantCode"
          label="启用中的渠道商户号"
          align="center"
        />
        <el-table-column
          label="操作"
          align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleReplaceChannel(scope.row)">替换渠道号</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 50]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <detail-dialog
      title="输入新的渠道号"
      confirmBtn="确认替换"
      :visible="visible"
      :cancelFn="() => visible = false"
      :confirmFn="handleValidateCode">
      <div slot="detail-container">
        <el-input v-model.trim="merchantCode"></el-input>
      </div>
    </detail-dialog>
  </div>
</template>

<script>
  import { queryMerchantPayChannelPageList, verifyMerchantCode, updateMerchantPayChannel } from '@/api/synthesizeImportManagement';
  import { tableMixin, channelLists } from "../mixin";
  export default {
    name: "merchantNumberReplace",
    mixins: [tableMixin],
    data() {
      return {
        visible: false,
        merchantCode: '', // 新的渠道号
        lyyMerchantInfoId: '',
        searchList: [
          {
            type: 'input',
            label: '商户账号：',
            placeholder: '请输入商户账号',
            valueName: 'orgValue',
            changeDataFun: (data) => {
              this.formData.orgValue = data || '';
            }
          },
          {
            type: 'input',
            label: '商户名称：',
            placeholder: '请输入商户名称',
            valueName: 'merchantName',
          },
          {
            type: 'select',
            label: '渠道：',
            placeholder: '请选择渠道',
            valueName: 'channelCode',
            options: [].concat(channelLists)
          },
          {
            type: 'input',
            label: '启用中的商户渠道号：',
            placeholder: '请输入启用中的商户渠道号',
            valueName: 'merchantCode',
          },
        ],
        searchFormData: {
          channelCode: '',
        },
        formData: {
          orgValue: '',
          merchantName: '',
          merchantCode: '',
          channelCode: ''
        },
      }
    },
    methods: {
      init() {
        this.pageInfo.pageIndex = 1;
        this.visible = false;
        this.merchantCode = '';
        this.getTableData();
      },
      async getTableData() {
        this.pageInfo.loading = true;
        this.pageInfo.list = [];
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
          ...this.formData
        };
        const res = await queryMerchantPayChannelPageList(params);
        this.pageInfo.loading = false;
        if (res && res.result === 0) {
          if (res.data && res.data.items) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        }
      },
      // 查询变更记录
      searchRecord() {
        if (!this.formData.orgValue) {
          this.$message.warning('请输入商户账号');
          return;
        }
        this.$router.push({
          path: '/replaceMerchantNumberRecord',
          query: {orgValue: this.formData.orgValue}
        });
      },
      // 查询列表
      handleSearchDataList(data) {
        this.pageInfo.pageIndex = 1;
        this.formData = Object.assign({}, this.formData, {
          orgValue: data.formData.orgValue || '',
          merchantCode: data.formData.merchantCode || '',
          merchantName: data.formData.merchantName || '',
          channelCode: data.formData.channelCode || ''
        });
        this.getTableData();
      },
      // 校验渠道号
      async handleValidateCode() {
        if (!this.merchantCode) {
          this.$message.error('请输入新的渠道号');
          return;
        };
        const params = {
          lyyMerchantInfoId: this.lyyMerchantInfoId,
          merchantCode: this.merchantCode
        };
        const res = await verifyMerchantCode(params);
        if (res && res.result === 0) {
          this.$confirm('即将替换新的渠道号，将会启用新的渠道号原启用中的渠道号将会被覆盖,是否继续？', '温馨提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            const resp = await updateMerchantPayChannel(params);
            if (resp && resp.result === 0) {
              this.$message({
                dangerouslyUseHTMLString: true,
                message: '<p>替换成功</p> <p>已启用新替换的渠道号</p>',
                type: 'success',
              });
              this.init();
            }
          }).catch(() => {
            this.merchantCode = '';
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
        } else {
          let content;
          if (res.data) {
            content = `<div style="width: 100%">
                        <p>替换失败</p>
                        <p>输入的渠道号所有者为</p>
                        <p>姓名：<span>${res.data.newName || ''}</span>，身份证号：<span>${res.data.newIdCard || ''}</span>，银行卡号：<span>${res.data.newAccount || ''}</span>
                        </p>
                        <p style="margin-top:20px">与待替换者</p>
                        <p>姓名：<span>${res.data.name || ''}</span>，身份证号：<span>${res.data.idCard || ''}</span>，银行卡号：<span>${res.data.account || ''}</p>
                        <p style="margin-top:20px">${res.data.merchantType === '1' ? '非同一个企业' : '非同一个人'}</p>
                       </div>`;
          } else {
            content = res.description || '替换账号失败';
          }
          this.$alert(content, '替换失败', {
            customClass: res.data ? 'special-message-content' : '',
            dangerouslyUseHTMLString: true,
            confirmButtonText: '关闭',
          });
        }
        this.visible = false;
      },
      // 替换渠道号
      handleReplaceChannel(data) {
        this.lyyMerchantInfoId = data.lyyMerchantInfoId;
        this.visible = true;
      }
    },

  }
</script>

<style>
  .special-message-content {
    width: 640px;
  }
</style>
