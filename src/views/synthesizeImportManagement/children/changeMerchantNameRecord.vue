<template>
  <div v-loading="pageInfo.loading">
    <el-button type="primary" size="small" class="return_btn" @click="handleReturn">返回</el-button>
    <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;">
      <el-table-column
        prop="orgValue"
        label="商户账号"
        align="center"/>
      <el-table-column
        label="变更前名称"
        align="center">
        <template slot-scope="scope">
          {{scope.row.oldValueObj && scope.row.oldValueObj.merchantName}}
        </template>
      </el-table-column>
      <el-table-column
        label="变更后名称"
        align="center">
        <template slot-scope="scope">
          {{scope.row.newValueObj && scope.row.newValueObj.merchantName}}
        </template>
      </el-table-column>
      <el-table-column
        prop="createdbyName"
        label="操作人"
        align="center"/>
      <el-table-column
        prop="updated"
        label="操作时间"
        align="center">
        <template slot-scope="scope">
          {{scope.row.updated && scope.row.updated | parseTime}}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="[10, 20, 50]"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      :current-page="pageInfo.pageIndex"
      background
      layout="total, prev, pager, next, sizes, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>

<script>
  import { tableMixin } from "../mixin";
  import { queryLogsPageList } from '@/api/synthesizeImportManagement';

  export default {
    name: "changeMerchantNameRecord",
    mixins: [tableMixin],
    data() {
      return {
        orgValue: '',
        dataList: [],
      }
    },
    mounted() {
      this.orgValue = this.$route.query.orgValue;
      this.getTableData();
    },
    methods: {
      async getTableData() {
        this.pageInfo.loading = true;
        this.pageInfo.list = [];
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
          orgValue: this.orgValue,
          type: 901,
        };
        const res = await queryLogsPageList(params);
        this.pageInfo.loading = false;
        if (res.data && res.data.items) {
          this.pageInfo.list = res.data.items;
          this.pageInfo.total = res.data.total;
        } else {
          this.$message.error(res.description || '没有该商户变更记录!');
        }
      },
      handleReturn() {
        this.$router.push({
          path: '/importManagement',
          query: {activeName: 'check'}
        })
      }
    }
  }
</script>

<style scoped lang="less">
  .return_btn {
    margin-bottom: 20px;
    width: 88px;
  }
</style>
