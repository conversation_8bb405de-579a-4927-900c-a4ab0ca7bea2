<template>
  <div>
    <searchModule
      :un-limit="true"
      :search-list="searchList"
      :search-form-data="searchFormData"
      @searchFun="handleSearchDataList"/>
    <div v-loading="pageInfo.loading">
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;">
        <el-table-column
          prop="orgValue"
          label="商家账号"
          align="center"
        />
        <el-table-column
          prop="merchantName"
          label="商家名称"
          align="center"
        />
        <el-table-column
          width="240px"
          label="旧卡"
          align="center"
        >
          <template slot-scope="scope">
            <div @click="handleViewDetail(scope.row)" v-show="scope.row.oldValue !== '{}'">
              <img :src="scope.row.oldValueObj.accountImg" alt="" class="_img" v-if="isLoadSensitiveImg && scope.row.oldValueObj && scope.row.oldValueObj.accountImg">
              <p>开户行:{{bank(scope.row.oldValueObj)}}</p>
              <p>银行卡号:{{scope.row.oldValueObj.accountCode}}</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          width="240px"
          label="新卡"
          align="center"
        >
          <template slot-scope="scope">
            <div @click="handleViewDetail(scope.row)" v-show="scope.row.newValue !== '{}'">
              <img :src="scope.row.newValueObj.accountImg" alt="" class="_img" v-if="isLoadSensitiveImg && scope.row.newValueObj && scope.row.newValueObj.accountImg">
              <!--            <img :src="imgPrefix + scope.row.newValueObj.accountImg" alt="" class="_img">-->
              <p>开户行:{{bank(scope.row.newValueObj)}}</p>
              <p>银行卡号:{{scope.row.newValueObj.accountCode}}</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="applyReason"
          label="申请原因"
          align="center"
        />
        <el-table-column
          label="创建时间"
          align="center"
        >
          <template slot-scope="scope">
            {{scope.row.created | parseTime}}
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          align="center"
        >
          <template slot-scope="scope">
            {{status(scope.row)}}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" style="color: #F56C6C" @click="handleUpdateStatus(scope.row, 4)" v-if="scope.row.status === 0">拒绝</el-button>
            <el-button type="text" @click="handleUpdateStatus(scope.row, 3)" v-if="scope.row.status === 0">驳回</el-button>
            <el-button type="text" @click="handleUpdateStatus(scope.row, 2)" v-if="scope.row.status === 0">通过</el-button>
            <el-button type="text" @click="handleViewDetail(scope.row)" v-if="scope.row.status !== 0">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 50]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <detail-dialog
      title="详情"
      :has-footer="false"
      width="80%"
      :cancelFn="closeDialog"
      :visible="popupInfo.visible">
      <div slot="detail-container">
        <div class="content">
          <div class="content-left">
            <h3>旧卡信息</h3>
            <img :src="popupInfo.oldValueObj.accountImg" alt="" class="detail-img">
            <p>银行名称:{{bank(popupInfo.oldValueObj || {})}}</p>
            <p>支行名称:{{popupInfo.oldValueObj.contactLineName || ''}}</p>
            <p>银行卡号:{{popupInfo.oldValueObj.accountCode}}</p>
            <p>支行省份:{{province(popupInfo.oldValueObj || {})}}</p>
            <p>支行城市:{{city(popupInfo.oldValueObj || {})}}</p>
          </div>
          <div class="content-right">
            <h3>新卡信息</h3>
            <img :src="popupInfo.newValueObj.accountImg" alt="" class="detail-img">
            <p>银行名称:{{bank(popupInfo.newValueObj || {})}}</p>
            <p>支行名称:{{popupInfo.newValueObj.contactLineName || ''}}</p>
            <p>银行卡号:{{popupInfo.newValueObj.accountCode}}</p>
            <p>支行省份:{{province(popupInfo.newValueObj || {})}}</p>
            <p>支行城市:{{city(popupInfo.newValueObj || {})}}</p>
          </div>
        </div>
        <div class="footer" v-if="+popupInfo.status === 0">
          <el-button type="danger" @click="handleUpdateStatus(popupInfo, 4)" class="btn">拒绝</el-button>
          <el-button type="warning" @click="handleUpdateStatus(popupInfo, 3)" class="btn">驳回</el-button>
          <el-button type="success" @click="handleUpdateStatus(popupInfo, 2)" class="btn">通过</el-button>
        </div>
      </div>
    </detail-dialog>
    <detail-dialog
      title="驳回理由"
      width="40%"
      :cancelFn="cancelFun"
      :confirmFn="updateMerchantName"
      :visible="inputDialogVisible">
      <div slot="detail-container" class="container">
        <el-input type="textarea" v-model.trim="rejectReason"></el-input>
      </div>
    </detail-dialog>
  </div>
</template>

<script>
  import { tableMixin } from "../mixin";
  import { queryMerChangeCardLogsPageList, getAreaTree, getBankList, auditApply, getLyyMerchantApplyLogDetail,getSensitiveFileByName  } from '@/api/synthesizeImportManagement';
  let statusList = [
    {label: '全部',value: ''},
    {label: '待处理', value: 0},
    {label: '处理中', value:1},
    {label: '完成', value: 2},
    {label: '驳回', value: 3},
    {label: '拒绝', value: 4},
  ];
  export default {
    name: "sameNameCardChange",
    mixins:[tableMixin],
    computed: {
      status() {
        return function(item) {
          const status = item.status;
          const list = statusList.filter(item => {
            return item.value === status;
          });
          return list.length ? list[0].label : '';
        }
      },
      img() {
        return function(item) {
          return item.accountImg ? (this.imgPrefix + item.accountImg) : ''
        }
      },
      bank() {
        return function(item) {
          const list = this.bankList.length && this.bankList.filter(v => {
            return v.code === item.bankCode;
          });
          return list.length && list[0].name;
        }
      },
      province() {
        return function(item) {
          if (JSON.stringify(item) === '{}') {
            return;
          }
          const list = this.areaList.length && this.areaList.filter(v => {
            return v.code === item.provinceCode;
          });
          return list.length && list[0].name || '';
        }
      },
      city() {
        return function (item) {
          if (JSON.stringify(item) === '{}') {
            return;
          }
          const province = this.areaList.length && this.areaList.filter(v => {
            return v.code === item.provinceCode;
          });
          const cityList = province.length && province[0].children;
          const city = cityList.length && cityList.filter(v => {
            return v.code === item.cityCode;
          });
          return city.length && city[0].name || '';
        }
      }
    },
    watch: {
    "pageInfo.list": async function(newVal) {
      console.log("newVal", newVal);
      this.isLoadSensitiveImg = false
      if (newVal.length) {
        const fileName = newVal.reduce((pre, cur) => {
          return [...pre, cur.newValueObj.accountImg, cur.oldValueObj.accountImg];
        }, []);
        const { data } = await getSensitiveFileByName({ fileName });
        newVal.forEach((item, index) => {
          item.newValueObj.accountImg = data[index];
          item.oldValueObj.accountImg = data[index + 1];
        });
        this.isLoadSensitiveImg = true // 标记图片替换完成

      }
    }
  },
    async mounted() {
      //this.init();
      await Promise.all([this.getAreaFun(), this.getBankListFun(), this.getTableData()]);
    },
    data() {
      return {
        isLoadSensitiveImg:false,
        imgPrefix:'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/',
        popupInfo: {
          visible: false,
          oldValueObj: {},
          newValueObj: {},
        },
        inputDialogVisible: false,
        searchList: [
          {
            type: 'date', // 检索类型,时间选择器
            label: '创建时间：', // label
            valueName: 'dateArr', // 返回对应值名
            dateType: 'datetimerange', // 时间检索特有,时间选择器类型，使用element-ui中的el-date-picker，默认daterange
            startPlaceholder: '', // 开始占位符
            endPlaceholder: '', // 结束占位符
            valueFormat: 'yyyy-MM-dd HH:mm:ss', // 输出格式
            pickerOptions: {
              disabledDate(time) {
                let curDate = new Date().getTime()-********;
                let endDate = 365 * ********;
                let lastYear = curDate - endDate;
                return time.getTime() > Date.now() || time.getTime() < lastYear;
              },
            },
          },
          {
            type: 'select',
            label: '商户类型：',
            placeholder: '请选择商户类型',
            valueName: 'applyMerchantType',
            options: [
              {label: '全部',value: ''},
              {label: '主账号', value: '1'},
              {label: '分成方', value: '2'},
            ]
          },
          {
            type: 'input',
            label: '商户账号：',
            placeholder: '请输入商户账号',
            valueName: 'orgValue',
            changeDataFun: (datas) => {
              this.formData.orgValue = datas || '';
            },
          },
          {
            type: 'input',
            label: '商户名称：',
            placeholder: '请输入商户名称',
            valueName: 'merchantName',
          },
          {
            type: 'select',
            label: '状态：',
            placeholder: '',
            valueName: 'status',
            options:  [].concat(statusList),
          },
        ],
        searchFormData: {
          orgValue: '', // 商户账号
          merchantName: '', // 商户账号
          dateArr: [new Date(new Date().setHours(0, 0, 0, 0)),new Date(new Date().getTime())],
          applyMerchantType: '2',
          status: '',
        },
        formData: {
          orgValue: '', // 商户账号
          merchantName: '', // 商户名称
          startTime: new Date().setHours(0, 0, 0, 0),
          endTime: new Date().getTime(),
          applyMerchantType: '2',
          status: '',
        },
        areaList: [],
        bankList: [],
        sendData: {},
        rejectReason: '',
      }
    },
    methods: {
      init() {
        this.pageInfo.pageIndex = 1;
        this.getTableData();
      },
      async getAreaFun() {
        const res = await getAreaTree();
        if (res && res.result === 0 && res.data) {
          this.areaList = res.data || [];
        }
      },
      async getBankListFun() {
        const res = await getBankList();
        if (res && res.result === 0 && res.data.length) {
          this.bankList = res.data;
        }
      },
      async getTableData() {
        this.pageInfo.loading = true;
        this.pageInfo.list = [];
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
          ...this.formData,
        };
        const res = await queryMerChangeCardLogsPageList(params);
        this.pageInfo.loading = false;
        if (res && res.result === 0) {
          if (res.data && res.data.items) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        }
      },
      cancelFun() {
        this.inputDialogVisible = false;
        this.rejectReason = '';
      },
      closeDialog() {
        this.popupInfo.visible = false;
      },
      handleSearchDataList(data) {
        // this.pageInfo.pageIndex = 1;
        this.formData = Object.assign({}, {
          merchantName: data.formData.merchantName,
          startTime: new Date(data.formData.dateArr[0]).getTime(),
          endTime: new Date(data.formData.dateArr[1]).getTime(),
          applyMerchantType: data.formData.applyMerchantType,
          status: data.formData.status,
        });
        this.getTableData();
      },
      handleUpdateStatus(data, status) {
        this.sendData = {};
        const { adOrgId, applyNo, type } = {...data};
        this.sendData = Object.assign({}, {adOrgId, applyNo, type, status});
        if (status === 3) {
          this.inputDialogVisible = true;
          return;
        }
        this.auditApplyFun();
      },
      // 查看卡详情
      async handleViewDetail({lyyMerchantApplyLogId,newValueObj,oldValueObj}) {
          const params = {
            lyyMerchantApplyLogId: lyyMerchantApplyLogId || ''
          };
         const res = await getLyyMerchantApplyLogDetail(params);
         if (res && res.result === 0) {
           //this.popupInfo = JSON.parse(JSON.stringify(res.data));
           this.popupInfo = Object.assign({}, this.popupInfo, res.data);
           this.popupInfo.newValueObj.accountImg = newValueObj.accountImg;
           this.popupInfo.oldValueObj.accountImg = oldValueObj.accountImg;
           this.popupInfo.visible = true;
         } else {
           this.$message.error(res && res.description || '无法获取详情信息');
         }
      },
      updateMerchantName() {
        if (!this.rejectReason) {
          this.$message.warning('请输入驳回的理由');
          return;
        }
        this.auditApplyFun();
      },
      async auditApplyFun() {
        let params = Object.assign({}, this.sendData);
        if (this.rejectReason) {
          params.rejectReason = this.rejectReason;
        }
        const res = await auditApply(params);
        if (res && res.result === 0) {
          this.inputDialogVisible = false;
          this.init();
        } else {
          this.$message.error(res && res.description || '更新操作失败');
        }
        this.rejectReason = '';
      }
    }
  }
</script>

<style scoped lang="less">
  ._img {
    width: 50px;
    height: 40px;
  }
  .content {
    width: 100%;
    display: flex;
    justify-content: space-around;

    .content-left {
      margin-left: 10%;
      width: 50%;
    }
    .content-right {
      width: 50%;
    }

    .detail-img {
      width: 80%;
      height: 300px;
      padding: 20px 0;
    }
    p {
      line-height: 28px;
      font-size: 16px;
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    .btn {
      width: 120px;
      margin-right: 20px;
    }
  }

</style>
