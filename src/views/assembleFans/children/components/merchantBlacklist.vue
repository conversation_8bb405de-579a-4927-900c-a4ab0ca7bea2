<!--
 * @Description: 商户黑名单
 * @Author: wutao
 * @Date: 2021/12/2 15:25
 * @LastEditors: wutao
 * @LastEditTime: 2021/12/2 15:25
 -->
<template>
  <div>
    <el-table
      border
      :data="tableData"
      style="width: 100%"
      height="600"
      v-loading.lock="LoadingFlag"
    >
      <el-table-column
        prop="equipmentTypeId"
        label="设备类型"
      >
        <template slot-scope="scope">
          <span v-for="item in equipmentInfo" v-if="scope.row.equipmentTypeId === item.id">{{ item.name }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="blacklist"
        label="黑/白名单"
      >
        <template slot-scope="scope">
          <span>{{scope.row.blacklist? '黑名单':'白名单'}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
      >
        <template slot-scope="scope">
          <el-button @click="handleCheckList(scope.row)" type="text" size="small">名单管理</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--  黑白名单列表  -->
    <el-dialog :title="blackWhiteList.title" :visible.sync="blackWhiteList.visible" width="60%">
      <el-form :inline="true">
        <el-form-item label="手机号码：">
          <el-input class="inline-input" v-model.trim="searchData.phone" placeholder="请输入手机号码" clearable></el-input>
        </el-form-item>
        <el-form-item label="商户名称：">
          <el-input class="inline-input" v-model.trim="searchData.distributorName" placeholder="请输入商户名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getDistributorBlackListFn(true)">查询</el-button>
          <el-button type="primary" @click="addMerchantDialogFn">新增商户</el-button>
          <el-button type="success" icon="el-icon-upload2" @click.native="triggerClick" readonly>批量导入商户ID</el-button>
          <input type="file" ref="file" hidden accept=".excel,.xls,.xlsx" @change="handleImport"/>
          <el-button type="warning" icon="el-icon-delete" @click="deleteMultipleDistributor">批量删除</el-button>
        </el-form-item>
      </el-form>
      <el-table
        border
        :data="blackWhiteList.tableData"
        style="width: 100%"
        height="600"
        v-loading.lock="blackWhiteList.LoadingFlag"
        @selection-change="handleSelectionDistributor"
      >
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column
          prop="lyyDistributorId"
          label="商户id"
        >
        </el-table-column>
        <el-table-column
          prop="lyyDistributorName"
          label="商户名称"
        >
        </el-table-column>
        <el-table-column
          prop="lyyDistributorPhone"
          label="商家手机号"
        >
        </el-table-column>
        <el-table-column
          prop="lyyEquipmentTypeId"
          label="设备类型"
        >
          <template slot-scope="scope">
            <span v-for="item in equipmentInfo" v-if="scope.row.lyyEquipmentTypeId === item.id">{{ item.name }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
        >
          <template slot-scope="scope">
            <el-button @click="deleteDistributorBlackListFn(scope.row.lyyDistributorId)" type="text" size="small">删除</el-button>
            <el-button @click="showEditGroupDialog(scope.row)" type="text" size="small" v-if="blackWhiteList.title === '白名单管理'">编辑场地</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination margin-top-10">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="blackWhiteList.pageNum"
          :page-sizes="[10, 20, 50]"
          :page-size="blackWhiteList.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="blackWhiteList.total"
        >
        </el-pagination>
      </div>
    </el-dialog>
    <!--  新增商户  -->
    <el-dialog title="新增商户" :visible.sync="addMerchantDialog.visible" width="760px">
      <el-form>
        <el-form-item prop="id">
          <el-select v-model="addMerchantDialog.curSelect">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model.number="addMerchantDialog.phone" style = "width:400px;"></el-input>
        </el-form-item>
        <el-table
          ref="multipleTableRef"
          :hidden="hiddenMerchantTable"
          :data="merchantList"
          max-height="450"
          highlight-current-row
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column property="adOrgId" label="商家ID" />
          <el-table-column property="name" label="商家名称" />
          <el-table-column property="adOrgName" label="用户账号" />
        </el-table>
      </el-form>
      <span slot="footer">
        <el-button @click="addMerchantDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="addDistributorBlackListFn()">新 增</el-button>
      </span>
    </el-dialog>
    <!--  编辑场地  -->
    <el-dialog title="编辑场地" :visible.sync="editGroupDialog.visible" width="40%">
      <el-form :inline="true">
        <p class="edit-group-title"><span>商家ID：{{editGroupDialog.lyyDistributorId}}</span><span>商家名称：{{editGroupDialog.lyyDistributorName}}</span></p>
        <el-form-item label="">
          <el-transfer v-model="editGroupDialog.groupIds" :data="editGroupDialog.groupList"
                       :titles="['场地（启用）', '场地（禁用）']"></el-transfer>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="editGroupDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="saveEquipmentGroupListFn()">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDistributorEquipmentType,
  getDistributorBlackList,
  deleteDistributorBlackList,
  addDistributorBlackList,
  getEquipmentGroupList,
  saveEquipmentGroupList,
  batchDeleteDistributor,
  importDistributorId,
} from '@/api/advertisement/assembleFans';
import { EQ_TYPE_ID } from "@/views/assembleFans/children/js/config";

  export default {
    name: "merchantBlacklist",
    data() {
      return {
        hiddenMerchantTable:true,
        merchantList:[],
        options: [
          {
            value: 1,
            label: '手机号码',
          }, {
            value: 2,
            label: '商家ID'
          }
        ],
        LoadingFlag: false,
        tableData: [],
        blackWhiteList: {
          visible: false,
          title: '黑名单管理',
          LoadingFlag: false,
          total: 0,//总数
          pageNum: 1,//当前页码
          pageSize: 10,//每页大小
          tableData: [],
          deleteIdArr: [], // 删除项id
        },
        searchData: {
          lyyEquipmentTypeId: null,
          phone: '',
          distributorName: '',
        },
        addMerchantDialog: {
          curSelect:1,
          visible: false,
          phone: '',
        },
        editGroupDialog: {
          visible: false,
          groupList: [],
          groupIds: [],
          lyyDistributorId: null,
          lyyDistributorName: '',
        },
        equipmentInfo: EQ_TYPE_ID,
      };
    },
    mounted() {
      this.getDistributorEquipmentTypeFn();
    },
    methods: {
      // 查询设备黑白名单
      async getDistributorEquipmentTypeFn() {
        this.LoadingFlag = true;
        const res = await getDistributorEquipmentType();
        this.LoadingFlag = false;
        this.tableData = res.result === 0? res.data: [];
      },
      // 查看名单列表
      handleCheckList(info) {
        const { equipmentTypeId, blacklist } = info;
        this.searchData.lyyEquipmentTypeId = equipmentTypeId;
        this.blackWhiteList.title = blacklist? '黑名单管理' : '白名单管理';
        this.blackWhiteList.visible = true;
        this.getDistributorBlackListFn(true);
      },
      // 新增商户
      addMerchantDialogFn(){
        this.addMerchantDialog.visible = true
        this.addMerchantDialog.curSelect = 1;
        this.addMerchantDialog.phone = '';
        this.hiddenMerchantTable = true;
        this.merchantList = [];

      },
      // 分页选择
      handleSizeChange(val) {
        this.blackWhiteList.pageSize = val;
        this.getDistributorBlackListFn();
      },
      handleCurrentChange(val) {
        this.blackWhiteList.pageNum = val;
        this.getDistributorBlackListFn();
      },
      handleSelectionChange(user){
        this.addMerchantDialog.phone = null;
        user.forEach(item => {
          if(this.addMerchantDialog.phone !== null && this.addMerchantDialog.phone !==''){
            this.addMerchantDialog.phone = this.addMerchantDialog.phone + ","+ item.adOrgId
          }else {
            this.addMerchantDialog.phone = item.adOrgId
          }
        })
      },
      // 查询商家开通名单
      async getDistributorBlackListFn(isInit) {
        if (isInit) { this.blackWhiteList.pageNum = 1}; //初始化页数
        let params = {
          pageNum: this.blackWhiteList.pageNum,
          pageSize: this.blackWhiteList.pageSize,
          ...this.searchData,
        };
        this.blackWhiteList.LoadingFlag = true;
        const res = await getDistributorBlackList(params);
        this.blackWhiteList.LoadingFlag = false;
        this.blackWhiteList.total = res.result === 0? res.data.total: 0;
        this.blackWhiteList.tableData = res.result === 0? res.data.list: [];
      },
      // 删除屯粉商家名单
      deleteDistributorBlackListFn(id) {
        this.$confirm('确认删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const params = {
            id,
            equipmentTypeId: this.searchData.lyyEquipmentTypeId,
          }
          const res = await deleteDistributorBlackList(params);
          if (res && res.result == 0) {
            this.$message.success('删除成功！');
            this.getDistributorBlackListFn(true);
          } else {
            this.$message.error(res.description);
          }
        }).catch(() => {});
      },
      // 添加屯粉商家名单
      async addDistributorBlackListFn() {
        if (!this.addMerchantDialog.phone) {
          this.$message.error('商户手机号码不能为空！');
          return;
        }
        const params = {
          phone: this.addMerchantDialog.phone,
          lyyEquipmentTypeId: this.searchData.lyyEquipmentTypeId,
          curSelect: this.addMerchantDialog.curSelect
        }
        const res = await addDistributorBlackList(params);
        if(res?.code == "666" && res.body){
          this.hiddenMerchantTable = false;
          this.merchantList = res.body
          this.$refs.multipleTableRef.toggleAllSelection();
          this.addMerchantDialog.phone = '';
          this.addMerchantDialog.curSelect = 2;
          return
        }
        if (res.result === 0) {
          this.addMerchantDialog.visible = false;
          if(res?.data == false){
            this.$message.error(res.description);
          }else {
            this.$message.success(res.description);
          }
          this.getDistributorBlackListFn(true);
        }
      },
      // 显示编辑场地弹窗
      showEditGroupDialog(info) {
        const { lyyDistributorId,  lyyDistributorName } = info;
        this.editGroupDialog.visible = true;
        this.editGroupDialog.lyyDistributorId = lyyDistributorId;
        this.editGroupDialog.lyyDistributorName = lyyDistributorName;
        this.getEquipmentGroupListFn();
      },
      // 查询场地名单列表
      async getEquipmentGroupListFn() {
        const params = {
          lyyEquipmentTypeId: this.searchData.lyyEquipmentTypeId,
          distributorId: this.editGroupDialog.lyyDistributorId,
        }
        const res = await getEquipmentGroupList(params);
        if (res.result === 0) {
          const allGroup = res.data.groupOpenList.concat(res.data.groupCloseList);
          this.editGroupDialog.groupList = allGroup.map(item => {
            return {
              key: item.lyyEquipmentGroupId,
              label: item.lyyEquipmentGroupName,
              disabled: false,
            }
          }) || [];
          this.editGroupDialog.groupIds = res.data.groupCloseList.map(item => {
            return item.lyyEquipmentGroupId
          }) || [];
        }
      },
      // 保存场地名单
      async saveEquipmentGroupListFn() {
        const params = {
          lyyDistributorId: this.editGroupDialog.lyyDistributorId,
          lyyEquipmentTypeId: this.searchData.lyyEquipmentTypeId,
          lyyEquipmentGroupIdList: this.editGroupDialog.groupIds,
        }
        const res = await saveEquipmentGroupList(params);
        if (res.result === 0) {
          this.editGroupDialog.visible = false;
          this.$message.success('保存成功！');
          this.getDistributorBlackListFn(true);
        }
      },
      // 执行导入
      async handleImport(options) {
        let formData = new FormData();
        formData.append('file', options.target.files[0]);
        const res = await importDistributorId(this.searchData.lyyEquipmentTypeId,formData);
        if (res.result === 0) {
          this.$message.success("导入成功");
          this.getDistributorBlackListFn(true);
        }
        this.$refs.file.value = null;
      },
      // 点击导入按钮
      triggerClick() {
        this.$refs.file.dispatchEvent(new MouseEvent("click"));
      },
      // 删除多选
      handleSelectionDistributor(val) {
        this.blackWhiteList.deleteIdArr = val;
      },
      // 删除多个商家
      deleteMultipleDistributor(){
        if (!this.blackWhiteList.deleteIdArr.length){
          this.$message.error('请至少选中一条记录删除！');
          return;
        }
        let idArr = [];
        this.blackWhiteList.deleteIdArr.map(item =>{
          idArr.push({lyyDistributorId: item.lyyDistributorId, lyyEquipmentTypeId: item.lyyEquipmentTypeId})
        })
        this.$confirm('确认删除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const res = await batchDeleteDistributor(idArr);
          if (res && res.result == 0) {
            this.$message.success('删除成功！');
            this.getDistributorBlackListFn(true);
          } else {
            this.$message.error(res.description);
          }
        }).catch(() => {});
      },
    },
  }
</script>

<style lang=less scoped>
  .edit-group-title{
    padding-bottom: 30px;
    span{
      &:first-child{
        margin-right: 20px;
      }
    }
  }
</style>
