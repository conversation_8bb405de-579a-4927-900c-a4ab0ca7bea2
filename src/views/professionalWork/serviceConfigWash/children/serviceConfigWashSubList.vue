<!--
日期：2023/10/24
功能：新增/编辑服务套餐配置列表
作者：庄炯权杨楠锋
-->
<template>
  <div>
    <div class="config-title">
      <p class="text">服务套餐配置</p>
      <el-button class="btn" type="success" icon="el-icon-circle-plus-outline" @click="addBtn"
                 :disabled="servicePackageConfig.length === maxservicePackageConfig">
        添加({{servicePackageConfig.length}}/{{maxservicePackageConfig}})
      </el-button>
    </div>
    <div class="service-package-box">
      <div v-for="(item, index) in servicePackageConfig" :key="index">
        <el-form :inline="true"
                 :model="item"
                 ref="ruleForm"
                 :rules="rules"
                 :key="index"
                 class="service-package-form-box">
          <el-form-item>
            <el-button type="primary" icon="el-icon-caret-top" circle plain :disabled="index === 0"
                       @click="upBtn(index)"></el-button>
            <el-button type="primary" icon="el-icon-caret-bottom" circle plain
                       :disabled="index === servicePackageConfig.length -1"
                       @click="downBtn(index)"></el-button>
            <el-button type="danger" icon="el-icon-delete" circle plain @click="deleteBtn(index)"></el-button>
          </el-form-item>

          <!-- 洗衣机，洗鞋机 -->
          <template v-if="!isHGJ">
            <el-form-item label="套餐模式" prop="startModelTypeId">
              <!-- <el-input class="input" v-model.trim="item.serviceName" :maxLength="maxServiceName" placeholder="请输入套餐模式">
              </el-input> -->
              <el-select v-model="item.startModelTypeId" :disabled="isEdit && !!item.id" filterable placeholder="请选择套餐模式" clearable>
                <el-option
                  v-for="(item, index) in startModelTypes"
                  :key="index"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="套餐名称" prop="serviceName">
              <el-input class="input" v-model.trim="item.serviceName" :maxLength="maxServiceName"
                        placeholder="请输入套餐名称">
              </el-input>
            </el-form-item>
            <el-form-item label="套餐描述" prop="remark">
              <el-input class="input" v-model.trim="item.remark" :maxLength="maxRemark"
                        placeholder="请输入套餐描述">
              </el-input>
            </el-form-item>
            <el-form-item label="时间" prop="serviceTime">
              <el-input class="input" v-model.number="item.serviceTime" placeholder="请输入套餐时间"
                        v-replace="serviceTimeReplace">
              </el-input>
            </el-form-item>
            <el-form-item label="价格" prop="price">
              <el-input class="input input-coins" v-model="item.price" placeholder="请输入套餐价格" v-replace="priceReplace">
              </el-input>
            </el-form-item>
            <el-form-item label="模拟投币数" prop="coins">
            <el-input class="input-coins" type="number" v-model.number="item.coins" placeholder="请输入模拟投币数" v-replace="coinsReplace">
              </el-input>
            </el-form-item>
            <el-form-item label="支持加液" prop="supportLiquid" v-if="isSupportLiquid">
              <el-checkbox v-model="item.supportLiquid" :true-label="false" :false-label="true"></el-checkbox>
            </el-form-item>
            <el-form-item label="默认开启" prop="active">
              <el-radio-group v-model="item.active">
                <el-radio v-for="(item, index) in activeArr" :key="index" :label="item.key">{{item.value}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>

          <!-- 烘干机 -->
          <template v-else>
            <el-form-item label="套餐模式" prop="startModelTypeId">
              <el-select v-model="item.startModelTypeId" :disabled="isEdit && !!item.id" filterable placeholder="请选择套餐模式" clearable>
                <el-option
                  v-for="(item, index) in startModelTypes"
                  :key="index"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="套餐名称" prop="serviceName">
              <el-input class="input" v-model.trim="item.serviceName" :maxLength="maxServiceName"
                        placeholder="请输入套餐名称">
              </el-input>
            </el-form-item>
            <el-form-item label="套餐描述" prop="remark">
              <el-input class="input" v-model.trim="item.remark" :maxLength="maxRemark"
                        placeholder="请输入套餐描述">
              </el-input>
            </el-form-item>
            <!-- 时间价格列表 -->
            <div class="sub-list">
              <div
                class="sub-list-item"
                v-for="(itemSub, indexSub) in item.subProtocolStartModelDTOList"
                :key="indexSub"
              >
                <el-form-item label="时间" :prop="`subProtocolStartModelDTOList[${indexSub}][serviceTime]`" :rules="rules.serviceTime">
                  <el-input class="input" v-model.number="itemSub.serviceTime" placeholder="请输入套餐时间"
                            v-replace="serviceTimeReplace">
                  </el-input>
                </el-form-item>
                <el-form-item label="价格" :prop="`subProtocolStartModelDTOList[${indexSub}][price]`" :rules="rules.price">
                  <el-input class="input input-coins" v-model="itemSub.price" placeholder="请输入套餐价格" v-replace="priceReplace">
                  </el-input>
                </el-form-item>
                <el-form-item label="模拟投币数" :prop="`subProtocolStartModelDTOList[${indexSub}][coins]`" :rules="rules.coins">
                  <el-input class="input input-coins" v-model.number="itemSub.coins" placeholder="请输入模拟投币数" v-replace="coinsReplace">
                  </el-input>
                </el-form-item>
                <el-form-item label="默认开启" prop="active">
                  <el-radio-group v-model="itemSub.active">
                    <el-radio v-for="(item, index) in activeArr" :key="index" :label="item.key">{{item.value}}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="deleteSubListItem(index, indexSub)"></el-button>
              </div>
              <div class="add-sub-list-item">
                <el-button type="success" @click="addSubListItem(index)">+ 添加规格</el-button>
              </div>
            </div>
            <template></template>
          </template>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "serviceConfigSubList",
    props: {
      arr: {
        type: Array,
        default: () => []
      },
      contractType: {
        type: Number,
        default: 1
      },
      // typeId: {
      //   type: Number
      // },
      isSupportLiquid: {
        type: Boolean,
        default: false
      },
      equipmentTypeValue: {
        type: String,
        default: ''
      },
      startModelTypes: {
        type: Array,
        default: () => []
      },
      isEdit: {
        type: Boolean,
        default: false
      },
    },
    components: {},
    data() {
      return {
        activeArr: [
          {key: true, value: '是'},
          {key: false, value: '否'},
        ],
        maxServiceName: 10,
        maxRemark: 10,
        maxservicePackageConfig: 30,
        serviceTimeSection: {
          min: 1,
          max: 1440,
        },
        priceSection: {
          min: 0.01,
          max: 10000,
        },
        coinsSection: {
          min: 1,
        },
        intReg: (/\D/g, ''),
        servicePackageConfig: [],
        rules: {},
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.init();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      init() {
        this.servicePackageConfig = this.servicePackageConfig.concat(this.arr);
        console.log('this.servicePackageConfig', this.servicePackageConfig)
        this.initRuleServicePackage();
      },
      priceValidator(rule, value, callback) {
        const val = value;
        if (val && parseFloat(val) < this.priceSection.min) {
          callback(new Error(`套餐价格不能小于${this.priceSection.min}`));
        } else if (val && parseFloat(val) > this.priceSection.max) {
          callback(new Error(`套餐价格不能大于${this.priceSection.max}`));
        } else{
          callback();
        }
      },
      initRuleServicePackage() {
        this.rules = {
          startModelTypeId: [
            {required: true, message: '请选择套餐模式', trigger: 'change'},
          ],
          serviceName: [
            {required: true, message: '请输入套餐名称', trigger: 'change'},
            {max: this.maxServiceName, message: `套餐名称不能超过${this.maxServiceName}字`, trigger: 'change'}
          ],
          remark: [
            {required: true, message: '请输入套餐描述', trigger: 'change'},
            {max: this.maxRemark, message: `套餐描述不能超过${this.maxRemark}字`, trigger: 'change'}
          ],
          serviceTime: [
            {type: 'number', required: true, message: '请输入套餐时间', trigger: 'change'},
            {type: 'number', message: '套餐时间只能输入数字', trigger: 'change'},
            {
              min: this.serviceTimeSection.min,
              type: 'number',
              message: `套餐时间不能小于${this.serviceTimeSection.min}`,
              trigger: 'change'
            },
            {
              max: this.serviceTimeSection.max,
              type: 'number',
              message: `套餐时间不能大于${this.serviceTimeSection.max}`,
              trigger: 'change'
            },
          ],
          price: [
            {required: true, message: '请输入套餐价格', trigger: 'change'},
            {validator: this.priceValidator, trigger: 'change'},
          ],
          coins: [
            {type: 'number', required: true, message: '请输入模拟投币数', trigger: 'change'},
            {type: 'number', message: '模拟投币数只能输入数字', trigger: 'change'},
            {
              min: this.coinsSection.min,
              type: 'number',
              message: `模拟投币数不能小于${this.coinsSection.min}`,
              trigger: 'change'
            },
          ],
        };
      },
      addBtn() {
        const item = {
          startModelTypeId: null,
          serviceName: '',
          remark: '',
          serviceTime: null,
          price: null,
          supportLiquid: false,
          active: true,
          serviceType: "service",
          coins: null,
          id: null,
          pattern: null,
          protocolConfigInfoId: null,
        }
        if (this.isHGJ) {
          item.serviceType = 'dryer'
          const subItem = {
            // ...item,
            serviceTime: null,
            price: null,
            active: true,
            serviceType: 'dryer',
            id: null,
            coins: null,
          }
          item.subProtocolStartModelDTOList = [subItem]
        }
        this.servicePackageConfig.push(item);
      },
      deleteBtn(index) {
        this.servicePackageConfig.splice(index, 1);
      },
      upBtn(index) {
        this.changeIndex(index, index - 1);
      },
      downBtn(index) {
        this.changeIndex(index, index + 1);
      },
      changeIndex(index, newIndex) {
        this.servicePackageConfig[index] = this.servicePackageConfig.splice(newIndex, 1, this.servicePackageConfig[index])[0];
      },
      serviceTimeReplace(val) {
        val = String(val).replace(/[^\d]/g, '');
        if (val && parseInt(val) < this.serviceTimeSection.min) {
          val = this.serviceTimeSection.min;
        } else if (val && parseInt(val) > this.serviceTimeSection.max) {
          val = this.serviceTimeSection.max;
        }
        return val;
      },
      priceReplace(val) {
        val = String(val).replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/, '$1');
        return val;
      },
      coinsReplace(val) {
        val = String(val).replace(/[^\d]/g, '');
        if (val && parseInt(val) < this.coinsSection.min) {
          val = this.coinsSection.min;
        }
        return val;
      },
      handleSave() {
        if (!this.servicePackageConfig || (Array.isArray(this.servicePackageConfig) && this.servicePackageConfig.length === 0)) {
          this.$message.error('套餐服务至少一个');
        } else {
          this.isParamsComplate(() => {
            let servicePackageConfig = [];
            servicePackageConfig = servicePackageConfig.concat([], this.servicePackageConfig);
            servicePackageConfig.map((item, index)=>{
              if (item.price) {
                item.price = parseFloat(item.price);
              }
              item.serviceType = 'service';
              item.pattern = this.startModelTypes?.find?.(it => it.id === item.startModelTypeId)?.name ?? ''
              if (this.isHGJ) {
                // 在这里再处理一遍，是为了避免添加规格后又把套餐模式之类的改了导致数据不对
                item.serviceType = 'dryer'

                if (item.subProtocolStartModelDTOList?.length > 0) {
                  item.subProtocolStartModelDTOList = item.subProtocolStartModelDTOList.map(subItem => {
                    const copyItem = {
                      ...item,
                      serviceTime: subItem.serviceTime,
                      price: subItem.price,
                      active: subItem.active,
                      serviceType: 'dryer',
                      id: subItem.id,
                      coins: subItem.coins,
                    }
                    delete copyItem.subProtocolStartModelDTOList
                    return copyItem
                  })
                }
              }
            })
            this.$emit('listSave', servicePackageConfig, 'service');
          });
        }
      },
      // 参数是否完整
      async isParamsComplate(call) {
        let flag = true;
        await this.$refs['ruleForm'].forEach((item, index) => {
          item.validate((isSuccess, valid) => {
            if (isSuccess) {
              // call();
              // return true;
            } else if (flag) {
              flag = false;
              let sign = '';
              if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
                const key = Object.keys(valid);
                sign = valid[key[0]][0]['message'];
                this.$message.error(sign);
              }
              return false;
            }
          });
        })
        if (flag) {
          call();
        }
      },
      /** 烘干机添加时间价格列表 */
      addSubListItem(index) {
        if (this.servicePackageConfig?.[index]?.subProtocolStartModelDTOList?.length >= 10) {
          this.$message('最多10个规格');
          return
        }
        const subItem = {
          // ...this.servicePackageConfig[index],
          serviceTime: null,
          price: null,
          active: true,
          serviceType: 'dryer',
          id: null,
        }
        // delete subItem.subProtocolStartModelDTOList
        this.servicePackageConfig[index].subProtocolStartModelDTOList.push(subItem)
      },
      /** 烘干机删除时间价格列表 */
      deleteSubListItem(index, indexSub) {
        if (this.servicePackageConfig?.[index]?.subProtocolStartModelDTOList?.length === 1) {
          this.$message('至少要有一个规格');
          return
        }
        this.servicePackageConfig[index].subProtocolStartModelDTOList.splice(indexSub, 1)
      }
    },
    directives: {
      replace: {
        bind: (el, binding, vnode) => {
          const input = el.getElementsByTagName('input')[0];

          const trigger = (el, type) => {
            const e = document.createEvent('HTMLEvents')
            e.initEvent(type, true, true)
            el.dispatchEvent(e)
          }
          const setVal = val => {
            if (vNode.componentInstance) {
              // 如果是自定义组件就触发自定义组件的input事件
              vNode.componentInstance.$emit('input', val)
            } else {
              // 如果是原生组件就触发原生组件的input事件
              el.value = val
              el.dispatchEvent(new Event('input'))
            }
          }
          input.onkeyup = (e) => {
            // input.value = input.value.replace(/[^\d]/g, '');
            input.value = binding.value(input.value)
            trigger(input, 'input')
          }
        }
      }
    },
    computed: {
      isHGJ() {
        return this.equipmentTypeValue === 'HGJ'
      }
    },

    watch: {}

  }

</script>
<style lang="less" scoped>
  .service-package-box {
    overflow: auto;

    .service-package-form-box {
      max-width: 1410px;

      .input {
        width: 130px;
      }

      .input-coins {
        width: 140px;
      }
    }
  }

  .config-title {
    .text {
      padding: 10px;
    }

    .btn {
      margin: 10px;
    }
  }
  .sub-list {
    margin-left: 136px;
  }
</style>
