<!--
日期：2023/10/24
功能：服务套餐配置列表
作者：庄炯权
-->
<template>
  <div>
    <div>
      <!--列表 start-->
      <el-table :data="pageInfo.list" border highlight-current-row
                style="width: 100%;margin-bottom: 20px;">
        <template v-for="(item, index) in colums">
          <el-table-column v-if="item.key === 'operation'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
                <el-button type="text"
                           class="info-btn"
                           @click="editBtn(scope.row)">操作
                </el-button>
                <el-button type="text"
                           class="info-btn"
                           @click="deleteBtn(scope.row)">
                  <span class="danger">删除</span>
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'contractType'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
                {{scope.row.contractType | arrGetValue(contractTypeArr, 'key', 'value')}}
              </div>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'protocolStartModelDTOList'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable">
            <template slot-scope="scope">
              <div v-if="scope.row.protocolStartModelDTOList && scope.row.protocolStartModelDTOList.length > 0">
                <!-- 烘干机 -->
                <template v-if="scope.row.equipmentTypeValue === 'HGJ'">
                  <div v-for="(itemWash, indexWash) in scope.row.protocolStartModelDTOList" :key="indexWash">
                    <p>{{ padNum(indexWash + 1) }} {{ itemWash.pattern }}-{{ itemWash.serviceName }}</p>
                    <p class="sub" style="margin-left: 20px;">
                      <span v-for="(subItem, subIndex) in itemWash.subProtocolStartModelDTOList" :key="subIndex">
                        <span v-if="subIndex !== 0">；</span>
                        {{ subItem.serviceTime }}分钟，{{ subItem.price }}元{{ itemWash.coins? `，${itemWash.coins}模拟投币数` : '' }}
                      </span>
                    </p>
                  </div>
                </template>
                <!-- 其它 -->
                <template v-else>
                  <div v-for="(itemWash, indexWash) in scope.row.protocolStartModelDTOList" :key="indexWash">
                    <p>{{ padNum(indexWash + 1) }} {{ itemWash.pattern }}-{{ itemWash.serviceName }}；{{ itemWash.serviceTime }}分钟；{{ itemWash.price }}元{{ itemWash.coins? `；${itemWash.coins}模拟投币数` : '' }}</p>
                  </div>
                  <template v-if="scope.row.liquidStartModelDTOList && scope.row.liquidStartModelDTOList.length > 0">
                    <div v-for="(itemLiquid, indexLiquid) in scope.row.liquidStartModelDTOList" :key="indexLiquid">
                      <p>{{ padNum(scope.row.protocolStartModelDTOList.length + indexLiquid + 1) }} {{ itemLiquid.pattern }}-{{ itemLiquid.serviceName }}；{{ itemLiquid.serviceTime }}分钟；{{ itemLiquid.price }}元{{ itemLiquid.coins? `；${itemLiquid.coins}模拟投币数` : '' }}</p>
                    </div>
                  </template>
                </template>
              </div>
              <div v-else>--</div>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :key="index"
            :prop="item.key"
            :label="item.label"
            :width="item.width"
            :sortable="item.sortable"
            align="center"
          />
        </template>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 50]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <!--列表 end-->
      <service-config-wash-dialog-delete ref="deleteRef" @confirm="getList()"/>
    </div>
  </div>
</template>

<script>
  import { serviceConfigWashQuery } from "@api/serviceConfigWash";
  import ServiceConfigWashDialogDelete
    from "@views/professionalWork/serviceConfigWash/children/serviceConfigWashDialogDelete";

  export default {
    name: "serviceConfigList",
    props: {
      setLoading: {
        type: Function,
        default: () => {
        }
      }
    },
    components: { ServiceConfigWashDialogDelete },
    data() {
      return {
        formData: {},
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          list: [],
        },
        // 列表每一列参数
        colums: [
          { key: 'operation', label: '操作' },
          { key: 'equipmentTypeName', label: '设备类型' },
          { key: 'startModelName', label: '设备型号' },
          { key: 'contractType', label: '对接方式' },
          { key: 'protocolIdOrLoginFlag', label: '协议ID/登录标识' },
          { key: 'protocolStartModelDTOList', label: '启动套餐' },
        ],
        contractTypeArr: [
          { key: 1, value: '正向对接' },
          { key: 2, value: '反向对接' },
        ],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
      this.getList();
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      query(formData) {
        this.pageInfo.pageIndex = 1;
        this.formData = Object.assign({}, formData);
        this.getList();
      },
      getList() {
        this.setLoading(true);
        serviceConfigWashQuery(this.getParams()).then(res => {
          this.setLoading(false);
          if (res.code === '0000000' && res.body) {
            this.pageInfo.list = res.body.records;
            this.pageInfo.total = res.body.total;
          }
        })
      },
      getParams() {
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        if (this.formData.startModelName) {
          params['startModelName'] = this.formData.startModelName;
        }
        if (this.formData.protocolIdOrLoginFlag) {
          params['protocolIdOrLoginFlag'] = this.formData.protocolIdOrLoginFlag;
        }
        if (this.formData.equipmentTypeValueList) {
          params['equipmentTypeValueList'] = this.formData.equipmentTypeValueList;
        }
        return params;
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.getList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.getList();
      },
      padNum(num) {
        if (num < 10) {
          return `0${num}`
        }
        return num
      },
      editBtn(item) {
        this.$router.push({
          path: '/serviceConfigWash/edit',
          query: {
            itemData: JSON.stringify(item)
          }
        })
      },
      deleteBtn(item) {
        this.$refs.deleteRef.open(item);
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="css" scoped>
</style>
