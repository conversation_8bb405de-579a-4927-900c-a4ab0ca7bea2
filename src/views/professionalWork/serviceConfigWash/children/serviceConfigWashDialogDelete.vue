<!--
日期：2023/10/24
功能：删除配置弹窗
作者：庄炯权
-->
<template>  <div>
  <el-dialog
    title="温馨提示"
    :visible.sync="visible"
    v-if="visible"
    width="30%"
    :before-close="handleClose">
    <el-form :inline="false"
             label-position="right"
             :model="formData"
             ref="ruleForm"
             v-loading="visibleLoading"
             label-width="110px">
      <p>确认要删除该主板服务套餐?</p>
    </el-form>
    <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="danger" @click="handleSave">删 除</el-button>
      </span>
  </el-dialog>
</div>
</template>

<script>
    import {serviceConfigWashDelete} from "@api/serviceConfigWash";

    export default {
        name: "serviceConfigDialogDelete",
      props: {},
      components: {},
      data() {
        return {
          visible: false,
          visibleLoading: false,
          formData: {}
        };
      },

      beforeCreate() {
      },

      created() {
      },

      beforeMount() {
      },

      mounted() {
      },

      beforeUpdate() {
      },

      updated() {
      },

      activated() {
      },

      deactivated() {
      },

      beforeDestroy() {
      },

      destroyed() {
      },

      errorCaptured() {
      },

      methods: {
        initFormData() {
          this.formData = {};
        },
        open(item) {
          this.initFormData();
          this.formData = item;
          this.visible = true;
        },
        handleClose() {
          this.visible = false;
        },
        handleSave() {
          this.visibleLoading = true;
          serviceConfigWashDelete(this.getParams()).then(res => {
              this.visibleLoading = false;
              if (res.code === '0000000') {
                this.$message.success('删除成功');
                this.$emit('confirm');
                this.handleClose();
              }
            }
          );
        },
        getParams() {
          let params = {
            contractType: this.formData.contractType,
            protocolIdOrLoginFlag: this.formData.protocolIdOrLoginFlag,
          };
          return params;
        }
      },

      computed: {},

      watch: {}

    };

</script>
<style lang="css" scoped>
</style>
