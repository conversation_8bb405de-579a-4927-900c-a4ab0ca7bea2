<!--
日期：2019/11/11
功能：模板组件
作者：杨楠锋
-->
<template>
  <div>
    <div class="config-title">
      <el-form :inline="true"
               label-position="left"
               :model="formData"
               ref="ruleForm"
               :rules="rules"
               class="">
        <el-form-item label="模式切换" prop="flag" label-width="90px">
          <el-radio-group v-model="formData.flag" @change="flagChange()">
            <el-radio v-for="(item, index) in flagArr" :key="index" :label="item.key">{{item.value}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="isShow">
          <el-button type="success" icon="el-icon-circle-plus-outline" @click="addBtn">
            添加({{list.length}}条)
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="service-package-box" v-if="isShow">
      <div v-for="(item, index) in list">
        <el-form :inline="true"
                 :model="item"
                 ref="ruleForm"
                 :rules="rules"
                 :key="index"
                 class="service-package-form-box">
          <el-form-item>
            <el-button type="primary" icon="el-icon-caret-top" circle plain :disabled="index === 0"
                       @click="upBtn(index)"></el-button>
            <el-button type="primary" icon="el-icon-caret-bottom" circle plain
                       :disabled="index === list.length -1"
                       @click="downBtn(index)"></el-button>
            <el-button type="danger" icon="el-icon-delete" circle plain @click="deleteBtn(index)"
                       :disabled="list.length <= 2"></el-button>
          </el-form-item>
          <el-form-item label="模式名称" prop="skillName">
            <el-input class="input" v-model.trim="item.skillName" :maxLength="maxSkillName" placeholder="请输入模式名称">
            </el-input>
          </el-form-item>
          <el-form-item label="协议指令" prop="protocolCmd">
            <el-input class="input" v-model.trim="item.protocolCmd"
                      placeholder="请输入协议指令">
            </el-input>
          </el-form-item>
          <el-form-item prop="isDefault">
            <el-checkbox v-model="item.isDefault === 1" @change="defaultChange($event, index)">
              设为默认启动项
            </el-checkbox>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "armchairsControlSubSkillConfigList",
    props: {
      arr: {
        type: Array,
        default: [],
      },
      flag: {
        type: Number,
        default: 1,
      }
    },
    components: {},
    data() {
      return {
        formData: {
          flag: 1,
        },
        maxSkillName: 10,
        list: [],
        rules: {},
        flagArr: [
          {key: 1, value: '有'},
          {key: 0, value: '无'}
        ],
        isShow: false,
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.init();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      init() {
        this.list = this.list.concat(this.arr);
        this.formData.flag = this.flag;
        if (this.list.length === 0) {
          this.list.push(Object.assign(this.getParams(), {isDefault: 1}));
          this.list.push(this.getParams());
        }
        this.initRuleServicePackage();
        this.flagChange();
      },
      initRuleServicePackage() {
        const flag = this.formData.flag === 1;
        this.rules = {
          skillName: [
            {required: flag, message: '请输入模式名称', trigger: 'change'},
            {max: this.maxSkillName, message: `模式名称不能超过${this.maxSkillName}字`, trigger: 'change'}
          ],
          protocolCmd: [
            {required: flag, message: '请输入协议指令', trigger: 'change'},
          ],
        };
      },
      addBtn() {
        this.list.push(this.getParams());
      },
      deleteBtn(index) {
        this.list.splice(index, 1);
      },
      upBtn(index) {
        this.changeIndex(index, index - 1);
      },
      downBtn(index) {
        this.changeIndex(index, index + 1);
      },
      changeIndex(index, newIndex) {
        this.list[index] = this.list.splice(newIndex, 1, this.list[index])[0];
      },
      handleSave() {
        this.isParamsComplate(() => {
          this.$emit('listSave', {
            flag: this.formData.flag,
            list: this.list,
          });
        });
      },
      // 参数是否完整
      async isParamsComplate(call) {
        let flag = true;
        let arr = this.$refs['ruleForm'] instanceof Array ? this.$refs['ruleForm'] : [this.$refs['ruleForm']];
        await arr.forEach((item, index) => {
          item.validate((isSuccess, valid) => {
            if (isSuccess) {
              // call();
              // return true;
            } else if (flag) {
              flag = false;
              let sign = '';
              if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
                const key = Object.keys(valid);
                sign = valid[key[0]][0]['message'];
                this.$message.error(sign);
              }
              return false;
            }
          });
        })
        if (flag) {
          call();
        }
      },
      defaultChange($event, index) {
        this.list.forEach((item, listIndex) => {
          if (index === listIndex && $event) {
            item.isDefault = 1;
          } else {
            item.isDefault = 0;
          }
        });
      },
      getParams() {
        return {
          skillName: '',
          protocolCmd: '',
          isDefault: 0
        };
      },
      flagChange() {
        this.isShow = this.formData.flag === 1;
      },
    },
    computed: {},

    watch: {}

  }

</script>
<style lang="less" scoped>
  .service-package-box {
    overflow: auto;

    .service-package-form-box {
      max-width: 1410px;

      .input {
        width: 130px;
      }

      .input-coins {
        width: 140px;
      }
    }
  }

  .config-title {
    .text {
      padding: 10px;
    }

    .btn {
      margin: 10px;
    }
  }
</style>
