<!--
日期：2022/06/14
功能：健康监测配置组件
作者：蓝素萍
-->
<template>
  <div>
    <el-card v-loading="visibleLoading" v-if="visible">
      <div slot="header" class="clearfix mul-top">
        {{formData.massageHealthCheckConfigId ? ' 编辑':' 新建'}}健康监测配置
      </div>
      <el-form :inline="true"
               v-if="visible"
               label-position="left"
               :model="formData"
               ref="ruleForm"
               :rules="rules"
               v-loading="visibleLoading"
               label-width="90px"
               class="">
        <div class="divider">
          <el-form-item label="主板厂家" prop="mainboardFactory">
            <el-input v-model.trim="formData.mainboardFactory" :maxLength="maxMainboardFactory"
                      placeholder="请输入主板厂家">
              <template slot="append">
              <span
                v-if="formData.mainboardFactory && typeof formData.mainboardFactory === 'string' && formData.mainboardFactory.length > 0">{{formData.mainboardFactory.length}}</span>
                <span v-else>0</span>
                <span>/{{maxMainboardFactory}}</span>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="设备类型" prop="lyyEquipmentTypeIds">
            <el-checkbox-group v-model="formData.lyyEquipmentTypeIds">
              <el-checkbox v-for="item in lyyEquipmentTypeIdArr" :key="item.lyyEquipmentTypeId"
                           :label="item.lyyEquipmentTypeId">{{item.typeName}}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="登录标识" prop="loginFlag">
            <el-input v-model.trim="formData.loginFlag" :maxLength="maxLoginFlag" placeholder="请输入登录标识"
                      v-replace="intReplace">
              <template slot="append">
                <span
                  v-if="formData.loginFlag && typeof formData.loginFlag === 'string' && formData.loginFlag.length > 0">{{formData.loginFlag.length}}</span>
                <span v-else>0</span>
                <span>/{{maxLoginFlag}}</span>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item  v-if="isShowProductNum" label="主板编号" prop="productNumber" >
            <el-input v-model.trim="formData.productNumber" placeholder="请输入主板编号"
                      v-replace="intReplace">
            </el-input>
          </el-form-item>
          <el-form-item label="检测模块类型" prop="collectType" label-width="120px">
            <el-select v-model="formData.collectType" placeholder="请选择">
              <el-option
                v-for="(item, index) in collectTypeList"
                :key="index"
                :label="item.description"
                :value="item.collectType">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="divider">
          <el-form-item label="下发方式" prop="pushType">
            <el-radio-group v-model="formData.pushType">
              <el-radio v-for="(item, index) in issuuedWayArr" :key="index" :label="item.key">{{item.value}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="item-flex" >
            <el-form-item label="等待时长" prop="healthCheckWaitTime" >
            <el-input v-model="formData.healthCheckWaitTime" placeholder="" type="text" maxlength="3" style="width:150px;"></el-input>
            </el-form-item>
            <div class="item-text">秒后启动健康监测组件</div>
          </div>
          <div class="item-flex">
            <el-form-item label="检测时长" prop="healthCheckTime">
              <el-input  v-model="formData.healthCheckTime" placeholder="" type="text" maxlength="3" style="width:150px;"></el-input>
            </el-form-item>
            <div class="item-text">秒后停止健康监测组件</div>
           </div>
        </div>
      </el-form>
        <el-form>
          <el-form-item>
            <el-button size="medium" @click="handleClose">关 闭</el-button>
            <el-button size="medium"  type="primary" @click="handleSave">保 存</el-button>
          </el-form-item>
        </el-form>

    </el-card>
  </div>
</template>

<script>
  import {massageHealthCheckDetail, armchairsControlTypeArr, massageHealthCheckSave, getCollectTypeList} from '@api/armchairsControl';
  export default {
    name: 'healthMonitor',
    props: {},
    components: {},
    data() {
      return {
        formData: {
          massageHealthCheckConfigId:"",
          mainboardFactory: '',
          lyyEquipmentTypeIds: [],
          loginFlag: '',//登录标识
          pushType: 1,//下发方式
          healthCheckWaitTime:'',//等待时长
          healthCheckTime:'',// 健康检测时长
          collectType: '', // 检测模块类型
        },
        rules: {},
        visibleLoading: false,
        visible: false,
        maxMainboardFactory: 15,
        maxLoginFlag: 10,
        lyyEquipmentTypeIdArr: [],
        issuuedWayArr: [
          {key: 1, value: '自动下发'},
          {key: 2, value: '套餐下发'}
        ],
        collectTypeList: [], // 检测模块类型列表
      };
    },
    mounted() {
      this.init();
    },

    methods: {
      init() {
        this.getLyyEquipmentTypeIdArr();
        this.initFormData();
        if (this.$route.query && Object.keys(this.$route.query) && Object.keys(this.$route.query).length > 0) {
          this.getDetail(this.$route.query.massageHealthCheckConfigId);
        } else {
          this.initRule();
          this.visible = true;
        }
        this.getCollectTypeList()
      },
      getDetail(id) {
        massageHealthCheckDetail({massageHealthCheckConfigId: id}).then(res => {
          if (res.result === 0) {
            this.formData = res.data;
            this.formData.lyyEquipmentTypeIds.forEach((item, index) => {
              this.formData.lyyEquipmentTypeIds[index] = Number(this.formData.lyyEquipmentTypeIds[index])
            });
            this.formData.loginFlag = String(this.formData.loginFlag);
            this.initRule();
            this.visible = true;
          }
        });
      },
      initRule() {
        this.rules = {
          mainboardFactory: [
            {required: true, message: '请输入主板厂家', trigger: 'change'},
            {max: this.maxMainboardFactory, message: `主板厂家不能超过${this.maxMainboardFactory}字`, trigger: 'change'}
          ],
          lyyEquipmentTypeIds: [
            {type: 'array', required: true, message: '请至少选择一个设备类型', trigger: 'change'}
          ],
          loginFlag: [
            {required: true, message: '请输入登录标识', trigger: 'change'},
            {max: this.maxLoginFlag, message: `登录标识不能超过${this.maxLoginFlag}字`, trigger: 'change'}
          ],
          productNumber: [
            {required: true, message: '配置反向对接时主板编号为必填', trigger: 'change'},
          ],
          pushType: [
            {required: true, message: '请选择下发方式', trigger: 'change'},
          ],
          healthCheckTime: [
            {required: true, message: '请输入检测时长', trigger: 'change'},
          ],
          healthCheckWaitTime: [
            {required: true, message: '请输入等待时长', trigger: 'change'},
          ],
          collectType: [
            {required: true, message: '请选择检测模块类型', trigger: 'change'},
          ]
        };
      },
      initFormData() {
        this.formData = {
          mainboardFactory: '',
          lyyEquipmentTypeIds: [],
          loginFlag: '',
          suspendFlag: 1,
        };
      },
      getLyyEquipmentTypeIdArr() {
        armchairsControlTypeArr().then(res => {
          if (res.result === 0) {
            res.data.forEach(item => {
              // if (item.typeName == '按摩椅' || item.typeName == '按摩垫' || item.typeName == '足疗机') {
              //   this.lyyEquipmentTypeIdArr.push(item);
              // }
                if (item.typeName == '按摩椅') {
                this.lyyEquipmentTypeIdArr.push(item);
              }
            });
          }
        });
      },
      intReplace(val) {
        val = String(val);
        val = val.replace(/[^\d]/g, '');
        return val;
      },
      handleClose() {
        this.$router.push({
          path: '/armchairsControl/list'
        });
      },
      async handleSave() {
        let isValidate = await this.isParamsComplate()
        if(!isValidate) return;
        massageHealthCheckSave(this.formData).then(res => {
          if(res.result === 0) {
            this.$message.success("保存健康监测配置成功！")
            this.$router.push({
              path: '/armchairsControl/list'
            });
          }
        })
      },
      // 参数是否完整
      async isParamsComplate() {
        let flag = true;
        await this.$refs['ruleForm'].validate((isSuccess, valid) => {
          if (isSuccess && flag) {
            flag = true;
          } else {
            let sign = '';
            if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]['message'];
              this.$message.error(sign);
            }
            flag = false;
          }
        });
        if(this.formData.healthCheckWaitTime <= 0 || this.formData.healthCheckTime <= 0) {
          this.$message.error("等待时长及检测时长必须大于0")
          flag = false;
        }
        return flag;
      },
      // 获取检测模块类型列表
      getCollectTypeList() {
        getCollectTypeList().then(res => {
          if (res.result === 0) {
            this.collectTypeList = res.data || []
          }
        })
      }
    },
    directives: {
      replace: {
        bind: (el, binding, vnode) => {
          const input = el.getElementsByTagName('input')[0];
          const trigger = (el, type) => {
            const e = document.createEvent('HTMLEvents');
            e.initEvent(type, true, true);
            el.dispatchEvent(e);
          };
          const setVal = val => {
            if (vNode.componentInstance) {
              // 如果是自定义组件就触发自定义组件的input事件
              vNode.componentInstance.$emit('input', val);
            } else {
              // 如果是原生组件就触发原生组件的input事件
              el.value = val;
              el.dispatchEvent(new Event('input'));
            }
          };
          input.onkeyup = (e) => {
            // input.value = input.value.replace(/[^\d]/g, '');
            input.value = binding.value(input.value);
            trigger(input, 'input');
          };
        }
      }
    },
    computed: {
      isShowProductNum() {
        if(this.formData.loginFlag == 10000) {
          return true;
        } else {
          return false;
        }
      }
    },

    watch: {}

  };

</script>
<style lang="less" scoped>
  .mul-top{
    button{
      float: right;
    }
  }
  .block-box {
    border: 1px solid #e5e5e5;
    margin: 10px;
    padding: 10px;
  }
  .item-flex{
    display: flex;
    align-items: center;
  }
  .item-text{
    transform: translateY(-10px);
  }
  .divider {
    border-bottom: 1px solid #ebebeb;
    margin-bottom: 10px;
  }

  .volume-section {
    margin-left: -10px;
  }
</style>
