<template>
  <div>
    <el-form :inline="true" :model="searchForm" class="form">
      <el-form-item label="产品名称">
        <el-input
          v-model.trim="searchForm.name"
          placeholder="请输入产品名称"
          @keyup.enter.native="postIncreaseIncomeList(true)"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="产品类型">
        <el-select v-model="searchForm.increaseIncomeProductTypeId" placeholder="请选择产品类型">
          <el-option label="全部" value=""></el-option>
          <el-option
            :label="item.name"
            :value="item.increaseIncomeProductTypeId"
            v-for="(item, index) in increaseIncomeTypeList"
            :key="index"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="产品状态">
        <el-select v-model="searchForm.status" placeholder="请选择产品状态">
          <el-option v-bind="item" v-for="(item, index) in statusMap" :key="index"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="postIncreaseIncomeList(true)">查询</el-button>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="addOrEdit(1)" style="margin-bottom: 20px">新建产品</el-button>
    <Table
      align="center"
      :data="increaseIncomeList"
      :column="column"
      :pagination="pagination"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
      v-loading="listLoading"
    >
      <template #operate="{ row }">
        <div class="btn-wraper">
          <el-button type="primary" @click="addOrEdit(2, row)">编辑</el-button>
          <el-button
            type="warning"
            v-if="row.status === 1"
            @click="updateStatus(row.increaseIncomeProductId, 2)"
            >下架</el-button
          >
          <el-button
            type="success"
            v-if="row.status === 2"
            @click="updateStatus(row.increaseIncomeProductId, 1)"
            >上架</el-button
          >
          <el-button type="danger" @click="increaseIncomeDelete(row)">删除</el-button>
        </div>
      </template>
      <template #photo="{ row }">
        <div class="btn-wraper">
          <el-image
            style="width: 100px; height: 100px"
            :src="row.photo"
            :preview-src-list="[row.photo]"
            fit="scale-down"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </div>
      </template>
    </Table>
    <el-dialog
      :title="handleType === 1 ? '新建产品' : '编辑产品'"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-form ref="form" :model="form" label-width="80px" class="dialog_form" :rules="rules">
        <el-form-item label="产品名称" prop="name">
          <div class="el-input-wraper">
            <el-input v-model.trim="form.name" maxlength="8">
              <template slot="append">{{ form.name.length }}/8</template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="产品类型" prop="increaseIncomeProductTypeId">
          <el-select
            v-model="form.increaseIncomeProductTypeId"
            filterable
            placeholder="请选择产品类型"
          >
            <el-option
              :label="item.name"
              :value="item.increaseIncomeProductTypeId"
              v-for="(item, index) in increaseIncomeTypeList"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="开放品类" prop="category">
          <el-select v-model="form.category" placeholder="请选择开放品类">
            <el-option label="区域一" value="shanghai"></el-option>
            <el-option label="区域二" value="beijing"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="活动封面" prop="photo">
          <el-upload
            ref="upload"
            action="/gw/admin/saas/file/upload"
            :headers="headerRamToken"
            list-type="picture-card"
            :file-list="fileList"
            :on-preview="handlePictureCardPreview"
            :on-remove="
              () => {
                fileList = [];
                form.photo = '';
              }
            "
            :limit="1"
            accept=".jpg,.png"
            :on-change="handleLimit"
            :on-success="handleSuccess"
            :before-upload="beforeUpload"
            :class="{ disabled: uploadDisabled }"
          >
            <i class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">
              建议尺寸：750*260，{{ maxSize }}M，支持JPG，PNG
            </div>
          </el-upload>
          <el-dialog :visible.sync="dialogVisibleUpload" append-to-body width="25%">
            <img width="100%" :src="dialogImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
        <el-form-item label="链接" prop="link">
          <div class="el-input-wraper">
            <el-input v-model.trim="form.link"></el-input>
          </div>
        </el-form-item>
        <el-form-item label="上架" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <div class="el-input-wraper">
            <el-input v-model.trim="form.sort" @input="form.sort = getNatural(form.sort)" maxlength="9"></el-input>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading" :disable="loading"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Table from "@/components/Table/index.vue";
import {getNatural} from '@js/index.js'
import {
  postIncreaseIncomeList,
  getIncreaseIncomeTypeList,
  updateStatus,
  increaseIncomeAdd,
  increaseIncomeEdit,
  increaseIncomeDelete,
} from "@/api/increaseIncome.js";
import { headerRamToken } from '@/utils/menu'

export default {
  name: "productionList",
  components: {
    Table,
  },
  data() {
    return {
      headerRamToken,
      getNatural:getNatural,
      statusMap: [
        {
          label: "全部",
          value: "",
        },
        {
          label: "已上架",
          value: "1",
        },
        {
          label: "已下架",
          value: "2",
        },
      ],
      searchForm: {
        name: "", //类型：String  可有字段  备注：商品名称
        increaseIncomeProductTypeId: "", //类型：Number  可有字段  备注：商品类型
        status: "", //类型：Number  可有字段  备注：产品状态 1：上架 2：下架
      },
      column: [
        {
          label: "序号",
          type: "index",
          fixed: true,
        },
        {
          label: "操作",
          prop: "operate",
          type: "custom",
          width: 250,
          fixed: true,
        },
        {
          label: "产品名称",
          prop: "name",
        },
        {
          label: "产品图",
          prop: "photo",
          type: "custom",
        },
        // {
        //   label: "产品类型",
        //   prop: "name",
        //   formatter: (row) => {
        //     return row.name ?? "--";
        //   },
        // },
        {
          label: "排序",
          prop: "sort",
          formatter: (row) => {
            return row.sort ?? "--";
          },
        },
        {
          label: "状态",
          prop: "status",
          formatter: (row) => {
            return this.statusMap?.[row.status]?.label ?? "--";
          },
        },
        {
          label: "创建时间",
          width: 200,
          prop: "created",
          type: "date",
        },
        {
          label: "创建人",
          prop: "createdbyName",
          formatter: (row) => {
            return row.createdbyName ?? "--";
          },
        },
      ],
      pagination: {
        pageSize: 10,
        currentPage: 1,
        total: 10,
      },
      increaseIncomeList: [],
      increaseIncomeTypeList: [], //产品类型列表
      dialogVisible: false,
      form: {
        name: "", //类型：String  必有字段  备注：产品标题
        increaseIncomeProductTypeId: "", //类型：Number  必有字段  备注：产品类型
        link: "", //类型：String  必有字段  备注：链接
        photo: "", //类型：String  必有字段  备注：活动封面
        status: "1", //类型：Number  必有字段  备注：1：上架 2：下架
        sort: "", //类型：Number  必有字段  备注：排序
      },
      rules: {
        name: [{ required: true, message: "请输入标题", trigger: "blur" }],
        increaseIncomeProductTypeId: [
          { required: true, message: "请选择产品类型", trigger: "change" },
        ],
        photo: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.form.photo === "") {
                callback(new Error("请上传活动封面"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        // link: [{ required: true, message: "请输入链接", trigger: "blur" }],
        status: [{ required: true, message: "请选择上架状态", trigger: "blur" }],
        sort: [{ required: true, message: "请输入排序值", trigger: "blur" }],
      },
      dialogImageUrl: "",
      dialogVisibleUpload: false,
      // uploadDisabled: false,
      maxSize: 15,
      handleType: 1, // 1：新增 2：编辑
      loading: false,
      deleteLoading: false,
      listLoading: false,
      fileList: [],
      updateStatusLoading:false,
    };
  },
  watch: {
    form: {
      handler(val) {
        console.log("form----", val);
      },
      deep: true,
    },
  },
  computed: {
    uploadDisabled() {
      console.log(this.fileList.length >= 1 ? true : false);
      return this.fileList.length >= 1 ? true : false;
    },
  },
  created() {
    this.postIncreaseIncomeList();
  },
  activated() {
    this.getIncreaseIncomeTypeList();
  },
  methods: {
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pagination.pageSize = val;
      this.postIncreaseIncomeList();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.pagination.currentPage = val;
      this.postIncreaseIncomeList();
    },
    // 新建产品
    addOrEdit(type, row) {
      // type 1:新增 type：2 编辑
      console.log("row", row);
      this.handleType = type;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.resetForm();
        if (type === 2) {
          this.fileList = [{ name: "", url: row.photo }];
          const {
            name,
            increaseIncomeProductTypeId,
            increaseIncomeProductId,
            link,
            photo,
            status,
            sort,
          } = row;
          this.form = {
            name,
            increaseIncomeProductTypeId,
            increaseIncomeProductId,
            link,
            photo,
            sort,
            status: String(status),
          };
        }
      });
    },
    // 上传成功
    handleSuccess(res) {
      if (res?.result == 0 && res?.data) {
        this.form.photo = res.data?.pictureUrl;
      }
    },
    // 图片校验
    beforeUpload(file) {
      const isJpgOrPng =  /image\/(jpe?g|png)/.test(file.type)
      const isLt15M = file.size / 1024 / 1024 < this.maxSize;

      if (!isJpgOrPng) {
        this.$message.error("上传头像图片只能是 JPG/PNG 格式!");
      }
      if (!isLt15M) {
        this.$message.error(`上传头像图片大小不能超过 ${this.maxSize}MB!`);
      }
      return isJpgOrPng && isLt15M;
    },
    // 预览图片
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisibleUpload = true;
    },
    // 限制只能上传一张
    handleLimit(file, fileList) {
      this.fileList = fileList;
      // this.uploadDisabled = fileList.length >= 1 ? true : false;
      // console.log("uploadDisabled", this.uploadDisabled);
    },

    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.handleType === 1 ? this.increaseIncomeAdd() : this.increaseIncomeEdit();
        }
      });
    },
    resetForm() {
      this.$refs.form.resetFields();
      this.$refs.upload.clearFiles();
      // this.uploadDisabled = false;
      this.fileList = [];
    },

    // 增收产品列表
    async postIncreaseIncomeList(isSearchAll = false) {
      this.listLoading = true;
      isSearchAll && (this.pagination.currentPage = 1);
      const { currentPage: pageIndex, pageSize } = this.pagination;
      const data = {
        ...this.searchForm,
        pageIndex,
        pageSize,
      };
      const res = await postIncreaseIncomeList(data);
      if (res?.result === 0) {
        this.increaseIncomeList = res?.data?.items ?? [];
        this.pagination.total = res.data?.total ?? 0;
      }
      this.listLoading = false;
    },
    // 增加产品类型列表
    async getIncreaseIncomeTypeList() {
      const res = await getIncreaseIncomeTypeList();
      if (res?.result === 0) {
        this.increaseIncomeTypeList = res?.data?.items ?? [];
      }
    },
    // 增加产品类型列表
    async updateStatus(increaseIncomeProductId, status) {
      this.$confirm(`确定${status === 1?'上架':'下架'}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        beforeClose: async (action, instance, done) => {
          if (action === "confirm") {
            if (this.updateStatusLoading) return;
            this.updateStatusLoading = true;
            const res = await updateStatus({
              increaseIncomeProductId,
              status,
            });
            if (res?.result === 0) {
              this.successMessage();
              this.postIncreaseIncomeList();
            }
            this.updateStatusLoading = false;
            done();
          } else {
            done();
          }
        },
      });
    },
    // 新增产品
    async increaseIncomeAdd() {
      if (this.loading) return;
      this.loading = true;
      const res = await increaseIncomeAdd({
        ...this.form,
      });
      if (res?.result === 0) {
        this.successMessage();
        this.dialogVisible = false;
        this.postIncreaseIncomeList();
      }
      this.loading = false;
    },
    // 编辑产品
    async increaseIncomeEdit() {
      if (this.loading) return;
      this.loading = true;
      const res = await increaseIncomeEdit({
        ...this.form,
      });
      if (res?.result === 0) {
        this.successMessage();
        this.dialogVisible = false;
        this.postIncreaseIncomeList();
      }
      this.loading = false;
    },
    // 删除产品
    async increaseIncomeDelete(row) {
      this.$confirm("确定删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        beforeClose: async (action, instance, done) => {
          if (action === "confirm") {
            if (this.deleteLoading) return;
            this.deleteLoading = true;
            const { increaseIncomeProductId } = row;
            const res = await increaseIncomeDelete({
              increaseIncomeProductId,
              isactive: false,
            });
            if (res?.result === 0) {
              this.successMessage();
              this.postIncreaseIncomeList();
            }
            this.deleteLoading = false;
            done();
          } else {
            done();
          }
        },
      });
    },
    successMessage() {
      this.$message({
        message: "操作成功",
        type: "success",
      });
    },
  },
};
</script>

<style lang="less" scoped>
.el-input-wraper {
  width: 50%;
  // min-width: 220px;
}
/deep/ .el-upload-list__item.is-success .el-upload-list__item-status-label {
  display:none
}
/deep/ .dialog_form .el-select {
  width: 50%;
  // min-width: 220px;
}
/deep/ .disabled .el-upload.el-upload--picture-card {
  display: none !important;
}

/deep/ .disabled .el-button--success.is-plain {
  display: none !important;
}
/deep/ .el-upload,
/deep/ .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
  line-height: 100px;
}
/deep/ .el-upload-list--picture-card .el-progress {
  width: 100%;
}
/deep/ .el-progress-circle {
  width: 95px !important;
  height: 95px !important;
}
/deep/ .el-upload-list__item {
  transition: none;
}
.btn-wraper {
  display: flex;
  justify-content: center;
}
/deep/ .el-image__error,
.el-image__placeholder {
  background: #ccc;
}
/deep/ .image-slot {
  background: #dcdee3;
  font-size: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
</style>
