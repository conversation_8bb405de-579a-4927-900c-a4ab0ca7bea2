<!--
日期：2019/9/26
功能：优惠券活动统计列表
作者：杨楠锋
-->
<template>
  <div v-loading="pageInfo.loading">
    <!--搜索参数、查询、导出-->
    <el-form :inline="true" label-position="left" label-width="100px">
      <!--时间参数：开始时间-结束时间-->
      <el-form-item label="起止日期：">
        <el-date-picker
          v-model="date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="changeDate"
          :picker-options="pickerOptions"
          :editable="false"
        >
        </el-date-picker>
      </el-form-item>
      <!--按钮-->
      <el-form-item>
<!--        <el-button type="primary" icon="el-icon-search" @click="queryBtn">查询</el-button>-->
        <el-button type="success" icon="el-icon-download" @click="exportBtn">导出</el-button>
      </el-form-item>
    </el-form>
    <!--搜索参数、查询、导出-->

    <!--列表-->
    <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;">
      <template v-for="(item, index) in colums">
        <el-table-column
          v-if="item.key === 'operation'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p>
              <el-button type="text" class="info-btn" @click="goToEdit(scope.row)">查看</el-button>
            </p>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'activityType'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p>{{scope.row.activityType | arrGetValue(typeArr, 'key', 'value')}}</p>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'payValidFee'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p>{{scope.row.payValidFee | emptyValueFilter(null, null, null, '元')}}</p>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'amount'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p>{{scope.row.amount | emptyValueFilter}}{{ scope.row.type | arrGetValue(amountArr, 'key', 'value')}}</p>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'payValidFee'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p>{{scope.row.payValidFee | emptyValueFilter(null, null, null, '元')}}</p>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.key === 'used'"
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        >
          <template slot-scope="scope">
            <p>赠送{{scope.row.get | emptyValueFilter}}次,已使用{{scope.row.used | emptyValueFilter}}次</p>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :key="index"
          :prop="item.key"
          :label="item.label"
          :width="item.width"
          :sortable="item.sortable"
          align="center"
        />
      </template>
    </el-table>
    <el-pagination
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      :current-page="pageInfo.pageIndex"
      background
      layout="total, prev, pager, next, sizes, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!--列表-->
  </div>
</template>

<script>
  import {couponExportList, couponQueryList} from "@api/professionalWork/coupon";
  import {dateFormat, queryParams} from "@js/utils";
  import { downloadFileRamToken } from '@/utils/menu'

  export default {
    name: "couponList",
    props: {},
    components: {},
    data() {
      return {
        date: [], // 起止日期
        // 查询参数
        formData: {
          beginCreated: '', // 由起止日期数组转化得到
          endCreated: '', // 由起止日期数组转化得到
        },
        // 列表参数
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          loading: false,
          list: [],
        },
        // 列表每一列参数
        colums: [
          {key: 'operation', label: '操作', width: '100px'},
          {key: 'telephone', label: '经销商账号'},
          {key: 'adOrgName', label: '经销商姓名'},
          {key: 'activityType', label: '活动类型'},
          {key: 'validFee', label: '充值金额'},
          {key: 'fixed', label: '赠送游戏币/立减面额'},
          {key: 'payValidFee', label: '使用条件(下次支付)'},
          {key: 'used', label: '使用统计'},
          {key: 'beginTime', label: '开始时间'},
          {key: 'endTime', label: '结束时间'},
        ],
        // 日期插件的时间选择范围
        pickerOptions: {
          // 禁止选择时间范围限制，return true 无法选择
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
          // 点击选择时间的回调，只选择一个返回默认是最小时间，｛null，minDate｝
          onPick({maxDate, minDate}) {
          },
        },
        days: 30, // 初始化时间开始日期小于当前时间的天数
        // 活动类型
        typeArr: [
          {key: 3, value: '充值送币券'},
          {key: 2, value: '支付立减券'},
        ],
        // 赠送游戏币/立减面额类型
        amountArr: [
          {key: 3, value: '个'}, // 赠送游戏币,充值送币券
          {key: 2, value: '元'}, // 立减面额,支付立减券
        ],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
      this.init();
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      init() {
        this.initDataPicker();
        this.queryBtn();
      },
      initDataPicker() {
        // 初始化date和fordate的值
        const beginCreated = dateFormat(new Date(Date.now() - this.days * 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
        const endCreated = dateFormat(new Date(new Date().setHours(0, 0, 0, 0)), 'yyyy-MM-dd');
        this.date = [beginCreated, endCreated];
        this.changeDate(this.date);
        //初始化datepicker最大时间和跨度和选中回调
        this.pickerOptions = {
          shortcuts: [
            {
              text: '昨天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 1; // 最大天数
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - times * days);
                end.setTime(end.getTime() - times * days);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '7天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 6; // 最大天数
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - times * days);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '30天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 29; // 最大天数
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - times * days);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '本月',
              onClick(picker) {
                const date = new Date();
                const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);
                nextMonth.setDate(0);
                const end = new Date();
                const start = new Date(date.getFullYear(), date.getMonth(), 1);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '上月',
              onClick(picker) {
                const date = new Date();
                date.setMonth(date.getMonth() - 1);
                const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);
                nextMonth.setDate(0);
                const end = new Date(date.getFullYear(), date.getMonth(), nextMonth.getDate());
                const start = new Date(date.getFullYear(), date.getMonth(), 1);
                picker.$emit('pick', [start, end]);
              }
            },
          ],
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
          onPick({maxDate, minDate}) {
            const days = 180; // 最大天数
            const times = days * 24 * 60 * 60 * 1000; // 最大毫秒数
            // 不能选择今天之后的日期
            const maxToday = (pickTime) => {
              return pickTime.getTime() > Date.now();
            };
            // 不能选择时间跨度超过最大毫秒数的日期范围，不能选择今天之后的日期
            const maxDays = (hoverTime, pickTime, times) => {
              if (maxToday(pickTime)) {
                return maxToday(pickTime);
              }
              if (maxToday(hoverTime)) {
                return maxToday(hoverTime);
              }
              return pickTime.getTime() > hoverTime.getTime() + times || pickTime.getTime() < hoverTime.getTime() - times;
            }
            // 如果只选择了一个日期，限制时间跨度
            if (!maxDate && minDate) {
              this.disabledDate = (time) => {
                return maxDays(time, minDate, times);
              };
            }
            // 其他只限制不能选择今天之后的日期
            else {
              this.disabledDate = (time) => {
                return maxToday(time);
              };
            }
          }
        }
      },
      // 选择结算日期
      changeDate(dates) {
        if (dates) {
          this.formData.beginCreated = dates[0];
          this.formData.endCreated = dates[1];
        } else {
          this.formData.beginCreated = '';
          this.formData.endCreated = ''
        }
      },
      // 查询按钮
      queryBtn() {
        this.pageInfo.pageSize = 20;
        this.pageInfo.pageIndex = 1;
        this.pageInfo.total = 0;
        this.queryList();
      },
      // 导出按钮
      exportBtn() {
        this.isParamsComplate(() => {
          downloadFileRamToken(encodeURI(couponExportList().url + `?${queryParams(this.getDateParams())}`), true);
        })
      },
      // 参数是否完整
      isParamsComplate(call) {
        let message = '';
        if (!(this.formData.beginCreated && this.formData.endCreated)) {
          message = '请选择开始日期和结束日期';
        } else {
          call();
          return;
        }
        this.$message({
          message: message,
          type: 'warning'
        });
      },
      // 列表查询
      queryList() {
        this.pageInfo.loading = true;
        couponQueryList(this.getListParams()).then(res => {
          this.pageInfo.loading = false;
          if (res.result === 0) {
            if (res.data) {
              this.pageInfo.list = res.data.list.map(item => {
                const datas = item.details[0]
                item.fixed = datas.rewardDetails.fixed
                item.validFee = datas.postTrigger?datas.postTrigger.map(data=>{
                  if(data.type == 'distribute'){
                    return data.target
                  }
                }):'--'
                item.payValidFee = datas.rewardDetails.payValidFee
                return item
              }) || [];
              this.pageInfo.total = res.data.total || 0;
            }
          }
        }).catch(_=>{
          this.pageInfo.loading = false;
        })
      },
      // 获取查询列表参数
      getListParams() {
        const params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        return Object.assign(params, this.getDateParams())
      },
      // 获取时间参数
      getDateParams() {
        const params = {};
        if (this.formData.beginCreated) {
          params.beginCreated = this.formData.beginCreated;
        }
        if (this.formData.endCreated) {
          params.endCreated = this.formData.endCreated;
        }
        return params;
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.queryList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.queryList();
      },
      // 跳转到详情页
      goToEdit(row) {
        this.$router.push({
          path: '/coupon/edit',
          query: row
        })
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="less" scoped>
  .info-btn {
    width: 100%;
  }
</style>
