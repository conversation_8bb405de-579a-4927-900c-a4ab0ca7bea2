<!--
日期：2019/9/27
功能：超值卡路由页面
作者：杨楠锋
-->
<template>
    <div>
      <div>
        <keep-alive>
          <router-view v-if="$route.meta.keepAlive"></router-view>
        </keep-alive>
        <router-view v-if="!$route.meta.keepAlive"></router-view>
      </div>
    </div>
</template>

<script>
    export default {
        name: "index",
        props: {},
        components: {},
        data() {
            return {};
        },

        beforeCreate() {
        },

        created() {
        },

        beforeMount() {
        },

        mounted() {
        },

        beforeUpdate() {
        },

        updated() {
        },

        activated() {
        },

        deactivated() {
        },

        beforeDestroy() {
        },

        destroyed() {
        },

        errorCaptured() {
        },

        methods: {},

        computed: {},

        watch: {}

    }

</script>
<style lang="less" scoped>
</style>
