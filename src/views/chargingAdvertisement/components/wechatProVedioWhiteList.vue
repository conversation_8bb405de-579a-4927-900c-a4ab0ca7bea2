<!--
 * @Description: 支付完成视频白名单--流量小程序
 * @Author: chenweijie
 * @Date: 2025/09/03 10:34
 * @LastEditors: chenweijie
 * @LastEditTime: 2025/09/03 10:34
 -->
<template>
  <div class="pro-countdown-list">
    <SearchBar
      type="wechatProVideo"
      @onComponentQuerySearch="onQuerySearch"
      @onComponentExportExcel="onExportExcel"
      @onComponentAddNewUser="onToggleDialog"
      @onComponentRefreshList="getListInfo(true)"
    />
    <TableList
      type="wechatProVideo"
      :list="list"
      :loading="common.loading"
      @onComponentDeleteItem="onDeleteItem"
    />
    <Pagination
      :pageSize="page.pageSize"
      :pageIndex="page.pageIndex"
      :total="page.total"
      @onComponentSizeChange="handleSizeChange"
      @onComponentCurrentChange="handleCurrentChange"
    />
    <NewUserDialog
      type="wechatProVideo"
      :visible="dialog.visible"
      @onComponentToggleDialog="onToggleDialog"
      @onComponentRefreshList="getListInfo"
    />
    <EditPlaceModal type="wechatProVideo" @onComponentRefreshList="getListInfo" />
  </div>
</template>
<script>
import SearchBar from '../../chargeInsurance/components/search';
import TableList from '../../chargeInsurance/components/table';
import Pagination from '../../chargeInsurance/components/pagination';
import NewUserDialog from '../../chargeInsurance/components/newUserDialog.vue';
import EditPlaceModal from '../../chargeInsurance/components/editPlaceModal.vue';
import { queryParams } from '@/utils/utils';
import Mixins from '../../chargeInsurance/mixins/mixins';
import {
  flowMiniWhilePage,
  delFlowMiniById,
  exportFlowMiniWhileList,
} from '@/api/advertisement/flowMiniProgram';
import { downloadFileRamToken } from '@/utils/menu';

export default {
  name: 'FlowMiniprogramWhiteList',
  mixins: [Mixins],
  components: {
    SearchBar,
    TableList,
    Pagination,
    NewUserDialog,
    EditPlaceModal,
  },
  mounted() {
    this.$nextTick(() => {
      this.getListInfo(true);
    });
  },
  methods: {
    /**
     * 获取列表数据
     */
    async getListInfo(init) {
      if (init) {
        this.page.pageIndex = 1;
      }
      const params = {
        pageSize: this.page.pageSize,
        pageIndex: this.page.pageIndex,
        distributorId: this.form.lyyDistributorId,
        distributorName: this.form.name,
        phone: this.form.phone,
        type: 27,
      };
      this.common.loading = true;
      const res = await flowMiniWhilePage(params);
      this.common.loading = false;
      if (res && res.result == 0) {
        this.list = res.data.list;
        this.page.total = res.data.total;
      }
    },
    /**
     * 查询
     */
    onQuerySearch(form) {
      this.form = form;
      this.getListInfo(true);
    },
    /**
     * 导出excel
     */
    onExportExcel(form) {
      this.form = form;
      const params = {
        lyyDistributorId: this.form.lyyDistributorId,
        name: this.form.name,
        phone: this.form.phone,
        type: 27,
      };
      downloadFileRamToken(exportFlowMiniWhileList() + `?${queryParams(params)}`);
    },
    /**
     * 新增用户弹窗控制
     */
    onToggleDialog() {
      this.dialog.visible = !this.dialog.visible;
    },
    /**
     * 改变列表每页条数
     */
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getListInfo(true);
    },
    /**
     * 改变页码
     */
    handleCurrentChange(val) {
      this.page.pageIndex = val;
      this.getListInfo();
    },
    /**
     * 删除
     */
    async onDeleteItem(lyyDistributorId) {
      const params = {
        lyyDistributorId,
        type: 27,
      };
      const res = await delFlowMiniById(params);
      if (res && res.result == 0 && res.data) {
        this.$message.success('删除成功');
        this.getListInfo();
      }
    },
  },
};
</script>
<style lang="less" scoped></style>
