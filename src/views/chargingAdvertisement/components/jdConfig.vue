<!--
 * @Description: 京东白条
 * @Author: chenweijie
 * @Date: 2025/09/03 09:21
 * @LastEditors: chenweijie
 * @LastEditTime: 2025/09/03 09:21
 -->
<template>
  <div class="jd-config">
    <el-form>
      <el-form-item>
        <div class="flexbox">
          <div class="config-item">
            <div class="item-title">首页Banner</div>
            <div class="img-panel">
              <el-upload
                class="avatar-uploader"
                action="/gw/admin/saas/file/upload"
                :headers="headerRamToken"
                :show-file-list="false"
                :on-success="handleAvatarJdBannerSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="config.jdBanner" :src="config.jdBanner" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div v-show="config.jdBanner" class="delete-btn" @click="handleDeletePic('jdBanner')">删除图片</div>
            </div>
          </div>
          <div class="config-item">
            <div class="item-title">购物车小横条</div>
            <div class="img-panel">
              <el-upload
                class="avatar-uploader"
                action="/gw/admin/saas/file/upload"
                :headers="headerRamToken"
                :show-file-list="false"
                :on-success="handleAvatarJdCartBarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="config.jdCartBar" :src="config.jdCartBar" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div v-show="config.jdCartBar" class="delete-btn" @click="handleDeletePic('jdCartBar')">删除图片</div>
            </div>
          </div>
          <div class="config-item">
            <div class="item-title">成功弹窗</div>
            <div class="img-panel">
              <el-upload
                class="avatar-uploader"
                action="/gw/admin/saas/file/upload"
                :headers="headerRamToken"
                :show-file-list="false"
                :on-success="handleAvatarJdSuccessImageSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="config.jdSuccessImage" :src="config.jdSuccessImage" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div v-show="config.jdSuccessImage" class="delete-btn" @click="handleDeletePic('jdSuccessImage')">删除图片</div>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="京东白条赠送红包金额：">
        <el-input class="normal-input" v-model.trim="config.jdWhiteAmount" />元
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSaveConfig">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { headerRamToken } from '@/utils/menu'
import { getJdConfig, saveJdConfig } from '@/api/advertisement/advertise';
import { validateMoney } from '@/utils/validate';

export default {
  name: 'JdConfig',
  data() {
    return {
      headerRamToken,
      common: {
        updateLoading: null, // 图片是否上传中
      },
      config: {
        jdBanner: '', // 京东首页banner
        jdCartBar: '', // 京东购物车小横条
        jdSuccessImage: '', // 京东支付成功弹窗图片
        jdWhiteAmount: '5', // 京东白条赠送金额
      },
    };
  },
  created() {
    this.getJdConfigFn()
  },
  methods: {
    /**
     * 获取京东配置
     */
    async getJdConfigFn() {
      const { result, data } = await getJdConfig()
      if (result === 0 && data) {
        this.config = data
      }
    },
    /**
     * jdBanner上传图片
     * @param res 
     */
    handleAvatarJdBannerSuccess(res) {
      if (res?.result === 0 && res?.data) {
        this.config.jdBanner = res.data?.pictureUrl
      }
      this.common.updateLoading.close();
    },
    /**
     * jdCartBar上传图片
     * @param res 
     */
    handleAvatarJdCartBarSuccess(res) {
      if (res?.result === 0 && res?.data) {
        this.config.jdCartBar = res.data?.pictureUrl
      }
      this.common.updateLoading.close();
    },
    /**
     * jdSuccessImage上传图片
     * @param res 
     */
    handleAvatarJdSuccessImageSuccess(res) {
      if (res?.result === 0 && res?.data) {
        this.config.jdSuccessImage = res.data?.pictureUrl
      }
      this.common.updateLoading.close();
    },
    /**
     * 图片上传前校验
     */
    beforeAvatarUpload(file) {
      this.common.updateLoading = this.$loading({
        lock: true,
        text: '上传图片中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isGIF = file.type === 'image/gif';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG && !isPNG && !isGIF) {
        this.$message.error('图片格式暂只支持jpg, png, gif!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过2MB!');
      }
      return (isJPG || isPNG || isGIF) && isLt2M;
    },
    /**
     * 删除图片
     * @params {String} type--类型
     * ex: jdBanner ｜ jdCartBar | jdSuccessImage
     */
    handleDeletePic(type) {
      this.config[type] = ''
    },
    /**
     * 保存配置
     */
    async handleSaveConfig() {
      if (!validateMoney(this.config.jdWhiteAmount)) {
        this.$message.error('请输入正确的红包金额')
        return
      }
      const { result } = await saveJdConfig(this.config)
      if (result === 0) {
        this.$message.success('保存成功')
      }
    },
  },
}
</script>
<style lang="less" scoped>
.jd-config {
  .config-item {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-right: 18px;
    .item-title {
      margin-bottom: 8px;
      text-align: center;
    }
    .delete-btn {
      position: absolute;
      cursor: pointer;
      width: 100%;
      height: 40px;
      left: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      text-align: center;
      line-height: 40px;
      color: #fff;
      font-size: 14px;
    }
  }
  .normal-input {
    width: 80px;
    margin: 0 8px;
  }
  /deep/ .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  /deep/ .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  /deep/ .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 150px;
    line-height: 150px;
    text-align: center;
  }
  /deep/ .avatar {
    width: 150px;
    height: 150px;
    display: block;
  }
}
</style>