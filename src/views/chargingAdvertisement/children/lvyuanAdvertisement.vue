<template>
  <div class="lvyuan-advertisement">
    <div class="title-layout">绿源广告</div>
    <el-form
      label-position="left"
      label-width="80px"
      class="ml-18"
    >
      <el-form-item label="总开关">
        <el-switch v-model="common.switchStatus"></el-switch>
      </el-form-item>
      <el-form-item label="红包设置">
        <div class="mb-18">单个红包金额<el-input class="normal-input" v-model.trim="couponConfig.amount" />元</div>
        <div>单日红包数量限制<el-input class="normal-input" v-model.trim="couponConfig.limitNumber" />个</div>
      </el-form-item>
    </el-form>
    <div class="title-layout">广告图设置</div>
    <el-form
      label-position="left"
      label-width="120px"
      class="ml-18"
    >
      <el-form-item label="红包版广告图">
        <div class="flexbox">
          <div class="config-item" v-for="(item, index) in redPacketImgConfig">
            <div class="item-title">{{ item.name }}</div>
            <div class="img-panel" @click="handleOnProgress('redPacket', index)">
              <el-upload
                class="avatar-uploader"
                action="/gw/admin/saas/file/upload"
                :headers="headerRamToken"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="item && item.image" :src="item.image" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div v-show="item && item.image" class="delete-btn" @click="handleDeletePic(index)">删除图片</div>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="购物券版广告图">
        <div class="flexbox">
          <div class="config-item" v-for="(item, index) in couponImgConfig">
            <div class="item-title">{{ item.name }}</div>
            <div class="img-panel" @click="handleOnProgress('coupon', index)">
              <el-upload
                class="avatar-uploader"
                action="/gw/admin/saas/file/upload"
                :headers="headerRamToken"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="item && item.image" :src="item.image" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div v-show="item && item.image" class="delete-btn" @click="handleDeletePic(index)">删除图片</div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="handleSaveConfig">保存配置</el-button>
    <div class="title-layout">电池检测引流图</div>
    <el-form>
      <el-form-item>
        <div class="flexbox">
          <div class="config-item">
            <div class="item-title">检测结果页banner</div>
            <div class="img-panel">
              <el-upload
                class="avatar-uploader"
                action="/gw/admin/saas/file/upload"
                :headers="headerRamToken"
                :show-file-list="false"
                :on-success="handleBannerSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="banner && banner.bannerImg" :src="banner.bannerImg" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div v-show="banner && banner.bannerImg" class="delete-btn" @click="handleDeleteBanner">删除图片</div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="handleSaveBanner">保存banner</el-button>
    <div class="title-layout">
      <span>红包/券派发记录</span>
      <span class="title-tips">（今天已派发个{{ common.sendCouponNumber }}红包）</span>
    </div>
    <el-form :inline="true">
      <el-form-item label="用户ID">
        <el-input v-model="form.lyyUserId"></el-input>
      </el-form-item>
      <el-form-item label="商家ID">
        <el-input v-model="form.lyyDistributorId"></el-input>
      </el-form-item>
      <el-form-item label="版本">
        <el-select v-model="form.activityTypeValue">
          <el-option
            v-for="item in activityTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择日期">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeDate">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getListInfo(true)">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleExportExcel">导出EXCEL</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="list"
      stripe
      v-loading="common.loading"
      style="width: 100%;"
    >
      <el-table-column label="用户ID" align="center" prop="lyyUserId"></el-table-column>
      <el-table-column label="用户昵称" align="center" prop="nickName"></el-table-column>
      <el-table-column label="商家ID" align="center" prop="lyyDistributorId"></el-table-column>
      <el-table-column label="红包金额" align="center">
        <template slot-scope="scope">
          {{ scope.row.amount / 100 }}
        </template>
      </el-table-column>
      <el-table-column label="领取位置" align="center">
        <template slot-scope="scope">
          {{ seatSourceMap[scope.row.seatSource] }}
        </template>
      </el-table-column>
      <el-table-column label="代理商ID" align="center" prop="agentId"></el-table-column>
      <el-table-column label="代理商" align="center" prop="agentName"></el-table-column>
      <el-table-column label="领取时间" align="center" prop="receiveTime"></el-table-column>
      <el-table-column label="有效期" align="center" prop="effectiveTime"></el-table-column>
    </el-table>
    <Pagination
      :pageSize="page.pageSize"
      :pageIndex="page.pageIndex"
      :total="page.total"
      @onComponentSizeChange="handleSizeChange"
      @onComponentCurrentChange="handleCurrentChange"
    />
  </div>
</template>
<script>
import Pagination from '../../chargeInsurance/components/pagination';
import { formatDate, queryParams } from '@/utils/utils';
import {
  getSettingInfo,
  getGreenSourceOrderByPage,
  exportGreenSourceOrder,
  updateSettingInfo,
  totalOrderNumByToday,
} from '@/api/advertisement/lvyuan';
import {
  findOneServiceDialogImg,
  updateOneServiceDialogImg,
} from '@/api/chargeInsurance/monthCard';
import { headerRamToken, downloadFileRamToken } from '@/utils/menu'


export default {
  name: "lvyuanAdvertisement",
  components: {
    Pagination,
  },
  data() {
    return {
      headerRamToken,
      list: [], // 派发记录列表
      dateRange: [],
      common: {
        loading: false, // 是否加载中
        updateLoading: null, // 上传图片是否加载中
        switchStatus: false, // 总开关
        imgHandleIndex: '', // 当前广告图操作的图片index
        imgHandleType: '', // 当前广告图操作的图片类型
        sendCouponNumber: 0, // 当天已发红包数量
        lyyCdzGreenSourceSettingId: '', // 查询的id，必须
      },
      banner: {
        bannerImg: '', // 图片
      },
      couponConfig: { // 红包设置
        amount: 0, // 单个红包金额
        limitNumber: 0, // 限额数量
      },
      redPacketImgConfig: [ //红包-广告图设置
        {
          name: '首页弹窗',
          image: '',
        },
        {
          name: '首页顶部轮播',
          image: '',
        },
        {
          name: '点金banner',
          image: '',
        },
        {
          name: '倒计时banner',
          image: '',
        }
      ],
      couponImgConfig: [ //购物券-广告图设置
        {
          name: '首页弹窗',
          image: '',
        },
        {
          name: '首页顶部轮播',
          image: '',
        },
        {
          name: '点金banner',
          image: '',
        },
        {
          name: '倒计时banner',
          image: '',
        }
      ],
      form: {
        lyyUserId: '', // 用户id
        lyyDistributorId: '', // 商家id
        activityTypeValue: 1, // 1红包版 2券版
      },
      page: {
        pageIndex: 1, // 页数
        pageSize: 20, // 条数
        total: 0, // 总数
      },
      activityTypeOptions: [
        {
          label: '红包版',
          value: 1,
        },
        {
          label: '券版',
          value: 2,
        }
      ],
      seatSourceMap: {
        1: '首页弹窗',
        2: '首页顶部轮播',
        3: '点金banner',
        4: '倒计时banner',
      },
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.dateRange = [
        formatDate(new Date(new Date() - 6 * 24 * 60 * 60 * 1000),'yyyy-MM-dd'),
        formatDate(new Date(),'yyyy-MM-dd')
      ];
      this.getConfigInfo();
      this.getBanner();
      this.getSendCouponNumber();
      this.getListInfo(true);
    })
  },
  methods: {
    /**
     * 获取配置信息
     */
    async getConfigInfo() {
      const res = await getSettingInfo();
      if (res?.result == 0 && res.data) {
        this.couponConfig.amount = res.data.redPacketAmount / 100;
        this.couponConfig.limitNumber = res.data.redPacketNum;
        this.redPacketImgConfig[0].image = res.data.redHomeImg;
        this.redPacketImgConfig[1].image = res.data.redTopImg;
        this.redPacketImgConfig[2].image = res.data.redDotmoreImg;
        this.redPacketImgConfig[3].image = res.data.redCountdownImg;
        this.couponImgConfig[0].image = res.data.couponHomeImg;
        this.couponImgConfig[1].image = res.data.couponTopImg;
        this.couponImgConfig[2].image = res.data.couponDotmoreImg;
        this.couponImgConfig[3].image = res.data.couponCountdownImg;
        this.common.switchStatus = !!res.data.status;
        this.common.lyyCdzGreenSourceSettingId = res.data.lyyCdzGreenSourceSettingId
      }
    },
    /**
     * 获取banner图片
     */
    async getBanner() {
      const params = {
        type: 4
      }
      const res = await findOneServiceDialogImg(params)
      if (res && res.result == 0 && res.data) {
        this.banner = res.data
      }
    },
    /**
     * 获取数据列表
     */
    async getListInfo(init) {
      if (init) {
        this.page.pageIndex = 1;
      }
      const params = {
        pageSize: this.page.pageSize,
        pageIndex: this.page.pageIndex,
        activityType: this.form.activityTypeValue,
        lyyUserId: this.form.lyyUserId,
        lyyDistributorId: this.form.lyyDistributorId,
        startTime: this.dateRange && (this.dateRange[0] + ' 00:00:00'),
        endTime: this.dateRange && (this.dateRange[1] + ' 23:59:59'),
      };
      this.common.loading = true;
      const res = await getGreenSourceOrderByPage(params);
      this.common.loading = false;
      if (res?.result == 0 && res.data) {
        this.list = res.data.list;
        this.page.total = res.data.total;
      }
    },
    /**
     * 获取已发红包个数
     */
    async getSendCouponNumber() {
      const res = await totalOrderNumByToday();
      if (res?.result == 0) {
        this.common.sendCouponNumber = res.data;
      }
    },
    /**
     * 保存配置
     */
    async handleSaveConfig() {
      const updateValidate = this.handleUpdateValidator();
      if (!updateValidate) return;
      const params = {
        redPacketAmount: this.couponConfig.amount * 100,
        redPacketNum: this.couponConfig.limitNumber,
        redHomeImg: this.redPacketImgConfig[0].image,
        redTopImg: this.redPacketImgConfig[1].image,
        redDotmoreImg: this.redPacketImgConfig[2].image,
        redCountdownImg: this.redPacketImgConfig[3].image,
        couponHomeImg: this.couponImgConfig[0].image,
        couponTopImg: this.couponImgConfig[1].image,
        couponDotmoreImg: this.couponImgConfig[2].image,
        couponCountdownImg: this.couponImgConfig[3].image,
        status: Number(this.common.switchStatus),
        lyyCdzGreenSourceSettingId: this.common.lyyCdzGreenSourceSettingId,
      }
      const res = await updateSettingInfo(params);
      if (res?.result == 0 && res.data) {
        this.$message.success('更新成功');
      } else {
        this.$message.error('更新失败');
      }
    },
    /**
     * 保存banner
     */
    async handleSaveBanner() {
      const res = await updateOneServiceDialogImg(this.banner)
      if (res?.result == 0) {
        this.$message.success('保存成功')
      }
    },
    /**
     * 获取当前操作的图片index
     * @params {String} type[redPacket/coupon] -- 操作的广告图类型
     * @params {Number} index--操作的索引
     */
    handleOnProgress(type, index) {
      this.common.imgHandleType = type;
      this.common.imgHandleIndex = index;
    },
    /**
     * 上传图片
     */
    handleAvatarSuccess(res) {
      if (res && res.result == 0 && res.data) {
        // 延时，为了拿到imgHandleType/imgHandleIndex
        setTimeout(() => {
          console.log(this.redPacketImgConfig, this.common.imgHandleIndex, this.common.imgHandleType)
          if (this.common.imgHandleType == 'redPacket') {
            this.redPacketImgConfig[this.common.imgHandleIndex].image = res.data.pictureUrl;
          } else {
            this.couponImgConfig[this.common.imgHandleIndex].image = res.data.pictureUrl;
          }
        }, 200)
      }
      this.common.updateLoading.close();
    },
    /**
     * 上传检测结果页banner
     */
    handleBannerSuccess(res) {
      if (res?.result == 0 && res.data) {
        this.banner.bannerImg = res.data.pictureUrl
      }
      this.common.updateLoading.close();
    },
    /**
     * 处理保存校验
     */
    handleUpdateValidator() {
      let priceReg = /^(\d{1,2}(\.\d{1,2})?|100|100.0|100.00)$/; // 价格正则
      if (!priceReg.test(this.couponConfig.amount)) {
        this.$message.warning('请填写正确的红包金额');
        return false;
      }
      if (isNaN(this.couponConfig.limitNumber) || this.couponConfig.limitNumber < 0) {
        this.$message.warning('请填写正确的红包数量限制');
        return false;
      }
      return true;
    },
    /**
     * 图片上传前校验
     */
    beforeAvatarUpload(file) {
      this.common.updateLoading = this.$loading({
        lock: true,
        text: '上传图片中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isGIF = file.type === 'image/gif';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG && !isPNG && !isGIF) {
        this.$message.error('图片格式暂只支持jpg, png, gif!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过2MB!');
      }
      return (isJPG || isPNG || isGIF) && isLt2M;
    },
    /**
     * 删除图片
     * @params {Number} index--操作的索引
     */
    handleDeletePic(index) {
      // 延时，不然index不生效
      setTimeout(() => {
        if (this.common.imgHandleType == 'redPacket') {
          this.redPacketImgConfig[index].image = '';
        } else {
          this.couponImgConfig[index].image = '';
        }
      }, 200)
    },
    /**
     * 删除banner图片
     */
    handleDeleteBanner() {
      this.banner.bannerImg = ''
    },
    /**
     * 选择时间
     */
    changeDate(val) {
      this.dateRange = val;
    },
    /**
     * 导出列表
     */
    handleExportExcel() {
      const params = {
        activityType: this.form.activityTypeValue,
        lyyUserId: this.form.lyyUserId,
        lyyDistributorId: this.form.lyyDistributorId,
        startTime: this.dateRange && (this.dateRange[0] + ' 00:00:00'),
        endTime: this.dateRange && (this.dateRange[1] + ' 23:59:59'),
      }
      downloadFileRamToken(exportGreenSourceOrder() + `?${queryParams(params)}`);
    },
    /**
     * 改变列表每页条数
     */
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getListInfo(true);
    },
    /**
     * 改变页码
     */
    handleCurrentChange(val) {
      this.page.pageIndex = val;
      this.getListInfo();
    },
  },
}
</script>
<style lang="less" scoped>
.lvyuan-advertisement {
  .title-layout {
    margin: 18px 0;
    font-size: 20px;
    font-weight: bold;
    .title-tips {
      font-size: 14px;
    }
  }
  .normal-input {
    width: 80px;
    margin-left: 8px;
    margin-right: 8px;
  }
  .config-item {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-right: 18px;
    .item-title {
      margin-bottom: 8px;
      text-align: center;
    }
    .delete-btn {
      position: absolute;
      cursor: pointer;
      width: 100%;
      height: 40px;
      left: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      text-align: center;
      line-height: 40px;
      color: #fff;
      font-size: 14px;
    }
  }
  /deep/ .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  /deep/ .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  /deep/ .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 150px;
    line-height: 150px;
    text-align: center;
  }
  /deep/ .avatar {
    width: 150px;
    height: 150px;
    display: block;
  }
}
.mb-18 {
  margin-bottom: 18px;
}
.ml-18 {
  margin-left: 18px;
}
</style>