<!--
 * @Description: 充电桩流量小程序
 * @Author: chen<PERSON>jie
 * @Date: 2023/03/09 09:21
 * @LastEditors: chenweijie
 * @LastEditTime: 2023/03/09 09:21
 -->
<template>
  <div class="flow-miniprogram">
    <el-tabs v-model="tabs.current" type="border-card" class="list-tabs">
      <el-tab-pane label="商户白名单" name="whiteList">
        <FlowMiniWhiteList v-if="tabs.current === 'whiteList'" />
      </el-tab-pane>
      <el-tab-pane label="倒计时白名单" name="countdownwhiteList">
        <CountdownWhiteList v-if="tabs.current === 'countdownwhiteList'" />
      </el-tab-pane>
      <el-tab-pane label="Pro倒计时白名单" name="proCountdownWhiteList">
        <ProCountdownWhiteList v-if="tabs.current === 'proCountdownWhiteList'" />
      </el-tab-pane>
      <el-tab-pane label="支付完成视频白名单" name="wechatProVedioWhiteList">
        <WechatProVedioWhiteList v-if="tabs.current === 'wechatProVedioWhiteList'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import FlowMiniWhiteList from '../components/flowMiniWhiteList';
import CountdownWhiteList from '../components/countdownWhiteList.vue';
import ProCountdownWhiteList from '../components/proCountdownWhiteList.vue';
import WechatProVedioWhiteList from '../components/wechatProVedioWhiteList.vue';

export default {
  name: 'FlowMiniProgram',
  components: {
    FlowMiniWhiteList,
    CountdownWhiteList,
    ProCountdownWhiteList,
    WechatProVedioWhiteList
  },
  data() {
    return {
      tabs: {
        current: 'whiteList', // tabs当前选中的
      }
    };
  },
}
</script>