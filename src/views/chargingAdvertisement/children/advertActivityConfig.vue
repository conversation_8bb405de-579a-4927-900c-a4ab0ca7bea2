<!--
 * @Description: 充电桩商创活动配置
 * @Author: ch<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/09/03 09:21
 * @LastEditors: chenweijie
 * @LastEditTime: 2025/09/03 09:21
 -->
<template>
  <div class="activity-config">
    <el-tabs v-model="tabs.current" type="border-card" class="list-tabs">
      <el-tab-pane label="京东白条" name="JD">
        <JdConfig v-if="tabs.current === 'JD'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import JdConfig from '../components/jdConfig.vue';

export default {
  name: 'ActivityConfig',
  components: {
    JdConfig
  },
  data() {
    return {
      tabs: {
        current: 'JD', // tabs当前选中的
      }
    };
  },
}
</script>
<style lang="less" scoped></style>