<!--
 * @Description: 上线名单管理--充电桩自动续费（首页弹窗名单）
 * @Author: ch<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/08/14 11:34
 * @LastEditors: chenweijie
 * @LastEditTime: 2024/08/14 11:34
 -->
<template>
  <div class="member-card-dialog-list">
    <SearchBar
      type="memberCardDialogList"
      whiteType="14"
      @onComponentQuerySearch="onQuerySearch"
      @onComponentExportExcel="onExportExcel"
      @onComponentAddNewUser="onToggleDialog"
      @onComponentRefreshList="getListInfo(true)"
    />
    <TableList
      type="memberCardDialogList"
      :list="list"
      :loading="common.loading"
      @onComponentDeleteItem="onDeleteItem"
    />
    <Pagination
      :pageSize="page.pageSize"
      :pageIndex="page.pageIndex"
      :total="page.total"
      @onComponentSizeChange="handleSizeChange"
      @onComponentCurrentChange="handleCurrentChange"
    />
    <NewUserDialog
      type="memberCardDialogList"
      :visible="dialog.visible"
      whiteType="14"
      @onComponentToggleDialog="onToggleDialog"
      @onComponentRefreshList="getListInfo"
    />
    <EditPlaceModal
      type="memberCardDialogList"
      @onComponentRefreshList="getListInfo"
    />
  </div>
</template>
<script>
import Mixins from '../mixins/mixins';
import SearchBar from './search';
import TableList from './table';
import Pagination from './pagination.vue';
import NewUserDialog from './newUserDialog.vue';
import EditPlaceModal from './editPlaceModal.vue';
import { queryParams } from '@/utils/utils';
import { downloadFileRamToken } from '@/utils/menu';
import {
  memberCardPopDistributorPage,
  exportMemberCardPopNamePage,
  delMemberCardPopNameById,
} from '@/api/chargeInsurance/memberCard';
export default {
  name: 'memberCardDialogList',
  mixins: [Mixins],
  components: {
    SearchBar,
    TableList,
    Pagination,
    NewUserDialog,
    EditPlaceModal
  },
  mounted() {
    this.$nextTick(() => {
      this.getListInfo(true);
    });
  },
  methods: {
    /**
     * 获取列表数据
     */
    async getListInfo(init) {
      if (init) {
        this.page.pageIndex = 1;
      }
      const params = {
        pageSize: this.page.pageSize,
        pageIndex: this.page.pageIndex,
        distributorId: this.form.lyyDistributorId,
        distributorName: this.form.name,
        phone: this.form.phone,
        type: '14'
      };
      this.common.loading = true;
      const { result, data } = await memberCardPopDistributorPage(params);
      this.common.loading = false;
      if (result === 0 && data) {
        this.list = data.list;
        this.page.total = data.total;
      }
    },
    /**
     * 查询
     */
    onQuerySearch(form) {
      this.form = form;
      this.getListInfo(true);
    },
    /**
     * 导出excel
     */
    onExportExcel(form) {
      this.form = form;
      const params = {
        lyyDistributorId: this.form.lyyDistributorId,
        name: this.form.name,
        phone: this.form.phone,
        type: '14'
      };
      downloadFileRamToken(exportMemberCardPopNamePage() + `?${queryParams(params)}`);
    },
    /**
     * 新增用户
     */
    onToggleDialog() {
      this.dialog.visible = !this.dialog.visible;
    },
    /**
     * 改变列表每页条数
     */
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getListInfo(true);
    },
    /**
     * 改变页码
     */
    handleCurrentChange(val) {
      this.page.pageIndex = val;
      this.getListInfo();
    },
    /**
     * 删除
     */
    async onDeleteItem(lyyDistributorId) {
      const params = {
        lyyDistributorId,
        type: '14'
      };
      const { result, data } = await delMemberCardPopNameById(params);
      if (result === 0 && data) {
        this.$message.success('删除成功');
        this.getListInfo();
      }
    },
  },
};
</script>
<style lang="less" scoped></style>
