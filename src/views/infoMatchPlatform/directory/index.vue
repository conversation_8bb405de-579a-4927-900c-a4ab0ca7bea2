<!--
 * @Description: 文章栏目
 * @Author: wutao
 * @Date: 2022/5/19 13:59
 * @LastEditors: wutao
 * @LastEditTime: 2022/5/19 13:59
 -->
<template>
<div>
  <el-form :inline="true">
    <el-form-item label="类型：">
      <el-select v-model="searchData.type" placeholder="请选择类型" @change="getListDirectoryFn(true)">
        <el-option label="一级类目" :value="1"></el-option>
        <el-option label="文章标签" :value="2"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="success" @click="handleShowDialog(true)">创建</el-button>
    </el-form-item>
  </el-form>
  <el-table
    :data="tableData"
    border
    style="width: 100%"
    height="600"
    v-loading.lock="LoadingFlag"
  >
    <el-table-column
      prop="name"
      label="名称"
    >
    </el-table-column>
    <el-table-column
      prop="type"
      label="类型"
    >
      <template slot-scope="scope">
        <p>{{scope.row.type === 1? '一级类目':'文章标签'}}</p>
      </template>
    </el-table-column>
    <el-table-column
      prop="created"
      label="创建时间"
    >
    </el-table-column>
    <el-table-column
      label="操作"
    >
      <template slot-scope="scope">
        <el-button @click="handleShowDialog(false, scope.row)" type="primary" size="small" plain>编辑</el-button>
        <el-button @click="delDirectoryFn(scope.row.lyyDirectoryId)" type="danger" size="small" plain v-show="scope.row.type === 2">删除</el-button>
        <el-button @click="adjustDirectorySeqFn(scope.row.lyyDirectoryId, tableData[scope.$index-1].lyyDirectoryId, true)" type="success" size="small" plain :disabled="scope.$index === 0">上移</el-button>
        <el-button @click="adjustDirectorySeqFn(scope.row.lyyDirectoryId, tableData[scope.$index+1].lyyDirectoryId, false)" type="success" size="small" plain :disabled="scope.$index + 1 === tableData.length">下移</el-button>
        <el-button @click="topDirectoryFn(scope.row)" type="success" size="small" plain>置顶</el-button>
        <el-switch style="margin-left: 20px;" v-show="scope.row.type === 1" v-model="scope.row.isShow" :active-value="1" :inactive-value="0" @change="updateShowDirectoryFn(scope.row)"/>
      </template>
    </el-table-column>
  </el-table>
  <div class="pagination margin-top-10">
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
  <!-- 创建/编辑账号组 -->
  <el-dialog
    :title="dialog.title"
    :visible.sync="dialog.visible"
    width="30%"
  >
    <el-form :model="formData" :rules="rules" ref="formData" label-width="140px">
      <el-form-item label="类型：">
        <el-radio-group v-model="formData.type">
          <el-radio-button :label="1">一级栏目</el-radio-button>
          <el-radio-button :label="2">文章标签</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="名称：" prop="name">
        <el-input v-model.trim="formData.name" placeholder="请输入名称" style="width: 200px"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button @click="dialog.visible = false">取 消</el-button>
      <el-button type="primary" @click="saveDirectoryFn('formData')">保 存</el-button>
    </span>
  </el-dialog>
</div>
</template>

<script>
import {
  getListDirectory,
  saveDirectory,
  delDirectory,
  adjustDirectorySeq,
  topDirectory,
  updateShowDirectory
} from '@/api/infoMatchPlatform/directory'

export default {
  name: "index",
  data() {
    return {
      LoadingFlag: false,
      total: 0,//总数
      pageIndex: 1,//当前页码
      pageSize: 10,//每页大小
      tableData: [],
      dialog: {
        title: '创建类目',
        visible: false,
      },
      searchData: {
        type: 1,
      },
      formData: {
        name: '', // 名称
        type: 1, // 类型，1 栏目，2 标签
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' },],
      },
    };
  },
  mounted() {
    this.getListDirectoryFn(true)
  },
  methods: {
    // 分页选择
    handleCurrentChange(val) {
      this.pageIndex = val;
      this.getListDirectoryFn();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getListDirectoryFn();
    },
    // 查询目录列表
    async getListDirectoryFn(isInit) {
      if (isInit) { this.pageIndex = 1}; //初始化页数
      let params = {
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        type: this.searchData.type,
      };
      this.LoadingFlag = true;
      const res = await getListDirectory(params);
      this.LoadingFlag = false;
      if (res.result === 0 && res.data) {
        this.total = res.data.total;
        this.tableData = res.data.records;
      }
    },
    // 显示账号组弹窗
    handleShowDialog(isAdd, info) {
      if (isAdd) { // 创建
        this.dialog.title = '创建类目';
        this.formData = {
          name: '',
          type: 1,
        }
      } else { // 编辑
        this.formData = JSON.parse(JSON.stringify(info));
        this.dialog.title = '编辑类目';
        this.$refs["formData"] && this.$refs["formData"].clearValidate(); // 移除校验提示
      }
      this.dialog.visible = true;
    },
    // 保存
    saveDirectoryFn(formData) {
      this.$refs[formData].validate(async (valid) => {
        if (valid) {
          const res = await saveDirectory(this.formData);
          if (res.result === 0) {
            this.$message.success(`${this.formData.lyyDirectoryId? '修改成功' : '创建成功！'}`);
            this.dialog.visible = false;
            this.getListDirectoryFn(true);
          }
        } else {
          return false;
        }
      });
    },
    // 删除
    delDirectoryFn(id) {
      this.$confirm('确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await delDirectory([id]);
        if (res && res.result == 0) {
          this.$message.success('删除成功！');
          this.getListDirectoryFn(true);
        } else {
          this.$message.error(res.description);
        }
      }).catch(() => {});
    },
    // 上下移动
    async adjustDirectorySeqFn(sourceId,targetId,up) {
      let params = {
        sourceId,
        targetId,
        up,
      }
      const res = await adjustDirectorySeq(params);
      if (res.result === 0) {
        this.getListDirectoryFn();
      }
    },
    // 置顶
    async topDirectoryFn(info) {
      let params = {
        ids: [info.lyyDirectoryId],
        isTop: 1,
      }
      const res = await topDirectory(params);
      if (res.result === 0) {
        this.getListDirectoryFn(true);
      }
    },
    // 置顶
    async updateShowDirectoryFn(info) {
      console.log('info.isShow====', info,info.isShow);
      let params = {
        ids: [info.lyyDirectoryId],
        isShow: info.isShow ? 1: 0,
      }
      const res = await updateShowDirectory(params);
      if (res.result === 0) {
        this.$message.success('更新成功！');
        this.getListDirectoryFn();
      }
    },
  },
}
</script>

<style scoped>

</style>
