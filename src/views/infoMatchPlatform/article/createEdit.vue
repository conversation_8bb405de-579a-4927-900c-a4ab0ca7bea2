<!--
 * @Description: 创建编辑页
 * @Author: wutao
 * @Date: 2022/5/19 17:47
 * @LastEditors: wutao
 * @LastEditTime: 2022/5/19 17:47
 -->
<template>
  <el-form :model="formData" label-width="140px">
    <el-form-item label="文章来源：" prop="type">
      <el-radio-group v-model="formData.type" @change="handleChangeType">
        <el-radio :label="1">自主撰写</el-radio>
        <el-radio :label="2">跳转至公众号</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="一级栏目：" class="is-required">
      <el-select v-model="formData.directory.lyyDirectoryId" placeholder="请选择一级栏目" @change="handleChangeDirectory">
        <el-option
          v-for="item in allDirectory"
          :key="item.lyyDirectoryId"
          :label="item.name"
          :value="item.lyyDirectoryId">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="同步至其他栏目：">
      <el-select v-model="selectOtherDirectory" multiple placeholder="请选择" @change="handleChangeOtherDirectory">
        <el-option
          v-for="item in otherDirectory"
          :key="item.lyyDirectoryId"
          :label="item.name"
          :value="item.lyyDirectoryId">
        </el-option>
      </el-select>
    </el-form-item>
<!--    <el-form-item label="归入专题：">-->
<!--      <el-switch active-value="Y" inactive-value="N" @change=""/>-->
<!--    </el-form-item>-->
    <el-form-item label="选择专题：">
      <el-select v-model="formData.topic.lyyTopicId" placeholder="请选择专题" clearable>
        <el-option
          v-for="item in allTopic"
          :key="item.lyyTopicId"
          :label="item.name"
          :value="item.lyyTopicId">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="文章标签：">
      <el-select v-model="selectLable" multiple placeholder="请选择文章标签" @change="handleChangeLabel">
        <el-option
          v-for="item in allLabel"
          :key="item.lyyDirectoryId"
          :label="`#${item.name}`"
          :value="item.lyyDirectoryId">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="置顶：">
      <el-switch v-model="formData.isTop" :active-value="1" :inactive-value="0"/>
    </el-form-item>
    <el-form-item label="封面：" class="is-required">
      <el-upload
        class="avatar-uploader"
        accept="image/jpeg, image/png"
        action="/gw/admin/rest/materiel/uploadMaterielPicture?maxSize=0"
        :headers="headerRamToken"
        :show-file-list="false"
        :on-success="handleUploadImg"
        :before-upload="beforeImgUpload"
      >
        <img v-if="formData.coverUrl" :src="formData.coverUrl" class="img" />
        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
      </el-upload>
    </el-form-item>
    <el-form-item label="文章标题：" class="is-required">
      <el-input v-model.trim="formData.title" placeholder="请输入文章标题" style="width: 300px"></el-input>
    </el-form-item>
    <el-form-item label="公众号链接：" class="is-required" v-show="formData.type === 2">
      <el-input v-model.trim="formData.officialAccountUrl" placeholder="请输入公众号链接"></el-input>
    </el-form-item>
    <el-form-item label="内容简述：" class="is-required" v-show="formData.type === 1">
      <div>
        <quillEditor v-model="formData.content"></quillEditor>
      </div>
    </el-form-item>
    <el-form-item style="margin-top: 100px;">
      <el-button @click="$router.push('/infoMatchPlatform/article')">返 回</el-button>
      <el-button type="primary" @click="saveArticleFn()">保 存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import QuillEditor from '@/components/quillEditor/quillEditor'
import {getAllDirectory} from '@/api/infoMatchPlatform/topic'
import {saveArticle, getAllTopic, getAllLabels, getArticleInfo} from "@/api/infoMatchPlatform/article";
import { headerRamToken } from '@/utils/menu'

export default {
  name: "createEdit",
  components: {
    QuillEditor
  },
  data() {
    return {
      headerRamToken,
      formData: {
        type: 1, // 1 自主撰写，2 跳转公众号
        directory: {lyyDirectoryId: null}, // 一级栏目
        subDirectory: [], // 同步其他栏目
        topic: {lyyTopicId:null}, // 专题
        labelList: [], // 标签
        isTop: 0, // 是否置顶， 0否，1是
        coverUrl: '', // 封面链接
        officialAccountUrl: '', // 公众号文章链接
        title: '', // 标题
        content: '', // 内容
      },
      allDirectory: [], // 所有栏目
      allTopic: [], // 所有专题
      allLabel: [], // 所有标签
      otherDirectory: [], // 其他栏目
      selectLable: [], // 选中标签
      selectOtherDirectory: [], // 选中其他栏目
    };
  },
  mounted() {
    this.getAllDirectoryFn();
    this.getAllLabels();
  },
  methods: {
    // 上传图片
    handleUploadImg(res) {
      if (res.result === 1) {
        this.formData.coverUrl =
          "https://oss.lyypublic.leyaoyao.com/" + res.para.split(";")[0];
      } else {
        this.$message.error("上传失败");
        this.formData.coverUrl = "";
      }
    },
    // 图片限制
    beforeImgUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error("请选择2M以内的图片");
      }
      return isLt2M;
    },
    // 查询文章
    async getArticleInfoFn() {
      const res = await getArticleInfo({id: this.$route.query.lyyArticleId});
      if (res.result === 0) {
        res.data.topic = {lyyTopicId: res.data.topic? res.data.topic.lyyTopicId : null}
        this.formData = res.data;
        this.handleChangeDirectory(true);
        this.selectOtherDirectory = this.formData.subDirectory && this.formData.subDirectory.length && this.formData.subDirectory.map(item => {
          return item.lyyDirectoryId
        });
        this.selectLable = this.formData.labelList && this.formData.labelList.length && this.formData.labelList.map(item => {
          return item.lyyDirectoryId
        });
      }
    },
    // 查询所有分类
    async getAllDirectoryFn() {
      const res = await getAllDirectory();
      if (res.result === 0) {
        this.allDirectory = res.data;
        if (this.$route.query && this.$route.query.lyyArticleId) { // 编辑文章获取数据
          this.getArticleInfoFn();
        }
      }
    },
    // 查询所有专题
    async getAllTopicFn() {
      const res = await getAllTopic({directoryId: this.formData.directory.lyyDirectoryId});
      if (res.result === 0) {
        this.allTopic = res.data;
      }
    },
    // 查询所有专题
    async getAllLabels() {
      const res = await getAllLabels();
      if (res.result === 0) {
        this.allLabel = res.data;
      }
    },
    // 选中栏目
    handleChangeDirectory(isInit) {
      this.getAllTopicFn();
      let arr = this.allDirectory.filter(item => {
        return item.lyyDirectoryId !== this.formData.directory.lyyDirectoryId;
      })
      this.otherDirectory = arr;
      this.selectOtherDirectory = []; // 选择一级栏目清空已选择的其他栏目
      if (isInit !== true) { this.formData.topic.lyyTopicId = null;}
    },
    // 选中标签
    handleChangeLabel(v) {
      let arr = [];
      v && v.forEach(item => {
        arr.push({ lyyDirectoryId: item});
      })
      this.formData.labelList = arr;
    },
    // 选中其他栏目
    handleChangeOtherDirectory(v) {
      let arr = [];
      v && v.forEach(item => {
        arr.push({ lyyDirectoryId: item});
      })
      this.formData.subDirectory = arr;
    },
    // 选择编写类型
    handleChangeType() {
      if (this.formData.type === 1) { // 自主撰写
        this.formData.content = '';
      } else { // 跳转公众号
        this.formData.officialAccountUrl = '';
      }
    },
    // 验证规则
    validateRule() {
      if (!this.formData.directory.lyyDirectoryId) {
        this.$message.error('请选择一级栏目！');
        return false;
      }
      if (!this.formData.coverUrl) {
        this.$message.error('请上传封面图！');
        return false;
      }
      if (!this.formData.title) {
        this.$message.error('请输入文章标题！');
        return false;
      }
      if (this.formData.type === 1) { // 自主撰写
        if (!this.formData.content) {
          this.$message.error('请输入内容简述！');
          return false;
        }
      } else { // 跳转公众号
        if (!this.formData.officialAccountUrl) {
          this.$message.error('请输入公众号链接！');
          return false;
        }
      }
      return true;
    },
    // 保存信息
    async saveArticleFn() {
      if (this.validateRule()) {
        const res = await saveArticle(this.formData);
        if (res.result === 0) {
          this.$message.success(`${this.formData.lyyArticleId? '修改成功' : '创建成功！'}`);
          this.visible = false;
          this.$router.push('/infoMatchPlatform/article');
        }
      }
    },
    // 重置表单信息
    resetFormData() {
      this.formData = {
        type: 1, // 1 自主撰写，2 跳转公众号
        directory: {lyyDirectoryId: null}, // 一级栏目
        subDirectory: [], // 同步其他栏目
        topic: {lyyTopicId:null}, // 专题
        labelList: [], // 标签
        isTop: 0, // 是否置顶， 0否，1是
        coverUrl: '', // 封面链接
        officialAccountUrl: '', // 公众号文章链接
        title: '', // 标题
        content: '', // 内容
      }
    },
  },
}
</script>

<style lang=less scoped>
.avatar-uploader {
  width: 120px;
  height: 120px;
  display: inline-block;
  margin-right: 20px;
  .avatar-uploader-icon {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
    &:hover{
      border-color: #409eff;
    }
  }
  .img {
    display: block;
    width: 100%;
    height: 100%;
  }
}
</style>
