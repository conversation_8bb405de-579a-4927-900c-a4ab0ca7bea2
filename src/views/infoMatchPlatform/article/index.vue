<!--
 * @Description: 撰写文章
 * @Author: wutao
 * @Date: 2022/5/19 17:24
 * @LastEditors: wutao
 * @LastEditTime: 2022/5/19 17:24
 -->
<template>
<div>
  <el-form :inline="true">
    <el-form-item label="栏目：">
      <el-select v-model="searchData.lyyDirectoryId" placeholder="请选择类型" clearable @change="handleChangeDirectory">
        <el-option
          v-for="item in allDirectory"
          :key="item.lyyDirectoryId"
          :label="item.name"
          :value="item.lyyDirectoryId">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="专题：">
      <el-select v-model="searchData.lyyTopicId" placeholder="请选择专题" clearable>
        <el-option
          v-for="item in allTopic"
          :key="item.lyyTopicId"
          :label="item.name"
          :value="item.lyyTopicId">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="文章标题：">
      <el-input v-model="searchData.title" placeholder="请输入文章标题" clearable class="width200"></el-input>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="getListArticleFn(true)">搜索</el-button>
      <el-button type="success" @click="$router.push('/infoMatchPlatform/createEditArticle')">创建</el-button>
    </el-form-item>
  </el-form>
  <el-table
    :data="tableData"
    border
    style="width: 100%"
    height="600"
    v-loading.lock="LoadingFlag"
  >
    <el-table-column
      prop="title"
      label="文字标题"
    >
    </el-table-column>
    <el-table-column
      prop="directory.name"
      label="栏目（主）"
    >
    </el-table-column>
    <el-table-column
      prop="subDirectory"
      label="同步至其他栏目"
    >
      <template slot-scope="scope">
        <el-tag v-for="(item, index) in scope.row.subDirectory" :key="index" type="info" style="margin: 5px">{{item.name}}</el-tag>
      </template>
    </el-table-column>
    <el-table-column
      prop="lyyEquipmentTypeId"
      label="文章标签"
    >
      <template slot-scope="scope">
        <el-tag v-for="(item, index) in scope.row.labelList" :key="index" style="margin: 5px">#{{item.name}}</el-tag>
      </template>
    </el-table-column>
    <el-table-column
      prop="isTop"
      label="置顶"
    >
      <template slot-scope="scope">
        <p>{{scope.row.isTop? '是' : '否'}}</p>
      </template>
    </el-table-column>
    <el-table-column
      prop="topic.name"
      label="归入专题（栏目-专题名称）"
    >
      <template slot-scope="scope" v-if="scope.row.topic">
        <p>{{scope.row.directory.name}}-{{scope.row.topic.name}}</p>
      </template>
    </el-table-column>
    <el-table-column
      prop="created"
      label="创建时间"
    >
    </el-table-column>
    <el-table-column
      label="操作"
      width="400"
    >
      <template slot-scope="scope">
        <el-button @click="$router.push({path: '/infoMatchPlatform/createEditArticle',query: { lyyArticleId: scope.row.lyyArticleId} })" type="primary" size="small" plain>编辑</el-button>
        <el-button @click="delArticleFn(scope.row.lyyArticleId)" type="danger" size="small" plain>删除</el-button>
        <el-button @click="adjustArticleSeqFn(scope.row.lyyArticleId, tableData[scope.$index-1].lyyArticleId, true)" type="success" size="small" plain :disabled="scope.$index === 0">上移</el-button>
        <el-button @click="adjustArticleSeqFn(scope.row.lyyArticleId, tableData[scope.$index+1].lyyArticleId, false)" type="success" size="small" plain :disabled="scope.$index + 1 === tableData.length">下移</el-button>
        <el-button @click="topArticleFn(scope.row)" type="success" size="small" plain>置顶</el-button>
      </template>
    </el-table-column>
  </el-table>
  <div class="pagination margin-top-10">
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageNum"
      :page-sizes="[10, 20, 50]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
</div>
</template>

<script>
import {
  getListArticle,
  delArticle,
  adjustArticleSeq,
  topArticle,
  getAllTopic,
} from '@/api/infoMatchPlatform/article'
import {getAllDirectory} from '@/api/infoMatchPlatform/topic'

export default {
  name: "index",
  data() {
    return {
      LoadingFlag: false,
      total: 0,//总数
      pageNum: 1,//当前页码
      pageSize: 10,//每页大小
      tableData: [],
      searchData: {
        lyyDirectoryId: null, // 栏目id
        lyyTopicId: null, // 专题id
        title: '', // 标题
      },
      allDirectory: [], // 所有栏目
      allTopic: [], // 所有专题
    };
  },
  mounted() {
    this.getAllDirectoryFn();
    if (this.$route.query && this.$route.query.lyyTopicId && this.$route.query.lyyDirectoryId) {
      this.searchData.lyyTopicId = Number(this.$route.query.lyyTopicId);
      this.searchData.lyyDirectoryId = Number(this.$route.query.lyyDirectoryId);
      this.handleChangeDirectory();
    }
    this.getListArticleFn(true);
  },
  methods: {
    // 分页选择
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getListArticleFn();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getListArticleFn();
    },
    // 查询所有分类
    async getAllDirectoryFn() {
      const res = await getAllDirectory();
      if (res.result === 0) {
        this.allDirectory = res.data;
      }
    },
    // 查询所有专题
    async getAllTopicFn() {
      const res = await getAllTopic({directoryId: this.searchData.lyyDirectoryId});
      if (res.result === 0) {
        this.allTopic = res.data;
      }
    },
    // 选中栏目
    handleChangeDirectory() {
      this.getAllTopicFn();
      this.searchData.lyyTopicId = null;
    },
    // 查询目录列表
    async getListArticleFn(isInit) {
      if (isInit) { this.pageIndex = 1}; //初始化页数
      if (this.searchData.lyyTopicId && !this.searchData.lyyDirectoryId) {
        this.$message.error('请先选择栏目！');
        return;
      }
      let params = {
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        ...this.searchData
      };
      this.LoadingFlag = true;
      const res = await getListArticle(params);
      this.LoadingFlag = false;
      if (res.result === 0 && res.data) {
        this.total = res.data.total;
        this.tableData = res.data.records;
      }
    },
    // 删除
    delArticleFn(id) {
      this.$confirm('确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await delArticle([id]);
        if (res && res.result == 0) {
          this.$message.success('删除成功！');
          this.getListArticleFn(true);
        } else {
          this.$message.error(res.description);
        }
      }).catch(() => {});
    },
    // 上下移动
    async adjustArticleSeqFn(sourceId,targetId,up) {
      let params = {
        sourceId,
        targetId,
        up,
      }
      const res = await adjustArticleSeq(params);
      if (res.result === 0) {
        this.getListArticleFn();
      }
    },
    // 置顶
    async topArticleFn(info) {
      let params = {
        ids: [info.lyyArticleId],
        isTop: 1,
      }
      const res = await topArticle(params);
      if (res.result === 0) {
        this.getListArticleFn(true);
      }
    },
  },
}
</script>

<style scoped>

</style>
