<!--
日期：2020/04/2
功能：生产商自有支付渠道
作者：夏龙旭
-->
<template>
  <div v-loading="loading">
    <el-form :inline="true" label-position="right">
      <el-form-item label="生产商（代理）名称：">
        <el-input
          class="inline-input"
          v-model.trim="listParams.agentName"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="生产商（代理）账号：">
        <el-input
          class="inline-input"
          v-model.trim="listParams.agentUserLogin"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="合同编号：">
        <el-input
          class="inline-input"
          v-model.trim="listParams.contractNo"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="生产商APPID：">
        <el-input class="inline-input" v-model.trim="listParams.appId" placeholder="请输入"
                  clearable></el-input>
      </el-form-item>
      <el-form-item label="收款账号类型：">
        <el-select v-model="listParams.payChannel">
          <el-option
            v-for="(item,idx) in aPayChannel"
            :key="idx + 'PayChannel'"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!--按钮-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList(true)">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="list"
      border
      highlight-current-row
      style="width: 100%;margin-bottom: 20px;"
    >
      <el-table-column label="客户类型" align="center">
        <template slot-scope="scope">
          <span>{{mapCode[scope.row.agentUserType]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="生产商名称" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.agentName}}</span>
        </template>
      </el-table-column>
      <el-table-column label="生产商账号" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.agentLoginName}}</span>
        </template>
      </el-table-column>
      <el-table-column label="合同编号" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.contractNo}}</span>
        </template>
      </el-table-column>
      <el-table-column label="收款账号类型" align="center">
        <template slot-scope="scope">
          <span>{{mapPayChannel[scope.row.payChannel]}}</span>
        </template>
      </el-table-column>
      <el-table-column label="APPID" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.appId}}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.created}}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.updated}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <span>
            <el-button @click="showDetail(scope.row,false)" type="text">详情</el-button>
            <el-button @click="showDetail(scope.row,true)" type="text">编辑</el-button>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination margin-top-10">
      <el-pagination
        background
        @current-change="handleCurrentChange"
        :current-page="listParams.pageIndex"
        :page-sizes="[50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
    <!-- 弹窗 -->
    <div>
      <el-dialog :title="isEdit?'账户编辑':'账户详情'" :visible.sync="dialogVisible" width="500px">
        <div class="dialogContent" v-if="detail">
          <div class="bdw">
            <p class="title">公众号信息</p>
            <div class="items">
              <div class="item">
                <span>平台APPID:</span>
                {{detail.appid}}
              </div>
              <div class="item">
                <span>商户号:</span>
                {{detail.merchantNum}}
              </div>
            </div>
            <div class="items">
              <div class="item">
                <span class="w100">公众号APPID:</span>
                {{detail.appid}}
              </div>
              <div class="item">
                <span class="w100">API秘钥:</span>
                {{detail.merchantSecret}}
              </div>
            </div>
          </div>
          <div>
            <p class="title">
              乐摇摇平台手续费：
              <el-input
                type="text"
                :disabled="!isEdit"
                class="percentInput"
                v-model="detail.factoryFeeRate"
              />
              %
            </p>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button v-if="isEdit" @click="save" :loading="saveing" type="primary">保 存</el-button>
          <el-button v-else type="primary" @click="dialogVisible = false">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
  import {
    paymentGetPage,
    paymentGetDetails,
    paymentGetChannel,
    factoryFeeRate
  } from "@/api/merchantPayChannel";

  export default {
    name: "merchantPayChannel",
    data() {
      return {
        formData: "",
        isEdit: false,
        total: 0,
        listParams: {
          pageIndex: 1,
          pageSize: 50,
          agentName: "",
          agentUserLogin: "",
          payChannel: "",
          contractNo: "",
          appId: ""
        },
        mapPayChannel: {
          WechatPay: "微信",
          AliPay: "支付宝",
          WechatMiniPay: "微信小程序"
        },
        aPayChannel: [
          {
            label: "全部",
            value: ""
          },
          {
            label: "微信公众号",
            value: "WechatPay"
          },
          {
            label: "支付宝生活号",
            value: "AliPay"
          },
          {
            label: "微信小程序",
            value: "WechatMiniPay"
          }
        ],
        mapCode: {
          1: '一级代理',
          2: '二级代理'
        },
        list: [],
        detail: null,
        dialogVisible: false,
        loading: false,
        saveing: false //保存费率
      };
    },
    created() {
      this.getList();
    },
    methods: {
      getList(isSeach) {
        this.loading = true;
        if (isSeach) {
          this.listParams.pageIndex = 1;
        }
        paymentGetPage(this.listParams).then(res => {
          this.loading = false;
          if (res.result === 0) {
            const json = res.data;
            this.list = json.items || [];
            this.total = json.total;
          }
        }).catch(_ => {
          this.loading = false;
        })
      },
      //保存费率
      save() {
        this.saveing = true;
        factoryFeeRate({
          factoryFeeRate: this.detail.factoryFeeRate,
          factoryDefaultFeeId: this.detail.factoryDefaultFeeId
        }).then(res => {
          this.saveing = false;
          if (res && res.result == "0") {
            this.$message({
              type: "success",
              message: "保存成功"
            });
            this.dialogVisible = false;
            this.getList();
          }
        });
      },
      handleCurrentChange(pageIndex) {
        this.listParams.pageIndex = pageIndex;
        this.getList();
      },
      showDetail(item, isEdit) {
        this.isEdit = isEdit;
        paymentGetDetails({ appConfigId: item.appConfigId }).then(res => {
          if (res.result === 0) {
            this.detail = res.data;
            this.dialogVisible = true;
          }
        });
      }
    }
  };
</script>
<style scoped>
  .dialogContent {
    font-size: 14px;
    color: #333;
  }

  .percentInput {
    width: 60px;
    margin-right: 10px;
  }

  .bdw {
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 20px;
  }

  .title {
    padding-bottom: 20px;
  }

  .items {
    display: flex;
    justify-content: space-between;
    color: #909399;
    flex-wrap: wrap;
  }

  .items span {
    padding-right: 10px;
  }

  .items .item {
    display: flex;
    padding-bottom: 10px;
  }

  .items span.w100 {
    width: 100px;
    text-align: right;
    flex-shrink: 0;
  }

  .pd-b-10 {
    padding-bottom: 10px;
  }
</style>
