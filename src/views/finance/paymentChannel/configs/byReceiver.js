export default [
  {
    "compName": "lyy-container",
    "compId": "8e709a6a-d525-4ef2-bd87-7e3610b10991",
    "compNameCn": "容器",
    "prop": {},
    "actions": [],
    "childrens": [
      {
        "compName": "lyy-container",
        "compId": "de7cae91-2465-48ca-941e-0cf40c4439ea",
        "compNameCn": "增删查改",
        "prop": {},
        "actions": [],
        "childrens": [
          {
            "compName": "lyy-form",
            "compId": "57da7d7b-f8ca-493e-a31b-210a9b0e8443",
            "compNameCn": "表单",
            "style": {},
            "prop": {
              "form": {
                "pageIndex": 1,
                "pageSize": 10,
                "startTime": "2023-02-07",
                "endTime": "2023-02-07",
                "current": 1,
                "size": 10
              },
              "aliasName": "表单",
              "isSignChange": null,
              "mainLayout": false,
              "labelCol": {
                "style": {
                  "width": "100px"
                }
              },
              "class": ""
            },
            "modelValue": {
              "pageIndex": 1,
              "pageSize": 10,
              "startTime": "2023-02-07",
              "endTime": "2023-02-07",
              "current": 1,
              "size": 10
            },
            "actions": [
              {
                "event": "mounted",
                "action": "request",
                "option": {
                  "url": "/finance/payChannel/withdraw/channel/idCard/pageList",
                  "method": "post",
                  "payloads": {
                    "type": "dynamic",
                    "static": "",
                    "dynamic": {
                      "nodePath": "表单-57da7d7b-f8ca-493e-a31b-210a9b0e8443.modelValue"
                    }
                  },
                  "responseDataKey": "byReceiverList",
                  "customOption": {
                    "loading": true,
                    "nextEventsDelay": 500,
                    "parse": false
                  },
                  "headerPayloads": [],
                  "interceptors": {
                    "requestInterceptor": "",
                    "responseInterceptor": ""
                  }
                },
                "thenActions": [
                  {
                    "event": "mounted",
                    "action": "setNewValue",
                    "option": {
                      "from": {
                        "dynamic": {
                          "nodePath": "byReceiverList.list"
                        }
                      },
                      "to": {
                        "type": "dynamic",
                        "dynamic": {
                          "nodePath": "按收款人表格-7412633d-bb36-4654-809b-f91d55d01c51.modelValue.datasource"
                        }
                      }
                    },
                    "thenActions": [
                      {
                        "event": "mounted",
                        "action": "setNewValue",
                        "option": {
                          "from": {
                            "dynamic": {
                              "nodePath": "byReceiverList.total"
                            }
                          },
                          "to": {
                            "type": "dynamic",
                            "dynamic": {
                              "nodePath": "按收款人分页-e6c13341-53fa-4bee-962c-d711713ce219.prop.total"
                            }
                          }
                        },
                        "thenActions": []
                      }
                    ]
                  }
                ]
              }
            ],
            "childrens": [
              {
                "compName": "lyy-search-pro",
                "compId": "06fa1c86-8a41-4a3f-b7b0-1cc7833b1db4",
                "compNameCn": "搜索容器",
                "prop": {
                  "searchFormList": [
                    {
                      "compName": "lyy-text-input",
                      "compId": "c54cc907-88f6-4eae-b23c-a22349f76d5d",
                      "compNameCn": "输入框",
                      "style": {},
                      "prop": {
                        "field": "idCode",
                        "label": "身份证号",
                        "aliasName": "文本输入框",
                        "placeholder": "请输入",
                        "defaultValue": "",
                        "validateTrigger": "change",
                        "maxlength": null,
                        "size": "default",
                        "suffix": "",
                        "addonAfter": "",
                        "rules": [
                          {
                            "required": false,
                            "message": "",
                            "trigger": "",
                            "pattern": ""
                          }
                        ],
                        "disabled": false,
                        "showCount": false,
                        "allowClear": true,
                        "labelCol": {
                          "style": {}
                        },
                        "icon": {
                          "iconName": "",
                          "popover": {
                            "description": []
                          }
                        },
                        "formId": "",
                        "show": {
                          "exp": ""
                        },
                        "forbid": {
                          "exp": ""
                        }
                      },
                      "modelValue": "",
                      "actions": []
                    },
                    {
                      "compName": "lyy-text-input",
                      "compId": "921ec838-e83c-4399-95d6-e4a11acd9f1e",
                      "compNameCn": "输入框",
                      "style": {},
                      "prop": {
                        "field": "name",
                        "label": "姓名",
                        "aliasName": "文本输入框",
                        "placeholder": "请输入",
                        "defaultValue": "",
                        "validateTrigger": "change",
                        "maxlength": null,
                        "size": "default",
                        "suffix": "",
                        "addonAfter": "",
                        "rules": [
                          {
                            "required": false,
                            "message": "",
                            "trigger": "",
                            "pattern": ""
                          }
                        ],
                        "disabled": false,
                        "showCount": false,
                        "allowClear": true,
                        "labelCol": {
                          "style": {}
                        },
                        "icon": {
                          "iconName": "",
                          "popover": {
                            "description": []
                          }
                        },
                        "formId": "",
                        "show": {
                          "exp": ""
                        },
                        "forbid": {
                          "exp": ""
                        }
                      },
                      "modelValue": "",
                      "actions": []
                    },
                    {
                      "compName": "lyy-select",
                      "compId": "965d57aa-2f84-4053-8fbf-20f3405b8d44",
                      "compNameCn": "下拉框",
                      "prop": {
                        "field": "idCodeType",
                        "label": "证件类型",
                        "aliasName": "下拉框",
                        "placeholder": "请选择",
                        "mode": "combobox",
                        "defaultValue": {
                          "source": "",
                          "sourceKey": ""
                        },
                        "rules": [
                          {
                            "required": false,
                            "message": "",
                            "trigger": ""
                          }
                        ],
                        "options": [
                          {
                            "label": "大陆身份证",
                            "value": 1,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "label": "护照",
                            "value": 2,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "label": "香港居民来往内地通行证",
                            "value": 3,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "label": "澳门居民来往内地通行证",
                            "value": 4,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "label": "台湾居民来往内地通行证",
                            "value": 5,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "label": "中华人民共和国港澳居民居住证",
                            "value": 6,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "label": "中华人民共和国台湾居民居住证",
                            "value": 7,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "label": "外国人永久居留身份证",
                            "value": 8,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "label": "中华人民共和国外国人就业许可证书",
                            "value": 9,
                            "disabled": false,
                            "show": true
                          }
                        ],
                        "fieldNames": {
                          "label": "label",
                          "value": "value"
                        },
                        "size": "default",
                        "immediate": true,
                        "allowClear": true,
                        "readonly": false,
                        "showSearch": true,
                        "optionFilterProp": "",
                        "disabled": false,
                        "lastComp": {
                          "text": "",
                          "icon": ""
                        },
                        "labelCol": {
                          "style": {}
                        },
                        "icon": {
                          "iconName": "",
                          "popover": {
                            "description": []
                          }
                        },
                        "formId": "",
                        "show": {
                          "exp": ""
                        },
                        "forbid": {
                          "exp": ""
                        }
                      },
                      "actions": []
                    },
                    {
                      "compName": "lyy-select",
                      "compId": "e065fc73-70a1-4e61-854e-49bdca1a5fa0",
                      "compNameCn": "下拉框",
                      "prop": {
                        "field": "payChannelName",
                        "label": "指定渠道",
                        "aliasName": "下拉框",
                        "placeholder": "请选择",
                        "mode": "combobox",
                        "defaultValue": {
                          "source": "",
                          "sourceKey": ""
                        },
                        "rules": [
                          {
                            "required": false,
                            "message": "",
                            "trigger": ""
                          }
                        ],
                        "options": [
                          {
                            "payChannelId": 100501,
                            "channelName": "云账户",
                            "channelType": "enterprise",
                            "isActive": "N",
                            "remark": "天津云账户",
                            "created": "2022-11-11 16:21:20",
                            "businessType": 2,
                            "clientType": 3,
                            "monthlyLimit": 3,
                            "priority": -11,
                            "mchAppId": "27596956",
                            "mchId": "27596956",
                            "cert": null,
                            "payRemark": null,
                            "partnerKey": null,
                            "isSign": true,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 100000,
                            "channelName": "life",
                            "channelType": "enterprise",
                            "isActive": "Y",
                            "remark": "",
                            "created": "2020-03-24 17:53:12",
                            "businessType": 0,
                            "clientType": 1,
                            "monthlyLimit": 200,
                            "priority": -10,
                            "mchAppId": "wxe3b203d57e31f7d5",
                            "mchId": "1361650402",
                            "cert": "/root/cert/1361650402_apiclient_cert.p12",
                            "payRemark": "乐摇摇提现到账",
                            "partnerKey": "CC2109e4bD8f35228Iaidc657ef3ffd4",
                            "isSign": false,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 100601,
                            "channelName": "星云开物兼职猫",
                            "channelType": "enterprise",
                            "isActive": "Y",
                            "remark": "九尾科技-兼职猫",
                            "created": "2022-11-11 16:21:20",
                            "businessType": 2,
                            "clientType": 4,
                            "monthlyLimit": 0,
                            "priority": -9,
                            "mchAppId": "681",
                            "mchId": "681",
                            "cert": null,
                            "payRemark": null,
                            "partnerKey": null,
                            "isSign": true,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 100200,
                            "channelName": "好灵工",
                            "channelType": "enterprise",
                            "isActive": "Y",
                            "remark": "灵鹊云-好灵工",
                            "created": "2022-05-16 13:54:47",
                            "businessType": 0,
                            "clientType": 2,
                            "monthlyLimit": 1,
                            "priority": -2,
                            "mchAppId": "102207061089810",
                            "mchId": "102207061089810",
                            "cert": null,
                            "payRemark": null,
                            "partnerKey": null,
                            "isSign": true,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 100300,
                            "channelName": "好灵工2",
                            "channelType": "enterprise",
                            "isActive": "Y",
                            "remark": "灵鹊云-好灵工",
                            "created": "2022-05-16 13:54:47",
                            "businessType": 2,
                            "clientType": 2,
                            "monthlyLimit": 100,
                            "priority": -1,
                            "mchAppId": "10210825634510",
                            "mchId": "10210825634510",
                            "cert": null,
                            "payRemark": null,
                            "partnerKey": null,
                            "isSign": true,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 10000011,
                            "channelName": "life3",
                            "channelType": "enterprise",
                            "isActive": "N",
                            "remark": "",
                            "created": "2020-03-24 17:53:12",
                            "businessType": 0,
                            "clientType": 1,
                            "monthlyLimit": 200,
                            "priority": 0,
                            "mchAppId": "wxe3b203d57e31f7d5",
                            "mchId": "13616504021111",
                            "cert": "/root/cert/1361650402_apiclient_cert.p12",
                            "payRemark": "乐摇摇提现到账",
                            "partnerKey": "CC2109e4bD8f35228Iaidc657ef3ffd4",
                            "isSign": false,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 100702,
                            "channelName": "乐联盟云账户9715",
                            "channelType": "enterprise",
                            "isActive": "Y",
                            "remark": "天津云账户",
                            "created": "2022-11-23 13:53:36",
                            "businessType": 2,
                            "clientType": 3,
                            "monthlyLimit": 0,
                            "priority": 0,
                            "mchAppId": "04349715",
                            "mchId": "04349715",
                            "cert": null,
                            "payRemark": null,
                            "partnerKey": null,
                            "isSign": true,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 100700,
                            "channelName": "星云开物云账户2062",
                            "channelType": "enterprise",
                            "isActive": "Y",
                            "remark": "天津云账户",
                            "created": "2022-11-23 13:53:36",
                            "businessType": 2,
                            "clientType": 3,
                            "monthlyLimit": 0,
                            "priority": 0,
                            "mchAppId": "02012062",
                            "mchId": "02012062",
                            "cert": null,
                            "payRemark": null,
                            "partnerKey": null,
                            "isSign": true,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 100701,
                            "channelName": "广分云账户5091",
                            "channelType": "enterprise",
                            "isActive": "Y",
                            "remark": "天津云账户",
                            "created": "2022-11-23 13:53:36",
                            "businessType": 2,
                            "clientType": 3,
                            "monthlyLimit": 0,
                            "priority": 0,
                            "mchAppId": "09645091",
                            "mchId": "09645091",
                            "cert": null,
                            "payRemark": null,
                            "partnerKey": null,
                            "isSign": true,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 100900,
                            "channelName": "薪宝科技76663949",
                            "channelType": "enterprise",
                            "isActive": "Y",
                            "remark": "薪宝科技",
                            "created": "2022-12-27 16:26:38",
                            "businessType": 2,
                            "clientType": 5,
                            "monthlyLimit": 0,
                            "priority": 0,
                            "mchAppId": "76663949",
                            "mchId": "76663949",
                            "cert": null,
                            "payRemark": null,
                            "partnerKey": null,
                            "isSign": true,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 101000,
                            "channelName": "薪宝科技（星云开物-生产-安徽）",
                            "channelType": "enterprise",
                            "isActive": "Y",
                            "remark": "薪宝科技",
                            "created": "2023-01-03 10:39:32",
                            "businessType": 2,
                            "clientType": 5,
                            "monthlyLimit": 1,
                            "priority": 0,
                            "mchAppId": "46451295",
                            "mchId": "46451295",
                            "cert": null,
                            "payRemark": null,
                            "partnerKey": null,
                            "isSign": true,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 100100,
                            "channelName": "life2",
                            "channelType": "enterprise",
                            "isActive": "N",
                            "remark": "1",
                            "created": "2020-04-29 10:51:08",
                            "businessType": 2,
                            "clientType": 1,
                            "monthlyLimit": 1,
                            "priority": 1,
                            "mchAppId": "wxe3b203d57e31f7d5",
                            "mchId": "1361650402",
                            "cert": "/root/cert/1361650402_apiclient_cert.p12",
                            "payRemark": "乐摇摇提现到账",
                            "partnerKey": "CC2109e4bD8f35228Iaidc657ef3ffd4",
                            "isSign": false,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          },
                          {
                            "payChannelId": 100400,
                            "channelName": "好灵工3",
                            "channelType": "enterprise",
                            "isActive": "Y",
                            "remark": "灵鹊云-好灵工",
                            "created": "2022-05-26 18:05:20",
                            "businessType": 1,
                            "clientType": 2,
                            "monthlyLimit": 100,
                            "priority": 3,
                            "mchAppId": "10210825634510test3",
                            "mchId": "10210825634510",
                            "cert": null,
                            "payRemark": null,
                            "partnerKey": null,
                            "isSign": true,
                            "pageIndex": null,
                            "pageSize": null,
                            "disabled": false,
                            "show": true
                          }
                        ],
                        "fieldNames": {
                          "label": "channelName",
                          "value": "channelName"
                        },
                        "size": "default",
                        "immediate": false,
                        "allowClear": true,
                        "readonly": false,
                        "showSearch": false,
                        "optionFilterProp": "",
                        "disabled": false,
                        "lastComp": {
                          "text": "",
                          "icon": ""
                        },
                        "labelCol": {
                          "style": {}
                        },
                        "icon": {
                          "iconName": "",
                          "popover": {
                            "description": []
                          }
                        },
                        "formId": "",
                        "show": {
                          "exp": ""
                        },
                        "forbid": {
                          "exp": ""
                        }
                      },
                      "actions": [
                        {
                          "event": "mounted",
                          "action": "request",
                          "option": {
                            "url": "/finance/payChannel/list",
                            "method": "post",
                            "payloads": {
                              "type": "higher",
                              "static": "",
                              "dynamic": {
                                "nodePath": "payChannelList.items"
                              },
                              "higher": [
                                {
                                  "key": "pageIndex",
                                  "value": "1",
                                  "id": "4387a8e6-0958-4c78-9ba6-5c5871c2e8a0"
                                },
                                {
                                  "key": "pageSize",
                                  "value": "999",
                                  "id": "0099d8c9-cf3e-4802-b0e7-f46f6af0701a"
                                }
                              ]
                            },
                            "responseDataKey": "payChannelList",
                            "customOption": {
                              "loading": true,
                              "nextEventsDelay": 500,
                              "parse": false,
                              "codeValue": 0,
                              "codeKey": "result",
                              "messageKey": "description",
                              "jsonPath": "data"
                            },
                            "headerPayloads": [],
                            "interceptors": {
                              "requestInterceptor": "",
                              "responseInterceptor": ""
                            }
                          },
                          "thenActions": [
                            {
                              "event": "mounted",
                              "action": "setNewValue",
                              "option": {
                                "to": {
                                  "type": "dynamic",
                                  "dynamic": {
                                    "nodePath": "下拉框-e065fc73-70a1-4e61-854e-49bdca1a5fa0.prop.options"
                                  }
                                },
                                "from": {
                                  "dynamic": {
                                    "nodePath": "payChannelList.items"
                                  }
                                }
                              },
                              "thenActions": []
                            }
                          ]
                        }
                      ]
                    },
                    {
                      "compName": "lyy-date-range",
                      "compId": "d8e18a09-5f8d-4a5a-9c8f-cd19ae3172f8",
                      "compNameCn": "日期范围",
                      "style": {},
                      "prop": {
                        "fields": [
                          "startTime",
                          "endTime"
                        ],
                        "label": "更新日期",
                        "aliasName": "日期",
                        "placeholder": [
                          "开始日期",
                          "结束日期"
                        ],
                        "defaultValue": "今天",
                        "picker": "date",
                        "valueFormat": "YYYY-MM-DD",
                        "rules": [
                          {
                            "required": false,
                            "message": "",
                            "trigger": "",
                            "pattern": ""
                          }
                        ],
                        "disabledOption": {
                          "unit": "year",
                          "duration": 2
                        },
                        "fastOptions": [],
                        "validateTrigger": "",
                        "showNow": false,
                        "showToday": false,
                        "showTime": false,
                        "allowClear": false,
                        "size": "default",
                        "disabled": false,
                        "labelCol": {
                          "style": {}
                        },
                        "icon": {
                          "iconName": "",
                          "popover": {
                            "description": []
                          }
                        },
                        "show": {
                          "exp": ""
                        },
                        "forbid": {
                          "exp": "",
                          "payloads": [
                            {
                              "key": "",
                              "source": "",
                              "sourceKey": "",
                              "decompose": false,
                              "value": ""
                            }
                          ]
                        },
                        "field": ""
                      },
                      "actions": [],
                      "modelValue": [
                        "2023-02-07",
                        "2023-02-07"
                      ]
                    }
                  ],
                  "aliasName": "搜索容器",
                  "formStorage": false
                },
                "actions": []
              },
              {
                "compName": "lyy-card",
                "compId": "3efded62-b465-4186-8b9f-f30c28004657",
                "compNameCn": "卡片",
                "prop": {
                  "showHeader": false,
                  "title": "卡片容器",
                  "aliasName": "卡片容器",
                  "extraList": [],
                  "mainLayout": false,
                  "bordered": true
                },
                "actions": [],
                "childrens": [
                  {
                    "compName": "lyy-toolbar",
                    "compId": "c17bf138-a5a8-4b5c-9983-732cb0a8b6f0",
                    "compNameCn": "工具栏",
                    "style": {},
                    "prop": {
                      "title": "",
                      "aliasName": "工具栏"
                    },
                    "childrens": [
                      {
                        "compName": "lyy-button",
                        "compId": "a42fc3cf-3641-4448-8095-d2f769f1222c",
                        "compNameCn": "按钮",
                        "actions": [
                          {
                            "event": "click",
                            "action": "linkto",
                            "option": {
                              "url": "/PaymentChannelIndex/ChangeRecords",
                              "go": 0,
                              "tab": "_self",
                              "mode": "query",
                              "payloads": [
                                {
                                  "key": "tab",
                                  "value": "byReceiver",
                                  "id": "d3a549e6-442c-45d8-8edd-4e58ff2f3945"
                                }
                              ]
                            },
                            "thenActions": []
                          }
                        ],
                        "childrens": [],
                        "style": {},
                        "prop": {
                          "text": "变更记录",
                          "type": "default",
                          "size": "middle",
                          "aliasName": "按钮",
                          "ghost": false,
                          "danger": false,
                          "disabled": false,
                          "icon": "",
                          "show": {
                            "exp": ""
                          },
                          "forbid": {
                            "exp": ""
                          }
                        }
                      },
                      {
                        "compName": "lyy-button",
                        "compId": "0456180e-765c-4389-8e42-ce4000b19a16",
                        "compNameCn": "按钮",
                        "actions": [
                          {
                            "event": "click",
                            "action": "openModal",
                            "option": {
                              "targetId": "c0344945-e98d-41ba-b5f8-121986aa70f0"
                            },
                            "thenActions": []
                          }
                        ],
                        "childrens": [],
                        "style": {},
                        "prop": {
                          "text": "添加",
                          "type": "primary",
                          "size": "middle",
                          "aliasName": "按钮",
                          "ghost": false,
                          "danger": false,
                          "disabled": false,
                          "icon": "",
                          "show": {
                            "exp": ""
                          },
                          "forbid": {
                            "exp": ""
                          }
                        }
                      }
                    ]
                  },
                  {
                    "compName": "lyy-table",
                    "compId": "7412633d-bb36-4654-809b-f91d55d01c51",
                    "field": "name",
                    "compNameCn": "表格",
                    "prop": {
                      "columns": [
                        {
                          "title": "序号",
                          "dataIndex": "_no",
                          "width": 45,
                          "fixed": "left"
                        },
                        {
                          "title": "签约证件号",
                          "dataIndex": "idCode",
                          "width": 120,
                          "type": "text",
                          "align": "left",
                          "fixed": "",
                          "replaceNull": "-",
                          "separator": "",
                          "actions": [],
                          "sorter": false,
                          "resizable": true,
                          "headComp": [],
                          "prevComp": [],
                          "nextComp": [],
                          "childrens": [],
                          "option": {
                            "format": "",
                            "preappend": "",
                            "append": ""
                          },
                          "joins": [],
                          "pipes": [
                            {
                              "pipe": "",
                              "option": {}
                            }
                          ],
                          "colors": [
                            {
                              "exp": "",
                              "color": ""
                            }
                          ],
                          "show": {
                            "exp": ""
                          }
                        },
                        {
                          "title": "签约证件类型",
                          "dataIndex": "idCodeType",
                          "width": 120,
                          "type": "text",
                          "align": "left",
                          "fixed": "",
                          "replaceNull": "-",
                          "separator": "",
                          "actions": [],
                          "sorter": false,
                          "resizable": true,
                          "headComp": [],
                          "prevComp": [],
                          "nextComp": [],
                          "childrens": [],
                          "option": {
                            "format": "",
                            "preappend": "",
                            "append": ""
                          },
                          "joins": [],
                          "pipes": [
                            {
                              "pipe": "textmap",
                              "option": {
                                "textMap": [
                                  {
                                    "origin": 1,
                                    "result": "大陆身份证"
                                  },
                                  {
                                    "origin": 2,
                                    "result": "护照"
                                  },
                                  {
                                    "origin": 3,
                                    "result": "香港居民来往内地通行证"
                                  },
                                  {
                                    "origin": 4,
                                    "result": "澳门居民来往内地通行证"
                                  },
                                  {
                                    "origin": 5,
                                    "result": "台湾居民来往内地通行证"
                                  },
                                  {
                                    "origin": 6,
                                    "result": "中华人民共和国港澳居民居住证"
                                  },
                                  {
                                    "origin": 7,
                                    "result": "中华人民共和国台湾居民居住证"
                                  },
                                  {
                                    "origin": 8,
                                    "result": "外国人永久居留身份证"
                                  },
                                  {
                                    "origin": 9,
                                    "result": "中华人民共和国外国人就业许可证书"
                                  }
                                ]
                              }
                            }
                          ],
                          "colors": [
                            {
                              "exp": "",
                              "color": ""
                            }
                          ],
                          "show": {
                            "exp": ""
                          },
                          "compId": "7412633d-bb36-4654-809b-f91d55d01c51"
                        },
                        {
                          "title": "证件姓名",
                          "dataIndex": "name",
                          "width": 120,
                          "type": "text",
                          "align": "left",
                          "fixed": "",
                          "replaceNull": "-",
                          "separator": "",
                          "actions": [],
                          "sorter": false,
                          "resizable": true,
                          "headComp": [],
                          "prevComp": [],
                          "nextComp": [],
                          "childrens": [],
                          "option": {
                            "format": "",
                            "preappend": "",
                            "append": ""
                          },
                          "joins": [],
                          "pipes": [
                            {
                              "pipe": "",
                              "option": {}
                            }
                          ],
                          "colors": [
                            {
                              "exp": "",
                              "color": ""
                            }
                          ],
                          "show": {
                            "exp": ""
                          }
                        },
                        {
                          "title": "指定渠道",
                          "dataIndex": "payChannelName",
                          "width": 120,
                          "type": "text",
                          "align": "left",
                          "fixed": "",
                          "replaceNull": "-",
                          "separator": "",
                          "actions": [],
                          "sorter": false,
                          "resizable": true,
                          "headComp": [],
                          "prevComp": [],
                          "nextComp": [],
                          "childrens": [],
                          "option": {
                            "format": "",
                            "preappend": "",
                            "append": ""
                          },
                          "joins": [],
                          "pipes": [
                            {
                              "pipe": "",
                              "option": {}
                            }
                          ],
                          "colors": [
                            {
                              "exp": "",
                              "color": ""
                            }
                          ],
                          "show": {
                            "exp": ""
                          }
                        },
                        {
                          "title": "记录创建时间",
                          "dataIndex": "created",
                          "width": 120,
                          "type": "text",
                          "align": "left",
                          "fixed": "",
                          "replaceNull": "-",
                          "separator": "",
                          "actions": [],
                          "sorter": false,
                          "resizable": true,
                          "headComp": [],
                          "prevComp": [],
                          "nextComp": [],
                          "childrens": [],
                          "option": {
                            "format": "",
                            "preappend": "",
                            "append": ""
                          },
                          "joins": [],
                          "pipes": [
                            {
                              "pipe": "",
                              "option": {}
                            }
                          ],
                          "colors": [
                            {
                              "exp": "",
                              "color": ""
                            }
                          ],
                          "show": {
                            "exp": ""
                          }
                        },
                        {
                          "title": "记录更新时间",
                          "dataIndex": "updated",
                          "width": 120,
                          "type": "text",
                          "align": "left",
                          "fixed": "",
                          "replaceNull": "-",
                          "separator": "",
                          "actions": [],
                          "sorter": false,
                          "resizable": true,
                          "headComp": [],
                          "prevComp": [],
                          "nextComp": [],
                          "childrens": [],
                          "option": {
                            "format": "",
                            "preappend": "",
                            "append": ""
                          },
                          "joins": [],
                          "pipes": [
                            {
                              "pipe": "",
                              "option": {}
                            }
                          ],
                          "colors": [
                            {
                              "exp": "",
                              "color": ""
                            }
                          ],
                          "show": {
                            "exp": ""
                          }
                        },
                        {
                          "title": "操作栏",
                          "dataIndex": "",
                          "width": 120,
                          "type": "operation",
                          "align": "left",
                          "fixed": "",
                          "replaceNull": "-",
                          "separator": "",
                          "actions": [],
                          "sorter": false,
                          "resizable": true,
                          "headComp": [],
                          "prevComp": [],
                          "nextComp": [],
                          "childrens": [
                            {
                              "compName": "lyy-button",
                              "compId": "125aba30-ee5c-420f-b582-09f8509d60f9",
                              "compNameCn": "按钮",
                              "actions": [
                                {
                                  "event": "click",
                                  "action": "openModal",
                                  "option": {
                                    "targetId": "a1fa4c8a-2fc2-41e4-a0ac-dc81a82bc4c9"
                                  },
                                  "thenActions": []
                                }
                              ],
                              "childrens": [],
                              "style": {},
                              "prop": {
                                "text": "编辑",
                                "type": "link",
                                "size": "middle",
                                "aliasName": "按钮",
                                "ghost": false,
                                "danger": false,
                                "disabled": false,
                                "icon": "",
                                "show": {
                                  "exp": ""
                                },
                                "forbid": {
                                  "exp": ""
                                }
                              }
                            }
                          ],
                          "option": {
                            "format": "",
                            "preappend": "",
                            "append": ""
                          },
                          "joins": [],
                          "pipes": [
                            {
                              "pipe": "",
                              "option": {}
                            }
                          ],
                          "colors": [
                            {
                              "exp": "",
                              "color": ""
                            }
                          ],
                          "show": {
                            "exp": ""
                          },
                          "compId": "7412633d-bb36-4654-809b-f91d55d01c51"
                        }
                      ],
                      "datasource": [],
                      "rowKey": "",
                      "aliasName": "按收款人表格",
                      "size": "small",
                      "sticky": false,
                      "scroll": {
                        "x": "100%"
                      },
                      "showIndex": true,
                      "showSelection": false,
                      "rowSelection": {
                        "getCheckboxProps": ""
                      },
                      "disabledRow": {
                        "exp": "",
                        "background": ""
                      },
                      "backgroundRows": [
                        {
                          "exp": "",
                          "background": ""
                        }
                      ],
                      "summary": {
                        "placement": "",
                        "summarys": [
                          {
                            "label": "",
                            "field": "",
                            "value": "",
                            "pipes": [
                              {
                                "pipe": "",
                                "option": {}
                              }
                            ]
                          }
                        ],
                        "payloads": [
                          {
                            "key": "",
                            "source": "",
                            "sourceKey": "",
                            "decompose": false,
                            "value": ""
                          }
                        ],
                        "sumFields": "",
                        "averageFields": ""
                      },
                      "rowExpandIconWidth": "",
                      "mergeField": [],
                      "mergeUniqueField": [],
                      "colspanConfig": [
                        {
                          "exp": "",
                          "fields": []
                        }
                      ],
                      "fieldNames": {},
                      "expandFetch": {
                        "url": "",
                        "method": "",
                        "payloads": [
                          {
                            "key": "",
                            "source": "",
                            "sourceKey": "",
                            "decompose": false,
                            "value": ""
                          }
                        ],
                        "headerPayloads": [
                          {
                            "key": "",
                            "source": "",
                            "sourceKey": "",
                            "decompose": false,
                            "value": ""
                          }
                        ]
                      }
                    },
                    "modelValue": {
                      "selectedRows": [],
                      "datasource": [
                        {
                          "withdrawIdCardChannelId": 5249,
                          "idCode": "1232323232323",
                          "name": "小明123",
                          "idCodeType": 3,
                          "payChannelName": "life",
                          "created": "2023-02-07 10:46:23",
                          "updated": "2023-02-07 19:59:23"
                        },
                        {
                          "withdrawIdCardChannelId": 5248,
                          "idCode": "110101199003072877",
                          "name": "小明",
                          "idCodeType": 1,
                          "payChannelName": "云账户",
                          "created": "2023-02-07 10:44:46",
                          "updated": "2023-02-07 10:44:46"
                        }
                      ],
                      "currentRow": {}
                    },
                    "actions": []
                  },
                  {
                    "compName": "lyy-pagination",
                    "compId": "e6c13341-53fa-4bee-962c-d711713ce219",
                    "compNameCn": "分页",
                    "prop": {
                      "fields": [
                        "pageIndex",
                        "pageSize"
                      ],
                      "aliasName": "按收款人分页",
                      "defaultCurrent": 1,
                      "defaultPageSize": 10,
                      "total": 2,
                      "size": "default",
                      "hideOnSinglePage": null,
                      "showQuickJumper": null,
                      "showSizeChanger": null,
                      "simple": null,
                      "show": {
                        "exp": ""
                      },
                      "formId": ""
                    },
                    "style": {},
                    "actions": [
                      {
                        "event": "update",
                        "action": "broadcast",
                        "option": {
                          "targetId": "57da7d7b-f8ca-493e-a31b-210a9b0e8443",
                          "event": "mounted"
                        }
                      }
                    ],
                    "modelValue": [
                      1,
                      10
                    ]
                  }
                ]
              }
            ]
          },
          {
            "compName": "lyy-modal",
            "compId": "c0344945-e98d-41ba-b5f8-121986aa70f0",
            "compNameCn": "模态框",
            "modelValue": false,
            "style": {},
            "prop": {
              "title": "新增",
              "size": "middle",
              "loading": {
                "tip": "加载中",
                "spinning": true
              },
              "buttonList": [],
              "centered": true,
              "maskClosable": true,
              "mask": true,
              "closable": true,
              "okText": "确定",
              "okType": "primary",
              "cancelText": "取消",
              "destroyOnClose": true,
              "footer": false
            },
            "childrens": [
              {
                "compName": "lyy-tabs",
                "compId": "2aa6081e-4a1f-48b8-8fb2-d95d97379b67",
                "compNameCn": "选项卡",
                "style": {},
                "prop": {
                  "class": "",
                  "aliasName": "选项卡",
                  "tabs": [
                    {
                      "key": "",
                      "title": "添加身份证",
                      "childrens": [
                        {
                          "compName": "lyy-form",
                          "compId": "b20af9c2-28c1-44a5-9a77-2f4bf16bbb5f",
                          "compNameCn": "添加身份证",
                          "style": {},
                          "prop": {
                            "form": {},
                            "aliasName": "新增表单",
                            "isSignChange": null,
                            "mainLayout": false,
                            "labelCol": {
                              "style": {
                                "width": "110px"
                              }
                            },
                            "class": ""
                          },
                          "modelValue": {},
                          "actions": [],
                          "childrens": [
                            {
                              "compName": "lyy-text-input",
                              "compId": "557910d4-2d2f-4750-a036-774fa36f1e85",
                              "compNameCn": "输入框",
                              "style": {},
                              "prop": {
                                "field": "idCode",
                                "label": "身份证件号码",
                                "aliasName": "身份证件号码输入框",
                                "placeholder": "请输入身份证件号码",
                                "defaultValue": "",
                                "validateTrigger": "change",
                                "maxlength": 50,
                                "size": "default",
                                "suffix": "",
                                "addonAfter": "",
                                "rules": [
                                  {
                                    "required": true,
                                    "message": "请输入身份证件号码",
                                    "trigger": "blur"
                                  }
                                ],
                                "disabled": false,
                                "showCount": false,
                                "allowClear": true,
                                "labelCol": {
                                  "style": {}
                                },
                                "icon": {
                                  "iconName": "",
                                  "popover": {
                                    "description": []
                                  }
                                },
                                "formId": "",
                                "show": {
                                  "exp": ""
                                },
                                "forbid": {
                                  "exp": ""
                                }
                              },
                              "modelValue": "",
                              "actions": []
                            },
                            {
                              "compName": "lyy-select",
                              "compId": "86e88dc9-0b23-4a4d-bbf4-7edd62442a54",
                              "compNameCn": "身份证件类型",
                              "prop": {
                                "field": "idCodeType",
                                "label": "身份证件类型",
                                "aliasName": "下拉框",
                                "placeholder": "请选择身份证件类型",
                                "mode": "combobox",
                                "defaultValue": {
                                  "source": "",
                                  "sourceKey": ""
                                },
                                "rules": [
                                  {
                                    "required": true,
                                    "message": "请选择身份证件类型",
                                    "trigger": "change"
                                  }
                                ],
                                "options": [
                                  {
                                    "label": "大陆身份证",
                                    "value": 1,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "label": "护照",
                                    "value": 2,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "label": "香港居民来往内地通行证",
                                    "value": 3,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "label": "澳门居民来往内地通行证",
                                    "value": 4,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "label": "台湾居民来往内地通行证",
                                    "value": 5,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "label": "中华人民共和国港澳居民居住证",
                                    "value": 6,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "label": "中华人民共和国台湾居民居住证",
                                    "value": 7,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "label": "外国人永久居留身份证",
                                    "value": 8,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "label": "中华人民共和国外国人就业许可证书",
                                    "value": 9,
                                    "disabled": false,
                                    "show": true
                                  }
                                ],
                                "fieldNames": {
                                  "label": "label",
                                  "value": "value"
                                },
                                "size": "default",
                                "immediate": true,
                                "allowClear": true,
                                "readonly": false,
                                "showSearch": false,
                                "optionFilterProp": "",
                                "disabled": false,
                                "lastComp": {
                                  "text": "",
                                  "icon": ""
                                },
                                "labelCol": {
                                  "style": {}
                                },
                                "icon": {
                                  "iconName": "",
                                  "popover": {
                                    "description": []
                                  }
                                },
                                "formId": "",
                                "show": {
                                  "exp": ""
                                },
                                "forbid": {
                                  "exp": ""
                                }
                              },
                              "actions": [
                                {
                                  "event": "update",
                                  "action": "customerJS",
                                  "option": {
                                    "customJs": "(useAction,data,context) => {\n    \n    \n    /* 自定义JS使用说明：\n* 1.动作执行函数useAction，可以执行所有类型的动作\n* 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n* 3.通过数据树对象data可以获取当前数据树的所有数据\n* 4.事件配置具体看 事件文档\n*/\nif (context.modelValue === 1) {\n    data['身份证件号码输入框-557910d4-2d2f-4750-a036-774fa36f1e85'].prop.rules = [\n        {\n            pattern:\n                /^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/,\n            message: '请输入正确的身份证件号码',\n            trigger: 'blur',\n        },\n        {\n            required: true,\n            message: '请输入身份证件号码',\n            trigger: 'blur',\n        },\n    ]\n} else {\n    data['身份证件号码输入框-557910d4-2d2f-4750-a036-774fa36f1e85'].prop.rules = [\n        {\n            required: true,\n            message: '请输入身份证件号码',\n            trigger: 'blur',\n        },\n    ]\n}\n\n    \n    \n    return useAction\n  }"
                                  },
                                  "thenActions": []
                                }
                              ],
                              "pageConfigureTemplateId": "1072157037252825088"
                            },
                            {
                              "compName": "lyy-text-input",
                              "compId": "038da8ba-eac4-4406-8751-6842828bf266",
                              "compNameCn": "输入框",
                              "style": {},
                              "prop": {
                                "field": "name",
                                "label": "姓名",
                                "aliasName": "文本输入框",
                                "placeholder": "请输入",
                                "defaultValue": "",
                                "validateTrigger": "change",
                                "maxlength": 50,
                                "size": "default",
                                "suffix": "",
                                "addonAfter": "",
                                "rules": [
                                  {
                                    "required": true,
                                    "message": "请输入姓名",
                                    "trigger": "blur",
                                    "pattern": ""
                                  }
                                ],
                                "disabled": false,
                                "showCount": false,
                                "allowClear": true,
                                "labelCol": {
                                  "style": {}
                                },
                                "icon": {
                                  "iconName": "",
                                  "popover": {
                                    "description": []
                                  }
                                },
                                "formId": "",
                                "show": {
                                  "exp": ""
                                },
                                "forbid": {
                                  "exp": ""
                                }
                              },
                              "modelValue": "",
                              "actions": []
                            },
                            {
                              "compName": "lyy-select",
                              "compId": "0203ee08-4b49-4288-87a1-d7e7022e1f6f",
                              "compNameCn": "指定渠道",
                              "prop": {
                                "field": "payChannelName",
                                "label": "指定渠道",
                                "aliasName": "下拉框",
                                "placeholder": "请选择",
                                "mode": "combobox",
                                "defaultValue": {
                                  "source": "",
                                  "sourceKey": ""
                                },
                                "rules": [
                                  {
                                    "required": true,
                                    "message": "请选择指定渠道",
                                    "trigger": "change"
                                  }
                                ],
                                "options": [
                                  {
                                    "payChannelId": 100501,
                                    "channelName": "云账户",
                                    "channelType": "enterprise",
                                    "isActive": "N",
                                    "remark": "天津云账户",
                                    "created": "2022-11-11 16:21:20",
                                    "businessType": 2,
                                    "clientType": 3,
                                    "monthlyLimit": 3,
                                    "priority": -11,
                                    "mchAppId": "27596956",
                                    "mchId": "27596956",
                                    "cert": null,
                                    "payRemark": null,
                                    "partnerKey": null,
                                    "isSign": true,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 100000,
                                    "channelName": "life",
                                    "channelType": "enterprise",
                                    "isActive": "Y",
                                    "remark": "",
                                    "created": "2020-03-24 17:53:12",
                                    "businessType": 0,
                                    "clientType": 1,
                                    "monthlyLimit": 200,
                                    "priority": -10,
                                    "mchAppId": "wxe3b203d57e31f7d5",
                                    "mchId": "1361650402",
                                    "cert": "/root/cert/1361650402_apiclient_cert.p12",
                                    "payRemark": "乐摇摇提现到账",
                                    "partnerKey": "CC2109e4bD8f35228Iaidc657ef3ffd4",
                                    "isSign": false,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 100601,
                                    "channelName": "星云开物兼职猫",
                                    "channelType": "enterprise",
                                    "isActive": "Y",
                                    "remark": "九尾科技-兼职猫",
                                    "created": "2022-11-11 16:21:20",
                                    "businessType": 2,
                                    "clientType": 4,
                                    "monthlyLimit": 0,
                                    "priority": -9,
                                    "mchAppId": "681",
                                    "mchId": "681",
                                    "cert": null,
                                    "payRemark": null,
                                    "partnerKey": null,
                                    "isSign": true,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 100200,
                                    "channelName": "好灵工",
                                    "channelType": "enterprise",
                                    "isActive": "Y",
                                    "remark": "灵鹊云-好灵工",
                                    "created": "2022-05-16 13:54:47",
                                    "businessType": 0,
                                    "clientType": 2,
                                    "monthlyLimit": 1,
                                    "priority": -2,
                                    "mchAppId": "102207061089810",
                                    "mchId": "102207061089810",
                                    "cert": null,
                                    "payRemark": null,
                                    "partnerKey": null,
                                    "isSign": true,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 100300,
                                    "channelName": "好灵工2",
                                    "channelType": "enterprise",
                                    "isActive": "Y",
                                    "remark": "灵鹊云-好灵工",
                                    "created": "2022-05-16 13:54:47",
                                    "businessType": 2,
                                    "clientType": 2,
                                    "monthlyLimit": 100,
                                    "priority": -1,
                                    "mchAppId": "10210825634510",
                                    "mchId": "10210825634510",
                                    "cert": null,
                                    "payRemark": null,
                                    "partnerKey": null,
                                    "isSign": true,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 10000011,
                                    "channelName": "life3",
                                    "channelType": "enterprise",
                                    "isActive": "N",
                                    "remark": "",
                                    "created": "2020-03-24 17:53:12",
                                    "businessType": 0,
                                    "clientType": 1,
                                    "monthlyLimit": 200,
                                    "priority": 0,
                                    "mchAppId": "wxe3b203d57e31f7d5",
                                    "mchId": "13616504021111",
                                    "cert": "/root/cert/1361650402_apiclient_cert.p12",
                                    "payRemark": "乐摇摇提现到账",
                                    "partnerKey": "CC2109e4bD8f35228Iaidc657ef3ffd4",
                                    "isSign": false,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 100702,
                                    "channelName": "乐联盟云账户9715",
                                    "channelType": "enterprise",
                                    "isActive": "Y",
                                    "remark": "天津云账户",
                                    "created": "2022-11-23 13:53:36",
                                    "businessType": 2,
                                    "clientType": 3,
                                    "monthlyLimit": 0,
                                    "priority": 0,
                                    "mchAppId": "04349715",
                                    "mchId": "04349715",
                                    "cert": null,
                                    "payRemark": null,
                                    "partnerKey": null,
                                    "isSign": true,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 100700,
                                    "channelName": "星云开物云账户2062",
                                    "channelType": "enterprise",
                                    "isActive": "Y",
                                    "remark": "天津云账户",
                                    "created": "2022-11-23 13:53:36",
                                    "businessType": 2,
                                    "clientType": 3,
                                    "monthlyLimit": 0,
                                    "priority": 0,
                                    "mchAppId": "02012062",
                                    "mchId": "02012062",
                                    "cert": null,
                                    "payRemark": null,
                                    "partnerKey": null,
                                    "isSign": true,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 100701,
                                    "channelName": "广分云账户5091",
                                    "channelType": "enterprise",
                                    "isActive": "Y",
                                    "remark": "天津云账户",
                                    "created": "2022-11-23 13:53:36",
                                    "businessType": 2,
                                    "clientType": 3,
                                    "monthlyLimit": 0,
                                    "priority": 0,
                                    "mchAppId": "09645091",
                                    "mchId": "09645091",
                                    "cert": null,
                                    "payRemark": null,
                                    "partnerKey": null,
                                    "isSign": true,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 100900,
                                    "channelName": "薪宝科技76663949",
                                    "channelType": "enterprise",
                                    "isActive": "Y",
                                    "remark": "薪宝科技",
                                    "created": "2022-12-27 16:26:38",
                                    "businessType": 2,
                                    "clientType": 5,
                                    "monthlyLimit": 0,
                                    "priority": 0,
                                    "mchAppId": "76663949",
                                    "mchId": "76663949",
                                    "cert": null,
                                    "payRemark": null,
                                    "partnerKey": null,
                                    "isSign": true,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 101000,
                                    "channelName": "薪宝科技（星云开物-生产-安徽）",
                                    "channelType": "enterprise",
                                    "isActive": "Y",
                                    "remark": "薪宝科技",
                                    "created": "2023-01-03 10:39:32",
                                    "businessType": 2,
                                    "clientType": 5,
                                    "monthlyLimit": 1,
                                    "priority": 0,
                                    "mchAppId": "46451295",
                                    "mchId": "46451295",
                                    "cert": null,
                                    "payRemark": null,
                                    "partnerKey": null,
                                    "isSign": true,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 100100,
                                    "channelName": "life2",
                                    "channelType": "enterprise",
                                    "isActive": "N",
                                    "remark": "1",
                                    "created": "2020-04-29 10:51:08",
                                    "businessType": 2,
                                    "clientType": 1,
                                    "monthlyLimit": 1,
                                    "priority": 1,
                                    "mchAppId": "wxe3b203d57e31f7d5",
                                    "mchId": "1361650402",
                                    "cert": "/root/cert/1361650402_apiclient_cert.p12",
                                    "payRemark": "乐摇摇提现到账",
                                    "partnerKey": "CC2109e4bD8f35228Iaidc657ef3ffd4",
                                    "isSign": false,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  },
                                  {
                                    "payChannelId": 100400,
                                    "channelName": "好灵工3",
                                    "channelType": "enterprise",
                                    "isActive": "Y",
                                    "remark": "灵鹊云-好灵工",
                                    "created": "2022-05-26 18:05:20",
                                    "businessType": 1,
                                    "clientType": 2,
                                    "monthlyLimit": 100,
                                    "priority": 3,
                                    "mchAppId": "10210825634510test3",
                                    "mchId": "10210825634510",
                                    "cert": null,
                                    "payRemark": null,
                                    "partnerKey": null,
                                    "isSign": true,
                                    "pageIndex": null,
                                    "pageSize": null,
                                    "disabled": false,
                                    "show": true
                                  }
                                ],
                                "fieldNames": {
                                  "label": "channelName",
                                  "value": "channelName"
                                },
                                "size": "default",
                                "immediate": false,
                                "allowClear": true,
                                "readonly": false,
                                "showSearch": false,
                                "optionFilterProp": "",
                                "disabled": false,
                                "lastComp": {
                                  "text": "",
                                  "icon": ""
                                },
                                "labelCol": {
                                  "style": {}
                                },
                                "icon": {
                                  "iconName": "",
                                  "popover": {
                                    "description": []
                                  }
                                },
                                "formId": "",
                                "show": {
                                  "exp": ""
                                },
                                "forbid": {
                                  "exp": ""
                                }
                              },
                              "actions": [
                                {
                                  "event": "mounted",
                                  "action": "setNewValue",
                                  "option": {
                                    "to": {
                                      "type": "dynamic",
                                      "dynamic": {
                                        "nodePath": "下拉框-0203ee08-4b49-4288-87a1-d7e7022e1f6f.prop.options"
                                      }
                                    },
                                    "from": {
                                      "dynamic": {
                                        "nodePath": "payChannelList.items"
                                      }
                                    }
                                  },
                                  "thenActions": []
                                }
                              ],
                              "pageConfigureTemplateId": "1072158082578239488"
                            },
                            {
                              "compName": "lyy-button-group",
                              "compId": "2200dd95-0520-4186-8415-ededcfb7a936",
                              "compNameCn": "按钮组",
                              "style": {},
                              "prop": {
                                "buttonGap": "12px",
                                "justifyContent": "center",
                                "show": {
                                  "exp": ""
                                }
                              },
                              "actions": [],
                              "childrens": [
                                {
                                  "compName": "lyy-button",
                                  "compId": "795c9159-1afe-4976-8a4b-393f2cf97fef",
                                  "compNameCn": "按钮",
                                  "actions": [
                                    {
                                      "event": "click",
                                      "action": "closeModal",
                                      "option": {
                                        "targetId": "c0344945-e98d-41ba-b5f8-121986aa70f0"
                                      },
                                      "thenActions": [
                                        {
                                          "event": "click",
                                          "action": "resetValue",
                                          "option": {
                                            "targetId": "b20af9c2-28c1-44a5-9a77-2f4bf16bbb5f"
                                          },
                                          "thenActions": []
                                        }
                                      ]
                                    }
                                  ],
                                  "childrens": [],
                                  "style": {},
                                  "prop": {
                                    "text": "取消",
                                    "type": "default",
                                    "size": "middle",
                                    "aliasName": "按钮",
                                    "ghost": false,
                                    "danger": false,
                                    "disabled": false,
                                    "icon": "",
                                    "show": {
                                      "exp": ""
                                    },
                                    "forbid": {
                                      "exp": ""
                                    }
                                  }
                                },
                                {
                                  "compName": "lyy-button",
                                  "compId": "3ebbcd48-8d93-4ad7-b46b-071517109667",
                                  "compNameCn": "按钮",
                                  "actions": [
                                    {
                                      "event": "click",
                                      "action": "validate",
                                      "option": {
                                        "targetId": "b20af9c2-28c1-44a5-9a77-2f4bf16bbb5f"
                                      },
                                      "thenActions": [
                                        {
                                          "event": "click",
                                          "action": "request",
                                          "option": {
                                            "url": "/finance/payChannel/withdraw/channel/idCard/add",
                                            "method": "post",
                                            "payloads": {
                                              "type": "dynamic",
                                              "static": "",
                                              "dynamic": {
                                                "nodePath": "新增表单-b20af9c2-28c1-44a5-9a77-2f4bf16bbb5f.modelValue"
                                              }
                                            },
                                            "responseDataKey": "",
                                            "customOption": {
                                              "loading": true,
                                              "successfulFeedback": {
                                                "type": "message",
                                                "duration": 2,
                                                "placement": "topLeft",
                                                "status": "success",
                                                "title": "信息添加成功",
                                                "message": ""
                                              },
                                              "nextEventsDelay": 500,
                                              "parse": false
                                            },
                                            "headerPayloads": [],
                                            "interceptors": {
                                              "requestInterceptor": "",
                                              "responseInterceptor": ""
                                            }
                                          },
                                          "thenActions": [
                                            {
                                              "event": "click",
                                              "action": "closeModal",
                                              "option": {
                                                "targetId": "c0344945-e98d-41ba-b5f8-121986aa70f0"
                                              },
                                              "thenActions": [
                                                {
                                                  "event": "click",
                                                  "action": "resetValue",
                                                  "option": {
                                                    "targetId": "b20af9c2-28c1-44a5-9a77-2f4bf16bbb5f"
                                                  },
                                                  "thenActions": [
                                                    {
                                                      "event": "click",
                                                      "action": "broadcast",
                                                      "option": {
                                                        "targetId": "57da7d7b-f8ca-493e-a31b-210a9b0e8443",
                                                        "event": "mounted"
                                                      },
                                                      "thenActions": []
                                                    }
                                                  ]
                                                }
                                              ]
                                            }
                                          ]
                                        }
                                      ]
                                    }
                                  ],
                                  "childrens": [],
                                  "style": {},
                                  "prop": {
                                    "text": "确定",
                                    "type": "primary",
                                    "size": "middle",
                                    "aliasName": "按钮",
                                    "ghost": false,
                                    "danger": false,
                                    "disabled": false,
                                    "icon": "",
                                    "show": {
                                      "exp": ""
                                    },
                                    "forbid": {
                                      "exp": ""
                                    }
                                  }
                                }
                              ],
                              "pageConfigureTemplateId": "1072170194562174976"
                            }
                          ],
                          "pageConfigureTemplateId": "1072153956809166848"
                        }
                      ]
                    },
                    {
                      "key": "",
                      "title": "批量添加身份证",
                      "childrens": [
                        {
                          "compName": "lyy-form",
                          "compId": "601189f2-8bfd-4dae-a539-848a0ee6c676",
                          "compNameCn": "表单",
                          "style": {},
                          "prop": {
                            "form": {},
                            "aliasName": "批量添加表单",
                            "isSignChange": null,
                            "mainLayout": false,
                            "labelCol": {
                              "style": {
                                "width": "120px"
                              }
                            },
                            "class": ""
                          },
                          "modelValue": {},
                          "actions": [
                            {
                              "event": "submit",
                              "action": "validate",
                              "option": {
                                "targetId": "601189f2-8bfd-4dae-a539-848a0ee6c676"
                              },
                              "thenActions": [
                                {
                                  "event": "submit*0",
                                  "action": "broadcast",
                                  "option": {
                                    "targetId": "0beb09d4-d68c-4120-9dbe-b316e4a03891",
                                    "event": "upload"
                                  },
                                  "thenActions": []
                                }
                              ]
                            }
                          ],
                          "childrens": [
                            {
                              "compName": "lyy-file-uploader",
                              "compId": "0beb09d4-d68c-4120-9dbe-b316e4a03891",
                              "compNameCn": "文件上传",
                              "modelValue": null,
                              "prop": {
                                "field": "file",
                                "label": "选择批量文件",
                                "type": "primary",
                                "uploadType": "other",
                                "fileSize": 50,
                                "maxCount": 10,
                                "accept": "xls,xlsx",
                                "tip": "",
                                "templateUrl": "https://oss.lyypublic.leyaoyao.com/dealer/按收款人指定渠道（模版）.xlsx",
                                "rules": [
                                  {
                                    "required": true,
                                    "message": "请选择文件",
                                    "trigger": "change"
                                  }
                                ],
                                "validateTrigger": "",
                                "disabled": false,
                                "multiple": false,
                                "auto": false,
                                "showCover": false,
                                "showTip": true,
                                "download": true,
                                "fieldNames": {
                                  "name": "",
                                  "url": ""
                                },
                                "uploadStyle": {},
                                "data": {},
                                "formId": "",
                                "show": {
                                  "exp": ""
                                },
                                "forbid": {
                                  "exp": "",
                                  "payloads": [
                                    {
                                      "key": "",
                                      "source": "",
                                      "sourceKey": "",
                                      "decompose": false,
                                      "value": ""
                                    }
                                  ]
                                }
                              },
                              "actions": [
                                {
                                  "event": "upload",
                                  "action": "upload",
                                  "option": {
                                    "url": "/finance/payChannel/withdraw/channel/idCard/batchSave",
                                    "method": "post",
                                    "payloads": [],
                                    "responseDataKey": "",
                                    "customOption": {
                                      "loading": false,
                                      "successfulFeedback": {
                                        "type": "message",
                                        "duration": 2,
                                        "placement": "topLeft",
                                        "status": "success",
                                        "title": "",
                                        "message": "批量添加成功"
                                      },
                                      "nextEventsDelay": 500,
                                      "codeValue": "0000000",
                                      "codeKey": "code",
                                      "messageKey": "message",
                                      "parse": false
                                    },
                                    "headerPayloads": []
                                  },
                                  "thenActions": [
                                    {
                                      "event": "upload",
                                      "action": "resetValue",
                                      "option": {
                                        "targetId": "601189f2-8bfd-4dae-a539-848a0ee6c676"
                                      },
                                      "thenActions": [
                                        {
                                          "event": "upload",
                                          "action": "closeModal",
                                          "option": {
                                            "targetId": "c0344945-e98d-41ba-b5f8-121986aa70f0"
                                          },
                                          "thenActions": [
                                            {
                                              "event": "upload",
                                              "action": "broadcast",
                                              "option": {
                                                "targetId": "57da7d7b-f8ca-493e-a31b-210a9b0e8443",
                                                "event": "mounted"
                                              },
                                              "thenActions": []
                                            }
                                          ]
                                        }
                                      ]
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "compName": "lyy-button-group",
                              "compId": "890e4dee-0d78-4418-a155-e1aec6bc83be",
                              "compNameCn": "批量添加-按钮组",
                              "style": {
                                "marginTop": "166px"
                              },
                              "prop": {
                                "buttonGap": "12px",
                                "justifyContent": "center",
                                "show": {
                                  "exp": ""
                                }
                              },
                              "actions": [],
                              "childrens": [
                                {
                                  "compName": "lyy-button",
                                  "compId": "6da60d3a-7eeb-4d48-83c4-c72709695dcd",
                                  "compNameCn": "按钮",
                                  "actions": [
                                    {
                                      "event": "click",
                                      "action": "resetValue",
                                      "option": {
                                        "targetId": "601189f2-8bfd-4dae-a539-848a0ee6c676"
                                      },
                                      "thenActions": [
                                        {
                                          "event": "click",
                                          "action": "closeModal",
                                          "option": {
                                            "targetId": "c0344945-e98d-41ba-b5f8-121986aa70f0"
                                          },
                                          "thenActions": []
                                        }
                                      ]
                                    }
                                  ],
                                  "childrens": [],
                                  "style": {},
                                  "prop": {
                                    "text": "取消",
                                    "type": "default",
                                    "size": "middle",
                                    "aliasName": "按钮",
                                    "ghost": false,
                                    "danger": false,
                                    "disabled": false,
                                    "icon": "",
                                    "show": {
                                      "exp": ""
                                    },
                                    "forbid": {
                                      "exp": ""
                                    }
                                  }
                                },
                                {
                                  "compName": "lyy-button",
                                  "compId": "b8671570-fa4d-46a8-8dc2-2a4c10dedc17",
                                  "compNameCn": "按钮",
                                  "actions": [
                                    {
                                      "event": "click",
                                      "action": "broadcast",
                                      "option": {
                                        "targetId": "601189f2-8bfd-4dae-a539-848a0ee6c676",
                                        "event": "submit"
                                      },
                                      "thenActions": []
                                    }
                                  ],
                                  "childrens": [],
                                  "style": {},
                                  "prop": {
                                    "text": "上传文件",
                                    "type": "primary",
                                    "size": "middle",
                                    "aliasName": "按钮",
                                    "ghost": false,
                                    "danger": false,
                                    "disabled": false,
                                    "icon": "",
                                    "show": {
                                      "exp": ""
                                    },
                                    "forbid": {
                                      "exp": ""
                                    }
                                  }
                                }
                              ],
                              "pageConfigureTemplateId": "1072177422895665152"
                            }
                          ]
                        }
                      ]
                    }
                  ],
                  "mainLayout": false,
                  "leftExtra": [],
                  "rightExtra": []
                },
                "modelValue": 0
              }
            ],
            "actions": [
              {
                "event": "confirm",
                "action": "validate",
                "option": {
                  "targetId": "1e4b25f6-8763-47e0-8d73-3324f6a8604f"
                },
                "condition": {},
                "thenActions": [
                  {
                    "event": "confirm",
                    "action": "request",
                    "option": {
                      "url": "",
                      "method": "post",
                      "payloads": {
                        "type": "dynamic",
                        "dynamic": {
                          "nodePath": "1e4b25f6-8763-47e0-8d73-3324f6a8604f.modelValue"
                        },
                        "higher": [],
                        "static": ""
                      },
                      "responseDataKey": "",
                      "headerPayloads": [],
                      "interceptors": {}
                    },
                    "condition": {},
                    "thenActions": [
                      {
                        "event": "confirm",
                        "action": "closeModal",
                        "option": {
                          "targetId": "c0344945-e98d-41ba-b5f8-121986aa70f0"
                        },
                        "condition": {},
                        "thenActions": [
                          {
                            "event": "confirm",
                            "action": "broadcast",
                            "option": {
                              "targetId": "57da7d7b-f8ca-493e-a31b-210a9b0e8443",
                              "event": "mounted"
                            },
                            "condition": {},
                            "thenActions": []
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            "compName": "lyy-modal",
            "compId": "a1fa4c8a-2fc2-41e4-a0ac-dc81a82bc4c9",
            "compNameCn": "模态框",
            "modelValue": false,
            "style": {},
            "prop": {
              "title": "编辑",
              "size": "middle",
              "loading": {
                "tip": "加载中",
                "spinning": true
              },
              "buttonList": [],
              "centered": true,
              "maskClosable": true,
              "mask": true,
              "closable": true,
              "okText": "确定",
              "okType": "primary",
              "cancelText": "取消",
              "destroyOnClose": true,
              "footer": true
            },
            "childrens": [
              {
                "compName": "lyy-form",
                "compId": "848d4c8a-18db-4bb8-a18c-10c3f9170c62",
                "compNameCn": "添加身份证",
                "style": {},
                "prop": {
                  "form": {},
                  "aliasName": "编辑表单",
                  "isSignChange": null,
                  "mainLayout": false,
                  "labelCol": {
                    "style": {
                      "width": "110px"
                    }
                  },
                  "class": ""
                },
                "modelValue": {},
                "actions": [
                  {
                    "event": "mounted",
                    "action": "setNewValue",
                    "option": {
                      "from": {
                        "dynamic": {
                          "nodePath": "按收款人表格-7412633d-bb36-4654-809b-f91d55d01c51.modelValue.currentRow"
                        }
                      },
                      "to": {
                        "type": "dynamic",
                        "dynamic": {
                          "nodePath": "编辑表单-848d4c8a-18db-4bb8-a18c-10c3f9170c62.modelValue"
                        }
                      }
                    },
                    "thenActions": []
                  }
                ],
                "childrens": [
                  {
                    "compName": "lyy-label-text",
                    "compId": "0d2852ce-b0a8-4f8b-9a0d-3a666229cbf7",
                    "compNameCn": "描述组件",
                    "prop": {
                      "field": "idCode",
                      "label": "身份证件号码",
                      "aliasName": "描述",
                      "value": "",
                      "type": "",
                      "labelWidth": "110",
                      "seperator": "",
                      "textPrefix": "",
                      "textSuffix": "",
                      "suffixIcon": {
                        "iconName": "",
                        "tooltip": ""
                      },
                      "prefixIcon": {
                        "iconName": "",
                        "tooltip": ""
                      },
                      "flag": false,
                      "tooltip": false,
                      "textStyle": {},
                      "pipes": [
                        {
                          "pipe": "",
                          "option": {}
                        }
                      ]
                    },
                    "actions": []
                  },
                  {
                    "compName": "lyy-select",
                    "compId": "ff466e89-5915-4dc0-b8d8-af4ca404b80a",
                    "compNameCn": "身份证件类型",
                    "prop": {
                      "field": "idCodeType",
                      "label": "身份证件类型",
                      "aliasName": "下拉框",
                      "placeholder": "请选择身份证件类型",
                      "mode": "combobox",
                      "defaultValue": {
                        "source": "",
                        "sourceKey": ""
                      },
                      "rules": [
                        {
                          "required": true,
                          "message": "请选择身份证件类型",
                          "trigger": "change"
                        }
                      ],
                      "options": [
                        {
                          "label": "大陆身份证",
                          "value": 1,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "label": "护照",
                          "value": 2,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "label": "香港居民来往内地通行证",
                          "value": 3,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "label": "澳门居民来往内地通行证",
                          "value": 4,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "label": "台湾居民来往内地通行证",
                          "value": 5,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "label": "中华人民共和国港澳居民居住证",
                          "value": 6,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "label": "中华人民共和国台湾居民居住证",
                          "value": 7,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "label": "外国人永久居留身份证",
                          "value": 8,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "label": "中华人民共和国外国人就业许可证书",
                          "value": 9,
                          "disabled": false,
                          "show": true
                        }
                      ],
                      "fieldNames": {
                        "label": "label",
                        "value": "value"
                      },
                      "size": "default",
                      "immediate": true,
                      "allowClear": true,
                      "readonly": false,
                      "showSearch": false,
                      "optionFilterProp": "",
                      "disabled": false,
                      "lastComp": {
                        "text": "",
                        "icon": ""
                      },
                      "labelCol": {
                        "style": {}
                      },
                      "icon": {
                        "iconName": "",
                        "popover": {
                          "description": []
                        }
                      },
                      "formId": "",
                      "show": {
                        "exp": ""
                      },
                      "forbid": {
                        "exp": ""
                      }
                    },
                    "actions": [],
                    "pageConfigureTemplateId": "1072157037252825088"
                  },
                  {
                    "compName": "lyy-text-input",
                    "compId": "ddd4ab7f-84f1-4a77-befb-77f16c2f3ea0",
                    "compNameCn": "输入框",
                    "style": {},
                    "prop": {
                      "field": "name",
                      "label": "姓名",
                      "aliasName": "文本输入框",
                      "placeholder": "请输入",
                      "defaultValue": "",
                      "validateTrigger": "change",
                      "maxlength": 50,
                      "size": "default",
                      "suffix": "",
                      "addonAfter": "",
                      "rules": [
                        {
                          "required": true,
                          "message": "请输入姓名",
                          "trigger": "blur",
                          "pattern": ""
                        }
                      ],
                      "disabled": false,
                      "showCount": false,
                      "allowClear": true,
                      "labelCol": {
                        "style": {}
                      },
                      "icon": {
                        "iconName": "",
                        "popover": {
                          "description": []
                        }
                      },
                      "formId": "",
                      "show": {
                        "exp": ""
                      },
                      "forbid": {
                        "exp": ""
                      }
                    },
                    "modelValue": "",
                    "actions": []
                  },
                  {
                    "compName": "lyy-select",
                    "compId": "08042343-0ab3-427c-8a53-29ced258a836",
                    "compNameCn": "指定渠道",
                    "prop": {
                      "field": "payChannelName",
                      "label": "指定渠道",
                      "aliasName": "下拉框",
                      "placeholder": "请选择",
                      "mode": "combobox",
                      "defaultValue": {
                        "source": "",
                        "sourceKey": ""
                      },
                      "rules": [
                        {
                          "required": true,
                          "message": "请选择指定渠道",
                          "trigger": "change"
                        }
                      ],
                      "options": [
                        {
                          "payChannelId": 100501,
                          "channelName": "云账户",
                          "channelType": "enterprise",
                          "isActive": "N",
                          "remark": "天津云账户",
                          "created": "2022-11-11 16:21:20",
                          "businessType": 2,
                          "clientType": 3,
                          "monthlyLimit": 3,
                          "priority": -11,
                          "mchAppId": "27596956",
                          "mchId": "27596956",
                          "cert": null,
                          "payRemark": null,
                          "partnerKey": null,
                          "isSign": true,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 100000,
                          "channelName": "life",
                          "channelType": "enterprise",
                          "isActive": "Y",
                          "remark": "",
                          "created": "2020-03-24 17:53:12",
                          "businessType": 0,
                          "clientType": 1,
                          "monthlyLimit": 200,
                          "priority": -10,
                          "mchAppId": "wxe3b203d57e31f7d5",
                          "mchId": "1361650402",
                          "cert": "/root/cert/1361650402_apiclient_cert.p12",
                          "payRemark": "乐摇摇提现到账",
                          "partnerKey": "CC2109e4bD8f35228Iaidc657ef3ffd4",
                          "isSign": false,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 100601,
                          "channelName": "星云开物兼职猫",
                          "channelType": "enterprise",
                          "isActive": "Y",
                          "remark": "九尾科技-兼职猫",
                          "created": "2022-11-11 16:21:20",
                          "businessType": 2,
                          "clientType": 4,
                          "monthlyLimit": 0,
                          "priority": -9,
                          "mchAppId": "681",
                          "mchId": "681",
                          "cert": null,
                          "payRemark": null,
                          "partnerKey": null,
                          "isSign": true,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 100200,
                          "channelName": "好灵工",
                          "channelType": "enterprise",
                          "isActive": "Y",
                          "remark": "灵鹊云-好灵工",
                          "created": "2022-05-16 13:54:47",
                          "businessType": 0,
                          "clientType": 2,
                          "monthlyLimit": 1,
                          "priority": -2,
                          "mchAppId": "102207061089810",
                          "mchId": "102207061089810",
                          "cert": null,
                          "payRemark": null,
                          "partnerKey": null,
                          "isSign": true,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 100300,
                          "channelName": "好灵工2",
                          "channelType": "enterprise",
                          "isActive": "Y",
                          "remark": "灵鹊云-好灵工",
                          "created": "2022-05-16 13:54:47",
                          "businessType": 2,
                          "clientType": 2,
                          "monthlyLimit": 100,
                          "priority": -1,
                          "mchAppId": "10210825634510",
                          "mchId": "10210825634510",
                          "cert": null,
                          "payRemark": null,
                          "partnerKey": null,
                          "isSign": true,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 10000011,
                          "channelName": "life3",
                          "channelType": "enterprise",
                          "isActive": "N",
                          "remark": "",
                          "created": "2020-03-24 17:53:12",
                          "businessType": 0,
                          "clientType": 1,
                          "monthlyLimit": 200,
                          "priority": 0,
                          "mchAppId": "wxe3b203d57e31f7d5",
                          "mchId": "13616504021111",
                          "cert": "/root/cert/1361650402_apiclient_cert.p12",
                          "payRemark": "乐摇摇提现到账",
                          "partnerKey": "CC2109e4bD8f35228Iaidc657ef3ffd4",
                          "isSign": false,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 100702,
                          "channelName": "乐联盟云账户9715",
                          "channelType": "enterprise",
                          "isActive": "Y",
                          "remark": "天津云账户",
                          "created": "2022-11-23 13:53:36",
                          "businessType": 2,
                          "clientType": 3,
                          "monthlyLimit": 0,
                          "priority": 0,
                          "mchAppId": "04349715",
                          "mchId": "04349715",
                          "cert": null,
                          "payRemark": null,
                          "partnerKey": null,
                          "isSign": true,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 100700,
                          "channelName": "星云开物云账户2062",
                          "channelType": "enterprise",
                          "isActive": "Y",
                          "remark": "天津云账户",
                          "created": "2022-11-23 13:53:36",
                          "businessType": 2,
                          "clientType": 3,
                          "monthlyLimit": 0,
                          "priority": 0,
                          "mchAppId": "02012062",
                          "mchId": "02012062",
                          "cert": null,
                          "payRemark": null,
                          "partnerKey": null,
                          "isSign": true,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 100701,
                          "channelName": "广分云账户5091",
                          "channelType": "enterprise",
                          "isActive": "Y",
                          "remark": "天津云账户",
                          "created": "2022-11-23 13:53:36",
                          "businessType": 2,
                          "clientType": 3,
                          "monthlyLimit": 0,
                          "priority": 0,
                          "mchAppId": "09645091",
                          "mchId": "09645091",
                          "cert": null,
                          "payRemark": null,
                          "partnerKey": null,
                          "isSign": true,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 100900,
                          "channelName": "薪宝科技76663949",
                          "channelType": "enterprise",
                          "isActive": "Y",
                          "remark": "薪宝科技",
                          "created": "2022-12-27 16:26:38",
                          "businessType": 2,
                          "clientType": 5,
                          "monthlyLimit": 0,
                          "priority": 0,
                          "mchAppId": "76663949",
                          "mchId": "76663949",
                          "cert": null,
                          "payRemark": null,
                          "partnerKey": null,
                          "isSign": true,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 101000,
                          "channelName": "薪宝科技（星云开物-生产-安徽）",
                          "channelType": "enterprise",
                          "isActive": "Y",
                          "remark": "薪宝科技",
                          "created": "2023-01-03 10:39:32",
                          "businessType": 2,
                          "clientType": 5,
                          "monthlyLimit": 1,
                          "priority": 0,
                          "mchAppId": "46451295",
                          "mchId": "46451295",
                          "cert": null,
                          "payRemark": null,
                          "partnerKey": null,
                          "isSign": true,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 100100,
                          "channelName": "life2",
                          "channelType": "enterprise",
                          "isActive": "N",
                          "remark": "1",
                          "created": "2020-04-29 10:51:08",
                          "businessType": 2,
                          "clientType": 1,
                          "monthlyLimit": 1,
                          "priority": 1,
                          "mchAppId": "wxe3b203d57e31f7d5",
                          "mchId": "1361650402",
                          "cert": "/root/cert/1361650402_apiclient_cert.p12",
                          "payRemark": "乐摇摇提现到账",
                          "partnerKey": "CC2109e4bD8f35228Iaidc657ef3ffd4",
                          "isSign": false,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        },
                        {
                          "payChannelId": 100400,
                          "channelName": "好灵工3",
                          "channelType": "enterprise",
                          "isActive": "Y",
                          "remark": "灵鹊云-好灵工",
                          "created": "2022-05-26 18:05:20",
                          "businessType": 1,
                          "clientType": 2,
                          "monthlyLimit": 100,
                          "priority": 3,
                          "mchAppId": "10210825634510test3",
                          "mchId": "10210825634510",
                          "cert": null,
                          "payRemark": null,
                          "partnerKey": null,
                          "isSign": true,
                          "pageIndex": null,
                          "pageSize": null,
                          "disabled": false,
                          "show": true
                        }
                      ],
                      "fieldNames": {
                        "label": "channelName",
                        "value": "channelName"
                      },
                      "size": "default",
                      "immediate": false,
                      "allowClear": true,
                      "readonly": false,
                      "showSearch": false,
                      "optionFilterProp": "",
                      "disabled": false,
                      "lastComp": {
                        "text": "",
                        "icon": ""
                      },
                      "labelCol": {
                        "style": {}
                      },
                      "icon": {
                        "iconName": "",
                        "popover": {
                          "description": []
                        }
                      },
                      "formId": "",
                      "show": {
                        "exp": ""
                      },
                      "forbid": {
                        "exp": ""
                      }
                    },
                    "actions": [
                      {
                        "event": "mounted",
                        "action": "setNewValue",
                        "option": {
                          "to": {
                            "type": "dynamic",
                            "dynamic": {
                              "nodePath": "下拉框-08042343-0ab3-427c-8a53-29ced258a836.prop.options"
                            }
                          },
                          "from": {
                            "dynamic": {
                              "nodePath": "payChannelList.items"
                            }
                          }
                        },
                        "thenActions": []
                      }
                    ],
                    "pageConfigureTemplateId": "1072158082578239488"
                  }
                ],
                "pageConfigureTemplateId": "1072165694199812096"
              }
            ],
            "actions": [
              {
                "event": "confirm",
                "action": "validate",
                "option": {
                  "targetId": "848d4c8a-18db-4bb8-a18c-10c3f9170c62"
                },
                "thenActions": [
                  {
                    "event": "confirm",
                    "action": "request",
                    "option": {
                      "url": "/finance/payChannel/withdraw/channel/idCard/edit",
                      "method": "post",
                      "payloads": {
                        "type": "dynamic",
                        "dynamic": {
                          "nodePath": "编辑表单-848d4c8a-18db-4bb8-a18c-10c3f9170c62.modelValue"
                        },
                        "higher": [],
                        "static": ""
                      },
                      "responseDataKey": "",
                      "customOption": {
                        "loading": true,
                        "successfulFeedback": {
                          "type": "message",
                          "duration": 2,
                          "placement": "topLeft",
                          "status": "success",
                          "title": "",
                          "message": "信息编辑成功"
                        },
                        "nextEventsDelay": 500,
                        "parse": false
                      },
                      "headerPayloads": [],
                      "interceptors": {}
                    },
                    "thenActions": [
                      {
                        "event": "confirm",
                        "action": "closeModal",
                        "option": {
                          "targetId": "a1fa4c8a-2fc2-41e4-a0ac-dc81a82bc4c9"
                        },
                        "thenActions": [
                          {
                            "event": "confirm",
                            "action": "resetValue",
                            "option": {
                              "targetId": "848d4c8a-18db-4bb8-a18c-10c3f9170c62"
                            },
                            "thenActions": [
                              {
                                "event": "confirm",
                                "action": "broadcast",
                                "option": {
                                  "targetId": "57da7d7b-f8ca-493e-a31b-210a9b0e8443",
                                  "event": "mounted"
                                },
                                "thenActions": []
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
];
