export default [
  {
    "compName": "lyy-container",
    "compId": "71dff363-9870-4f7f-b221-d6c1690dd2b9",
    "compNameCn": "容器",
    "prop": {},
    "actions": [],
    "childrens": [
      {
        "compName": "lyy-form",
        "compId": "c6181a75-f9b3-422d-8823-36657a7bf59f",
        "compNameCn": "表单",
        "style": {},
        "prop": {
          "form": {},
          "aliasName": "变更记录表单",
          "isSignChange": null,
          "mainLayout": false,
          "labelCol": {
            "style": {
              "width": "100px"
            }
          },
          "class": ""
        },
        "modelValue": {},
        "actions": [
          {
            "event": "mounted",
            "action": "setNewValue",
            "option": {
              "from": {
                "dynamic": {
                  "nodePath": "route.query.tab"
                }
              },
              "to": {
                "type": "dynamic",
                "dynamic": {
                  "nodePath": "变更记录表单-c6181a75-f9b3-422d-8823-36657a7bf59f.modelValue.tab"
                }
              }
            },
            "thenActions": []
          }
        ],
        "childrens": [
          {
            "compName": "lyy-button",
            "compId": "ad3d36ac-41db-4a54-89dd-e021f68d5664",
            "compNameCn": "按钮",
            "actions": [
              {
                "event": "click",
                "action": "linkto",
                "option": {
                  "url": "",
                  "go": -1,
                  "tab": "_self",
                  "mode": "query",
                  "payloads": []
                },
                "thenActions": []
              }
            ],
            "childrens": [],
            "style": {},
            "prop": {
              "text": "返回上一级",
              "type": "primary",
              "size": "middle",
              "aliasName": "按钮",
              "ghost": false,
              "danger": false,
              "disabled": false,
              "icon": "",
              "show": {
                "exp": ""
              },
              "forbid": {
                "exp": ""
              }
            }
          },
          {
            "compName": "lyy-tabs",
            "compId": "0c983cfc-ef90-49cd-b15e-cf595c6b1e1e",
            "compNameCn": "变更记录",
            "style": {},
            "prop": {
              "field": "tab",
              "class": "",
              "aliasName": "选项卡",
              "tabs": [
                {
                  "key": "byReceiver",
                  "title": "变更记录（按收款人）",
                  "childrens": [
                    {
                      "compName": "lyy-container",
                      "compId": "f08ce29a-7567-482a-a493-59cb3a460cae",
                      "compNameCn": "变更记录-按收款人",
                      "prop": {},
                      "actions": [],
                      "childrens": [
                        {
                          "compName": "lyy-form",
                          "compId": "4de8d07b-7f39-4695-96b5-0451752a1b34",
                          "compNameCn": "表单",
                          "style": {},
                          "prop": {
                            "form": {
                              "pageIndex": 1,
                              "pageSize": 10,
                              "startTime": "2023-02-08",
                              "endTime": "2023-02-08",
                              "current": 1,
                              "size": 10
                            },
                            "aliasName": "表单",
                            "isSignChange": null,
                            "mainLayout": false,
                            "labelCol": {
                              "style": {
                                "width": "100px"
                              }
                            },
                            "class": ""
                          },
                          "modelValue": {
                            "pageIndex": 1,
                            "pageSize": 10,
                            "startTime": "2023-02-08",
                            "endTime": "2023-02-08",
                            "current": 1,
                            "size": 10
                          },
                          "actions": [
                            {
                              "event": "mounted",
                              "action": "request",
                              "option": {
                                "url": "/finance/payChannel/withdraw/channel/idCard/record/pageList",
                                "method": "post",
                                "payloads": {
                                  "type": "dynamic",
                                  "dynamic": {
                                    "nodePath": "表单-4de8d07b-7f39-4695-96b5-0451752a1b34.modelValue"
                                  },
                                  "higher": [],
                                  "static": ""
                                },
                                "responseDataKey": "CRUD-4de8d07b-7f39-4695-96b5-0451752a1b34",
                                "customOption": {
                                  "loading": true,
                                  "codeValue": "0000000",
                                  "codeKey": "code",
                                  "messageKey": "message"
                                },
                                "headerPayloads": [],
                                "interceptors": {
                                  "requestInterceptor": "(config) => {\n  \n  return config;\n  }",
                                  "responseInterceptor": "(res) => {\n  \n  return res;\n  }"
                                }
                              },
                              "thenActions": [
                                {
                                  "event": "mounted",
                                  "action": "setNewValue",
                                  "option": {
                                    "to": {
                                      "type": "dynamic",
                                      "dynamic": {
                                        "nodePath": "5acfec38-bd84-4858-b5d6-c59b4eef34d1.modelValue.datasource"
                                      }
                                    },
                                    "from": {
                                      "dynamic": {
                                        "nodePath": "CRUD-4de8d07b-7f39-4695-96b5-0451752a1b34.list"
                                      }
                                    }
                                  },
                                  "thenActions": [
                                    {
                                      "event": "mounted",
                                      "action": "setNewValue",
                                      "option": {
                                        "to": {
                                          "type": "dynamic",
                                          "dynamic": {
                                            "nodePath": "c37e5af8-a893-4b2b-997b-a48a1a45d5c0.prop.total"
                                          }
                                        },
                                        "from": {
                                          "dynamic": {
                                            "nodePath": "CRUD-4de8d07b-7f39-4695-96b5-0451752a1b34.total"
                                          }
                                        }
                                      },
                                      "thenActions": []
                                    }
                                  ]
                                }
                              ]
                            }
                          ],
                          "childrens": [
                            {
                              "compName": "lyy-search-pro",
                              "compId": "c9d0cb7c-e150-4d56-9c7c-87940c459af9",
                              "compNameCn": "搜索容器",
                              "prop": {
                                "searchFormList": [
                                  {
                                    "compName": "lyy-text-input",
                                    "compId": "3bdd162b-3ca9-40a5-b68a-48b18a45b82a",
                                    "compNameCn": "输入框",
                                    "style": {},
                                    "prop": {
                                      "field": "idCode",
                                      "label": "身份证号",
                                      "aliasName": "文本输入框",
                                      "placeholder": "请输入",
                                      "defaultValue": "",
                                      "validateTrigger": "change",
                                      "maxlength": 50,
                                      "size": "default",
                                      "suffix": "",
                                      "addonAfter": "",
                                      "rules": [
                                        {
                                          "required": false,
                                          "message": "",
                                          "trigger": "",
                                          "pattern": ""
                                        }
                                      ],
                                      "disabled": false,
                                      "showCount": false,
                                      "allowClear": true,
                                      "labelCol": {
                                        "style": {}
                                      },
                                      "icon": {
                                        "iconName": "",
                                        "popover": {
                                          "description": []
                                        }
                                      },
                                      "formId": "",
                                      "show": {
                                        "exp": ""
                                      },
                                      "forbid": {
                                        "exp": ""
                                      }
                                    },
                                    "modelValue": "",
                                    "actions": []
                                  },
                                  {
                                    "compName": "lyy-text-input",
                                    "compId": "77498940-e7f2-4639-9cd0-20d09c5d4455",
                                    "compNameCn": "输入框",
                                    "style": {},
                                    "prop": {
                                      "field": "name",
                                      "label": "姓名",
                                      "aliasName": "文本输入框",
                                      "placeholder": "请输入",
                                      "defaultValue": "",
                                      "validateTrigger": "change",
                                      "maxlength": 50,
                                      "size": "default",
                                      "suffix": "",
                                      "addonAfter": "",
                                      "rules": [
                                        {
                                          "required": false,
                                          "message": "",
                                          "trigger": "",
                                          "pattern": ""
                                        }
                                      ],
                                      "disabled": false,
                                      "showCount": false,
                                      "allowClear": true,
                                      "labelCol": {
                                        "style": {}
                                      },
                                      "icon": {
                                        "iconName": "",
                                        "popover": {
                                          "description": []
                                        }
                                      },
                                      "formId": "",
                                      "show": {
                                        "exp": ""
                                      },
                                      "forbid": {
                                        "exp": ""
                                      }
                                    },
                                    "modelValue": "",
                                    "actions": []
                                  },
                                  {
                                    "compName": "lyy-select",
                                    "compId": "6d900b42-5787-4a08-aebe-fc7b43d1625e",
                                    "compNameCn": "下拉框",
                                    "prop": {
                                      "field": "idCodeType",
                                      "label": "证件类型",
                                      "aliasName": "下拉框",
                                      "placeholder": "请选择",
                                      "mode": "combobox",
                                      "defaultValue": {
                                        "source": "",
                                        "sourceKey": ""
                                      },
                                      "rules": [
                                        {
                                          "required": false,
                                          "message": "",
                                          "trigger": ""
                                        }
                                      ],
                                      "options": [
                                        {
                                          "label": "大陆身份证",
                                          "value": 1,
                                          "disabled": false,
                                          "show": true
                                        },
                                        {
                                          "label": "护照",
                                          "value": 2,
                                          "disabled": false,
                                          "show": true
                                        },
                                        {
                                          "label": "香港居民来往内地通行证",
                                          "value": 3,
                                          "disabled": false,
                                          "show": true
                                        },
                                        {
                                          "label": "澳门居民来往内地通行证",
                                          "value": 4,
                                          "disabled": false,
                                          "show": true
                                        },
                                        {
                                          "label": "台湾居民来往内地通行证",
                                          "value": 5,
                                          "disabled": false,
                                          "show": true
                                        },
                                        {
                                          "label": "中华人民共和国港澳居民居住证",
                                          "value": 6,
                                          "disabled": false,
                                          "show": true
                                        },
                                        {
                                          "label": "中华人民共和国台湾居民居住证",
                                          "value": 7,
                                          "disabled": false,
                                          "show": true
                                        },
                                        {
                                          "label": "外国人永久居留身份证",
                                          "value": 8,
                                          "disabled": false,
                                          "show": true
                                        },
                                        {
                                          "label": "中华人民共和国外国人就业许可证书",
                                          "value": 9,
                                          "disabled": false,
                                          "show": true
                                        }
                                      ],
                                      "fieldNames": {
                                        "label": "label",
                                        "value": "value"
                                      },
                                      "size": "default",
                                      "immediate": true,
                                      "allowClear": true,
                                      "readonly": false,
                                      "showSearch": true,
                                      "optionFilterProp": "",
                                      "disabled": false,
                                      "lastComp": {
                                        "text": "",
                                        "icon": ""
                                      },
                                      "labelCol": {
                                        "style": {}
                                      },
                                      "icon": {
                                        "iconName": "",
                                        "popover": {
                                          "description": []
                                        }
                                      },
                                      "formId": "",
                                      "show": {
                                        "exp": ""
                                      },
                                      "forbid": {
                                        "exp": ""
                                      }
                                    },
                                    "actions": []
                                  },
                                  {
                                    "compName": "lyy-text-input",
                                    "compId": "8ee669a6-e89d-43ee-a9cf-6bbe8cc45a1e",
                                    "compNameCn": "输入框",
                                    "style": {},
                                    "prop": {
                                      "field": "operator",
                                      "label": "操作人",
                                      "aliasName": "文本输入框",
                                      "placeholder": "请输入",
                                      "defaultValue": "",
                                      "validateTrigger": "change",
                                      "maxlength": 50,
                                      "size": "default",
                                      "suffix": "",
                                      "addonAfter": "",
                                      "rules": [
                                        {
                                          "required": false,
                                          "message": "",
                                          "trigger": "",
                                          "pattern": ""
                                        }
                                      ],
                                      "disabled": false,
                                      "showCount": false,
                                      "allowClear": true,
                                      "labelCol": {
                                        "style": {}
                                      },
                                      "icon": {
                                        "iconName": "",
                                        "popover": {
                                          "description": []
                                        }
                                      },
                                      "formId": "",
                                      "show": {
                                        "exp": ""
                                      },
                                      "forbid": {
                                        "exp": ""
                                      }
                                    },
                                    "modelValue": "",
                                    "actions": []
                                  },
                                  {
                                    "compName": "lyy-date-range",
                                    "compId": "0f7c6934-6ac1-494f-85d5-d4b28b90240a",
                                    "compNameCn": "日期范围",
                                    "style": {},
                                    "prop": {
                                      "fields": [
                                        "startTime",
                                        "endTime"
                                      ],
                                      "label": "操作日期",
                                      "aliasName": "日期",
                                      "placeholder": [
                                        "开始日期",
                                        "结束日期"
                                      ],
                                      "defaultValue": "今天",
                                      "picker": "date",
                                      "valueFormat": "YYYY-MM-DD",
                                      "rules": [
                                        {
                                          "required": false,
                                          "message": "",
                                          "trigger": "",
                                          "pattern": ""
                                        }
                                      ],
                                      "disabledOption": {
                                        "unit": "year",
                                        "duration": 2
                                      },
                                      "fastOptions": [],
                                      "validateTrigger": "",
                                      "showNow": false,
                                      "showToday": false,
                                      "showTime": false,
                                      "allowClear": true,
                                      "size": "default",
                                      "disabled": false,
                                      "labelCol": {
                                        "style": {}
                                      },
                                      "icon": {
                                        "iconName": "",
                                        "popover": {
                                          "description": []
                                        }
                                      },
                                      "show": {
                                        "exp": ""
                                      },
                                      "forbid": {
                                        "exp": "",
                                        "payloads": [
                                          {
                                            "key": "",
                                            "source": "",
                                            "sourceKey": "",
                                            "decompose": false,
                                            "value": ""
                                          }
                                        ]
                                      },
                                      "field": ""
                                    },
                                    "actions": [],
                                    "modelValue": [
                                      "2023-02-08",
                                      "2023-02-08"
                                    ]
                                  }
                                ],
                                "aliasName": "搜索容器",
                                "formStorage": false
                              },
                              "actions": []
                            },
                            {
                              "compName": "lyy-card",
                              "compId": "80cf17ab-17e8-40d7-9c04-dcf2878350dd",
                              "compNameCn": "卡片",
                              "prop": {
                                "showHeader": false,
                                "title": "卡片容器",
                                "aliasName": "卡片容器",
                                "extraList": [],
                                "mainLayout": false,
                                "bordered": true
                              },
                              "actions": [],
                              "childrens": [
                                {
                                  "compName": "lyy-table",
                                  "compId": "5acfec38-bd84-4858-b5d6-c59b4eef34d1",
                                  "field": "name",
                                  "compNameCn": "表格",
                                  "prop": {
                                    "columns": [
                                      {
                                        "title": "序号",
                                        "dataIndex": "_no",
                                        "width": 45,
                                        "fixed": "left"
                                      },
                                      {
                                        "title": "操作类型",
                                        "dataIndex": "operationType",
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "textmap",
                                            "option": {
                                              "textMap": [
                                                {
                                                  "origin": 1,
                                                  "result": "新增"
                                                },
                                                {
                                                  "origin": 2,
                                                  "result": "变更"
                                                }
                                              ]
                                            }
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "5acfec38-bd84-4858-b5d6-c59b4eef34d1"
                                      },
                                      {
                                        "title": "签约证件号",
                                        "dataIndex": "idCode",
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "5acfec38-bd84-4858-b5d6-c59b4eef34d1"
                                      },
                                      {
                                        "title": "签约证件类型",
                                        "dataIndex": "idCodeType",
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "textmap",
                                            "option": {
                                              "textMap": [
                                                {
                                                  "origin": 1,
                                                  "result": "大陆身份证"
                                                },
                                                {
                                                  "origin": 2,
                                                  "result": "护照"
                                                },
                                                {
                                                  "origin": 3,
                                                  "result": "香港居民来往内地通行证"
                                                },
                                                {
                                                  "origin": 4,
                                                  "result": "澳门居民来往内地通行证"
                                                },
                                                {
                                                  "origin": 5,
                                                  "result": "台湾居民来往内地通行证"
                                                },
                                                {
                                                  "origin": 6,
                                                  "result": "中华人民共和国港澳居民居住证"
                                                },
                                                {
                                                  "origin": 7,
                                                  "result": "中华人民共和国台湾居民居住证"
                                                },
                                                {
                                                  "origin": 8,
                                                  "result": "外国人永久居留身份证"
                                                },
                                                {
                                                  "origin": 9,
                                                  "result": "中华人民共和国外国人就业许可证书"
                                                }
                                              ]
                                            }
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "5acfec38-bd84-4858-b5d6-c59b4eef34d1"
                                      },
                                      {
                                        "title": "证件姓名（旧值）",
                                        "dataIndex": [
                                          "source",
                                          "name"
                                        ],
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "5acfec38-bd84-4858-b5d6-c59b4eef34d1"
                                      },
                                      {
                                        "title": "证件姓名（新值）",
                                        "dataIndex": [
                                          "target",
                                          "name"
                                        ],
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "5acfec38-bd84-4858-b5d6-c59b4eef34d1"
                                      },
                                      {
                                        "title": "指定渠道（旧值）",
                                        "dataIndex": [
                                          "source",
                                          "payChannelName"
                                        ],
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "5acfec38-bd84-4858-b5d6-c59b4eef34d1"
                                      },
                                      {
                                        "title": "指定渠道（新值）",
                                        "dataIndex": [
                                          "target",
                                          "payChannelName"
                                        ],
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "5acfec38-bd84-4858-b5d6-c59b4eef34d1"
                                      },
                                      {
                                        "title": "操作时间",
                                        "dataIndex": "created",
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "5acfec38-bd84-4858-b5d6-c59b4eef34d1"
                                      },
                                      {
                                        "title": "操作人",
                                        "dataIndex": "operator",
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        }
                                      }
                                    ],
                                    "datasource": [],
                                    "rowKey": "",
                                    "aliasName": "表格",
                                    "size": "small",
                                    "sticky": false,
                                    "scroll": {
                                      "x": "100%"
                                    },
                                    "showIndex": true,
                                    "showSelection": false,
                                    "rowSelection": {
                                      "getCheckboxProps": ""
                                    },
                                    "disabledRow": {
                                      "exp": "",
                                      "background": ""
                                    },
                                    "backgroundRows": [
                                      {
                                        "exp": "",
                                        "background": ""
                                      }
                                    ],
                                    "summary": {
                                      "placement": "",
                                      "summarys": [
                                        {
                                          "label": "",
                                          "field": "",
                                          "value": "",
                                          "pipes": [
                                            {
                                              "pipe": "",
                                              "option": {}
                                            }
                                          ]
                                        }
                                      ],
                                      "payloads": [
                                        {
                                          "key": "",
                                          "source": "",
                                          "sourceKey": "",
                                          "decompose": false,
                                          "value": ""
                                        }
                                      ],
                                      "sumFields": "",
                                      "averageFields": ""
                                    },
                                    "rowExpandIconWidth": "",
                                    "mergeField": [],
                                    "mergeUniqueField": [],
                                    "colspanConfig": [
                                      {
                                        "exp": "",
                                        "fields": []
                                      }
                                    ],
                                    "fieldNames": {},
                                    "expandFetch": {
                                      "url": "",
                                      "method": "",
                                      "payloads": [
                                        {
                                          "key": "",
                                          "source": "",
                                          "sourceKey": "",
                                          "decompose": false,
                                          "value": ""
                                        }
                                      ],
                                      "headerPayloads": [
                                        {
                                          "key": "",
                                          "source": "",
                                          "sourceKey": "",
                                          "decompose": false,
                                          "value": ""
                                        }
                                      ]
                                    }
                                  },
                                  "modelValue": {
                                    "selectedRows": [],
                                    "datasource": [],
                                    "currentRow": {}
                                  },
                                  "actions": []
                                },
                                {
                                  "compName": "lyy-pagination",
                                  "compId": "c37e5af8-a893-4b2b-997b-a48a1a45d5c0",
                                  "compNameCn": "分页",
                                  "prop": {
                                    "fields": [
                                      "pageIndex",
                                      "pageSize"
                                    ],
                                    "aliasName": "分页",
                                    "defaultCurrent": 1,
                                    "defaultPageSize": 10,
                                    "total": 0,
                                    "size": "default",
                                    "hideOnSinglePage": null,
                                    "showQuickJumper": null,
                                    "showSizeChanger": null,
                                    "simple": null,
                                    "show": {
                                      "exp": ""
                                    },
                                    "formId": ""
                                  },
                                  "style": {},
                                  "actions": [
                                    {
                                      "event": "update",
                                      "action": "broadcast",
                                      "option": {
                                        "targetId": "4de8d07b-7f39-4695-96b5-0451752a1b34",
                                        "event": "mounted"
                                      }
                                    }
                                  ],
                                  "modelValue": [
                                    1,
                                    10
                                  ]
                                }
                              ]
                            }
                          ]
                        }
                      ],
                      "pageConfigureTemplateId": "1072202730649022464"
                    }
                  ]
                },
                {
                  "key": "byMerchant",
                  "title": "变更记录（按商户）",
                  "childrens": [
                    {
                      "compName": "lyy-container",
                      "compId": "d625226a-23d8-4206-8273-99504f320139",
                      "compNameCn": "变更记录-按收款人",
                      "prop": {},
                      "actions": [],
                      "childrens": [
                        {
                          "compName": "lyy-form",
                          "compId": "5be913c6-bade-467f-afc7-3d030a7d08e8",
                          "compNameCn": "表单",
                          "style": {},
                          "prop": {
                            "form": {
                              "pageIndex": 1,
                              "pageSize": 10,
                              "startTime": "2023-02-07",
                              "endTime": "2023-02-07",
                              "current": 1,
                              "size": 10
                            },
                            "aliasName": "表单",
                            "isSignChange": null,
                            "mainLayout": false,
                            "labelCol": {
                              "style": {
                                "width": "100px"
                              }
                            },
                            "class": ""
                          },
                          "modelValue": {},
                          "actions": [
                            {
                              "event": "mounted",
                              "action": "request",
                              "option": {
                                "url": "/finance/payChannel/withdraw/channel/merchant/record/pageList",
                                "method": "post",
                                "payloads": {
                                  "type": "dynamic",
                                  "dynamic": {
                                    "nodePath": "表单-5be913c6-bade-467f-afc7-3d030a7d08e8.modelValue"
                                  },
                                  "higher": [],
                                  "static": ""
                                },
                                "responseDataKey": "CRUD-5be913c6-bade-467f-afc7-3d030a7d08e8",
                                "customOption": {
                                  "loading": true,
                                  "codeValue": "0000000",
                                  "codeKey": "code",
                                  "messageKey": "message"
                                },
                                "headerPayloads": [],
                                "interceptors": {
                                  "requestInterceptor": "(config) => {\n  \n  return config;\n  }",
                                  "responseInterceptor": "(res) => {\n  \n  return res;\n  }"
                                }
                              },
                              "thenActions": [
                                {
                                  "event": "mounted",
                                  "action": "setNewValue",
                                  "option": {
                                    "to": {
                                      "type": "dynamic",
                                      "dynamic": {
                                        "nodePath": "ccadcb61-9358-48ac-b5ce-e0ded128180f.modelValue.datasource"
                                      }
                                    },
                                    "from": {
                                      "dynamic": {
                                        "nodePath": "CRUD-5be913c6-bade-467f-afc7-3d030a7d08e8.list"
                                      }
                                    }
                                  },
                                  "thenActions": [
                                    {
                                      "event": "mounted",
                                      "action": "setNewValue",
                                      "option": {
                                        "to": {
                                          "type": "dynamic",
                                          "dynamic": {
                                            "nodePath": "da850a55-a03e-473f-ab73-b4b3c46f8f44.prop.total"
                                          }
                                        },
                                        "from": {
                                          "dynamic": {
                                            "nodePath": "CRUD-5be913c6-bade-467f-afc7-3d030a7d08e8.total"
                                          }
                                        }
                                      },
                                      "thenActions": []
                                    }
                                  ]
                                }
                              ]
                            }
                          ],
                          "childrens": [
                            {
                              "compName": "lyy-search-pro",
                              "compId": "3dfe850b-8439-4529-aa0a-0a9c59ab3004",
                              "compNameCn": "搜索容器",
                              "prop": {
                                "searchFormList": [
                                  {
                                    "compName": "lyy-text-input",
                                    "compId": "d101f0a9-4c7e-4390-8962-2a0f40e0a136",
                                    "compNameCn": "输入框",
                                    "style": {},
                                    "prop": {
                                      "field": "adOrgId",
                                      "label": "商户ID",
                                      "aliasName": "文本输入框",
                                      "placeholder": "请输入",
                                      "defaultValue": "",
                                      "validateTrigger": "change",
                                      "maxlength": 50,
                                      "size": "default",
                                      "suffix": "",
                                      "addonAfter": "",
                                      "rules": [
                                        {
                                          "required": false,
                                          "message": "",
                                          "trigger": "",
                                          "pattern": ""
                                        }
                                      ],
                                      "disabled": false,
                                      "showCount": false,
                                      "allowClear": true,
                                      "labelCol": {
                                        "style": {}
                                      },
                                      "icon": {
                                        "iconName": "",
                                        "popover": {
                                          "description": []
                                        }
                                      },
                                      "formId": "",
                                      "show": {
                                        "exp": ""
                                      },
                                      "forbid": {
                                        "exp": ""
                                      }
                                    },
                                    "modelValue": "",
                                    "actions": []
                                  },
                                  {
                                    "compName": "lyy-text-input",
                                    "compId": "4ed2f76d-6d2f-4275-93f2-71f3cdbf5fe7",
                                    "compNameCn": "输入框",
                                    "style": {},
                                    "prop": {
                                      "field": "operator",
                                      "label": "操作人",
                                      "aliasName": "文本输入框",
                                      "placeholder": "请输入",
                                      "defaultValue": "",
                                      "validateTrigger": "change",
                                      "maxlength": 50,
                                      "size": "default",
                                      "suffix": "",
                                      "addonAfter": "",
                                      "rules": [
                                        {
                                          "required": false,
                                          "message": "",
                                          "trigger": "",
                                          "pattern": ""
                                        }
                                      ],
                                      "disabled": false,
                                      "showCount": false,
                                      "allowClear": true,
                                      "labelCol": {
                                        "style": {}
                                      },
                                      "icon": {
                                        "iconName": "",
                                        "popover": {
                                          "description": []
                                        }
                                      },
                                      "formId": "",
                                      "show": {
                                        "exp": ""
                                      },
                                      "forbid": {
                                        "exp": ""
                                      }
                                    },
                                    "modelValue": "",
                                    "actions": []
                                  },
                                  {
                                    "compName": "lyy-date-range",
                                    "compId": "d6d346d0-8f05-4ea9-8d15-43fb578d7eed",
                                    "compNameCn": "日期范围",
                                    "style": {},
                                    "prop": {
                                      "fields": [
                                        "startTime",
                                        "endTime"
                                      ],
                                      "label": "操作日期",
                                      "aliasName": "日期",
                                      "placeholder": [
                                        "开始日期",
                                        "结束日期"
                                      ],
                                      "defaultValue": "今天",
                                      "picker": "date",
                                      "valueFormat": "YYYY-MM-DD",
                                      "rules": [
                                        {
                                          "required": false,
                                          "message": "",
                                          "trigger": "",
                                          "pattern": ""
                                        }
                                      ],
                                      "disabledOption": {
                                        "unit": "year",
                                        "duration": 2
                                      },
                                      "fastOptions": [],
                                      "validateTrigger": "",
                                      "showNow": false,
                                      "showToday": false,
                                      "showTime": false,
                                      "allowClear": true,
                                      "size": "default",
                                      "disabled": false,
                                      "labelCol": {
                                        "style": {}
                                      },
                                      "icon": {
                                        "iconName": "",
                                        "popover": {
                                          "description": []
                                        }
                                      },
                                      "show": {
                                        "exp": ""
                                      },
                                      "forbid": {
                                        "exp": "",
                                        "payloads": [
                                          {
                                            "key": "",
                                            "source": "",
                                            "sourceKey": "",
                                            "decompose": false,
                                            "value": ""
                                          }
                                        ]
                                      },
                                      "field": ""
                                    },
                                    "actions": [],
                                    "modelValue": [
                                      "2023-02-07",
                                      "2023-02-07"
                                    ]
                                  }
                                ],
                                "aliasName": "搜索容器",
                                "formStorage": false
                              },
                              "actions": []
                            },
                            {
                              "compName": "lyy-card",
                              "compId": "021e2b54-8f51-4255-ba4c-306d2be7e178",
                              "compNameCn": "卡片",
                              "prop": {
                                "showHeader": false,
                                "title": "卡片容器",
                                "aliasName": "卡片容器",
                                "extraList": [],
                                "mainLayout": false,
                                "bordered": true
                              },
                              "actions": [],
                              "childrens": [
                                {
                                  "compName": "lyy-table",
                                  "compId": "ccadcb61-9358-48ac-b5ce-e0ded128180f",
                                  "field": "name",
                                  "compNameCn": "表格",
                                  "prop": {
                                    "columns": [
                                      {
                                        "title": "序号",
                                        "dataIndex": "_no",
                                        "width": 45,
                                        "fixed": "left"
                                      },
                                      {
                                        "title": "操作类型",
                                        "dataIndex": "operationType",
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "textmap",
                                            "option": {
                                              "textMap": [
                                                {
                                                  "origin": 1,
                                                  "result": "新增"
                                                },
                                                {
                                                  "origin": 2,
                                                  "result": "变更"
                                                }
                                              ]
                                            }
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "ccadcb61-9358-48ac-b5ce-e0ded128180f"
                                      },
                                      {
                                        "title": "商户ID",
                                        "dataIndex": "adOrgId",
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "ccadcb61-9358-48ac-b5ce-e0ded128180f"
                                      },
                                      {
                                        "title": "指定渠道（旧值）",
                                        "dataIndex": [
                                          "source",
                                          "payChannelName"
                                        ],
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "ccadcb61-9358-48ac-b5ce-e0ded128180f"
                                      },
                                      {
                                        "title": "指定渠道（新值）",
                                        "dataIndex": [
                                          "target",
                                          "payChannelName"
                                        ],
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "ccadcb61-9358-48ac-b5ce-e0ded128180f"
                                      },
                                      {
                                        "title": "操作时间",
                                        "dataIndex": "created",
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "ccadcb61-9358-48ac-b5ce-e0ded128180f"
                                      },
                                      {
                                        "title": "操作人",
                                        "dataIndex": "operator",
                                        "width": 120,
                                        "type": "text",
                                        "align": "left",
                                        "fixed": "",
                                        "replaceNull": "-",
                                        "separator": "",
                                        "actions": [],
                                        "sorter": false,
                                        "resizable": true,
                                        "headComp": [],
                                        "prevComp": [],
                                        "nextComp": [],
                                        "childrens": [],
                                        "option": {
                                          "format": "",
                                          "preappend": "",
                                          "append": ""
                                        },
                                        "joins": [],
                                        "pipes": [
                                          {
                                            "pipe": "",
                                            "option": {}
                                          }
                                        ],
                                        "colors": [
                                          {
                                            "exp": "",
                                            "color": ""
                                          }
                                        ],
                                        "show": {
                                          "exp": ""
                                        },
                                        "compId": "ccadcb61-9358-48ac-b5ce-e0ded128180f"
                                      }
                                    ],
                                    "datasource": [],
                                    "rowKey": "",
                                    "aliasName": "表格",
                                    "size": "small",
                                    "sticky": false,
                                    "scroll": {
                                      "x": "100%"
                                    },
                                    "showIndex": true,
                                    "showSelection": false,
                                    "rowSelection": {
                                      "getCheckboxProps": ""
                                    },
                                    "disabledRow": {
                                      "exp": "",
                                      "background": ""
                                    },
                                    "backgroundRows": [
                                      {
                                        "exp": "",
                                        "background": ""
                                      }
                                    ],
                                    "summary": {
                                      "placement": "",
                                      "summarys": [
                                        {
                                          "label": "",
                                          "field": "",
                                          "value": "",
                                          "pipes": [
                                            {
                                              "pipe": "",
                                              "option": {}
                                            }
                                          ]
                                        }
                                      ],
                                      "payloads": [
                                        {
                                          "key": "",
                                          "source": "",
                                          "sourceKey": "",
                                          "decompose": false,
                                          "value": ""
                                        }
                                      ],
                                      "sumFields": "",
                                      "averageFields": ""
                                    },
                                    "rowExpandIconWidth": "",
                                    "mergeField": [],
                                    "mergeUniqueField": [],
                                    "colspanConfig": [
                                      {
                                        "exp": "",
                                        "fields": []
                                      }
                                    ],
                                    "fieldNames": {},
                                    "expandFetch": {
                                      "url": "",
                                      "method": "",
                                      "payloads": [
                                        {
                                          "key": "",
                                          "source": "",
                                          "sourceKey": "",
                                          "decompose": false,
                                          "value": ""
                                        }
                                      ],
                                      "headerPayloads": [
                                        {
                                          "key": "",
                                          "source": "",
                                          "sourceKey": "",
                                          "decompose": false,
                                          "value": ""
                                        }
                                      ]
                                    }
                                  },
                                  "modelValue": {
                                    "selectedRows": [],
                                    "datasource": [],
                                    "currentRow": {}
                                  },
                                  "actions": []
                                },
                                {
                                  "compName": "lyy-pagination",
                                  "compId": "da850a55-a03e-473f-ab73-b4b3c46f8f44",
                                  "compNameCn": "分页",
                                  "prop": {
                                    "fields": [
                                      "pageIndex",
                                      "pageSize"
                                    ],
                                    "aliasName": "分页",
                                    "defaultCurrent": 1,
                                    "defaultPageSize": 10,
                                    "total": 0,
                                    "size": "default",
                                    "hideOnSinglePage": null,
                                    "showQuickJumper": null,
                                    "showSizeChanger": null,
                                    "simple": null,
                                    "show": {
                                      "exp": ""
                                    },
                                    "formId": ""
                                  },
                                  "style": {},
                                  "actions": [
                                    {
                                      "event": "update",
                                      "action": "broadcast",
                                      "option": {
                                        "targetId": "5be913c6-bade-467f-afc7-3d030a7d08e8",
                                        "event": "mounted"
                                      }
                                    }
                                  ],
                                  "modelValue": [
                                    1,
                                    10
                                  ]
                                }
                              ]
                            }
                          ]
                        }
                      ],
                      "pageConfigureTemplateId": "1072206957777645568"
                    }
                  ]
                }
              ],
              "mainLayout": true,
              "leftExtra": [],
              "rightExtra": []
            },
            "modelValue": 0,
            "pageConfigureTemplateId": "1072210557992038400"
          }
        ]
      }
    ]
  }
];
