<!--
 * @Description: 财务-风控-实收对账查看/实收金额对账
 * @Author: wanghai
 * @Date: 2020-05-20 09:23:41
 * @LastEditors  : wanghai
 * @LastEditTime : 2020-05-20 09:23:41
 -->
<template>
  <div class="realIncomeDetail">
    <!-- 对账 -->
    <template v-if="type==0">
        <el-row class="padding-bottom-10">
            <el-col :span="12" class="padding-right-10">
                <realIncomeCell :titleName="'业务数据-支付渠道'" :tableType="1" :status="3" :type="'sys'"></realIncomeCell>
            </el-col>
            <el-col :span="12" class="padding-left-10">
                <realIncomeCell :titleName="'导入数据-支付渠道'" :tableType="2" :status="3" :type="'third'"></realIncomeCell>
            </el-col>
        </el-row>
        <el-row class="padding-top-10">
            <el-col :span="24">
                <realIncomeCell :titleName="'勾兑详情-支付渠道'" :tableType="3" :type="'third'"></realIncomeCell>
            </el-col>
        </el-row>
        
    </template>

    <!-- 查看 -->
    <template v-else-if="type==1">
        <realIncomeCell :isTitle="false" :tableType="3" :type="'third'"></realIncomeCell>
    </template>
  </div>
</template>

<script>
import realIncomeCell from "./children/realIncomeCell";

  export default {
    name:"realIncomeDetail",
    components: {
        realIncomeCell
    },
    data () {
      return {
            type:'',//
      };
    },

    beforeCreate() {},

    created() {
        this.getParams(); 
    },

    beforeMount() {},

    mounted() {},

    beforeUpdate() {},

    updated() {},

    activated() {},

    deactivated() {},

    beforeDestroy() {},

    destroyed() {},

    errorCaptured() {},

    methods: {
        // 获取url参数
        getParams() {
            this.type = this.$route.query.type;
        },
    },

    computed: {},

    watch: {}

  }

</script>
<style scoped lang="less">

</style>