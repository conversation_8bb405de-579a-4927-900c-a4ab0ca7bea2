<!--
 * @Description: 系统操作记录
 * @Author:
 * @Date:
 * @LastEditors:
 * @LastEditTime:
 -->
<template>
  <div>
    <el-card>
      <div slot="header" class="clearfix">
        <el-form :inline="true" label-position="right" label-width="100px"
                 :model="formData"
                 ref="ruleForm">
          <el-form-item label="生效渠道：" prop="associatedId">
            <el-select v-model="formData.associatedId" placeholder="请选择" clearable>
              <el-option
                v-for="(item,index) in payChannelList"
                :key="index"
                :label="item.channelName"
                :value="item.payChannelId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="变更类型：" prop="type">
            <el-select v-model="formData.type" placeholder="请选择" clearable>
              <el-option
                v-for="(item,index) in typeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <!--按钮-->
          <el-form-item>
            <el-button type="primary" @click="queryBtn">查询</el-button>
            <el-button type="primary" @click="backBtn">返回</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <ul class="table">
          <li class="card" v-for="(item, index) in pageInfo.list" :key="index">
            <div class="header-wrapper">
              <p class="header-item">操作日期：{{item.createTime}}</p>
              <p class="header-item">操作人：{{item.operatorName}}</p>
              <p class="header-item">变更类型：{{arrGetValue(item.type, typeList, 'value' ,
                'label')}}</p>
              <p class="header-item" v-if="item.type === 3">商户归属：{{item.source.agentName}}</p>
              <p class="header-item" v-else>生效渠道：
                <template v-if="item.associatedId">{{arrGetValue(item.associatedId, payChannelList,
                  'payChannelId' , 'channelName')}}
                </template>
                <template v-else>全部</template>
              </p>
            </div>
            <div class="detail">
              <div class="prev">
                <p class="detail-item first">变更前（旧值）</p>
                <template v-if="item.source">
                  <template v-if="item.type === 1">
                    <p class="detail-item">对应业务类型：{{arrGetValue(item.source.businessType,
                      businessTypeList,
                      'value' , 'label')}}</p>
                    <p class="detail-item">渠道可用状态：{{arrGetValue(item.source.isActive, isActiveList,
                      'value' , 'label')}}</p>
                    <p class="detail-item">是否需要签约：{{item.source.isSign?'是':'否'}}</p>
                    <p class="detail-item">付款额度限制：
                      <template v-if="item.source.monthlyLimit">
                        单商户月累计额度不超{{item.source.monthlyLimit}}元
                      </template>
                      <template v-else>不限</template>
                    </p>
                  </template>
                  <template v-else-if="item.type === 2">
                    <p class="detail-item">{{getChannelText(item.source)}}</p>
                  </template>
                  <template v-else-if="item.type === 3">
                    <p class="detail-item">渠道: {{arrayToString(item.source.channelCodeNames)}}</p>
                    <p class="detail-item">顺序：{{arrayToString(item.source.orderNames)}}</p>
                    <p class="detail-item">推荐：{{arrayToString(item.source.recommentNames)}}</p>
                  </template>
                </template>

              </div>
              <div class="next">
                <p class="detail-item first">变更后（新值）</p>
                <template v-if="item.target">
                  <template v-if="item.type === 1">
                    <p class="detail-item">对应业务类型：{{arrGetValue(item.target.businessType,
                      businessTypeList,
                      'value' , 'label')}}</p>
                    <p class="detail-item">渠道可用状态：{{arrGetValue(item.target.isActive, isActiveList,
                      'value' , 'label')}}</p>
                    <p class="detail-item">是否需要签约：{{item.target.isSign?'是':'否'}}</p>
                    <p class="detail-item">付款额度限制：
                      <template v-if="item.target.monthlyLimit">
                        单商户月累计额度不超{{item.target.monthlyLimit}}元
                      </template>
                      <template v-else>不限</template>
                    </p>
                  </template>
                  <template v-else-if="item.type === 2">
                    <p class="detail-item">{{getChannelText(item.target)}}</p>
                  </template>
                  <template v-else-if="item.type === 3">
                    <p class="detail-item">渠道: {{arrayToString(item.target.channelCodeNames)}}</p>
                    <p class="detail-item">顺序：{{arrayToString(item.target.orderNames)}}</p>
                    <p class="detail-item">推荐：{{arrayToString(item.target.recommentNames)}}</p>
                  </template>
                </template>
              </div>
            </div>
          </li>
        </ul>
        <el-pagination
          :page-sizes="pageInfo.pageSizeArr"
          :page-size="pageInfo.pageSize"
          :total="pageInfo.total"
          :current-page="pageInfo.pageIndex"
          background
          layout="total, prev, pager, next, sizes, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
  import {getPaymentChannelList} from "@api/finance";
  import Table from '@/components/Table/index.vue'
  import {
    financePayChannelLogList,
  } from "@api/riskManagement/riskManagement";
  import {arrGetValue} from "@js/filter";

  export default {
    name: "payChannelLog",

    props: {},

    components: {
      Table
    },

    mixins: [],

    data() {
      return {
        column: [
          {
            label: '渠道',
            prop: 'channelNames',
          },
          {
            label: '商户归属',
            // width: 250,
            prop: 'agentName',
          },
        ],
        payChannelList: [],
        formData: {
          associatedId: null, // 生效渠道
          type: null, // 变更类型
        },
        typeList: [
          {label: '基础配置变更', value: 1},
          {label: '渠道优先级变更', value: 2},
          {label: '进件渠道配置', value: 3},
        ],
        businessTypeList: [
          {label: '不限', value: 0},
          {label: '在线支付', value: 1},
          {label: '平台补贴', value: 2},
        ],
        isActiveList: [
          {label: '禁用', value: 'N'},
          {label: '启用', value: 'Y'},
        ],
        pageInfo: {
          columns: [],
          pageSize: 10,
          total: 0,
          pageIndex: 1,
          loading: false,
          list: [],
          pageSizeArr: [10, 20, 30, 40],
        },
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.getPaymentChannelList();
      this.queryBtn();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      arrayToString(arr=[]){
        return arr.join('、') || '-'
      },
      async getPaymentChannelList() {
        const params = {
          pageSize: 999,
        }
        const res = await getPaymentChannelList(params);
        if (res && res.result === 0 && res.data && Array.isArray(res.data.items)) {
          this.payChannelList = res.data.items;
        }
      },
      queryBtn() {
        this.pageInfo.pageIndex = 1;
        this.getList();
      },
      backBtn() {
        this.$router.push({path: '/PaymentChannelIndex/PaymentChannelList'})
      },
      getPageParams() {
        return {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
          ...this.getParams(),
        };
      },
      getParams() {
        const params = {};
        if (this.formData.associatedId) {
          params.associatedId = this.formData.associatedId;
        }
        if (this.formData.type) {
          params.type = this.formData.type;
        }
        return params;
      },
      async getList() {
        this.pageInfo.loading = true;
        const res = await financePayChannelLogList(this.getPageParams());
        this.pageInfo.loading = false;
        if (res && res.result === 0 && res.data) {
          this.pageInfo.total = res.data.total || 0;
          this.pageInfo.list = res.data.items || [];
        }
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.getList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.getList();
      },
      arrGetValue(val, list, key, value) {
        return arrGetValue(val, list, key, value);
      },
      getChannelText(obj) {
        let list = [];
        let sortList = [];
        for (let i in obj) {
          if (Object.hasOwnProperty.call(obj, i)) {
            list.push({value: i, key: obj[i]});
          }
        }
        list = list.sort((a, b) => a.key - b.key);
        list.forEach(item => {
          if (item && item.value) {
            sortList.push(item.value);
          }
        });
        return sortList.join('>');
      },
    },

    computed: {},

    watch: {},
  }
</script>

<style lang="less" scoped>
  .table {

    .card {
      border: 1px solid #e5e5e5;

      &:not(:first-child) {
        margin-top: 20px;
      }

      .header-wrapper {
        display: flex;
        align-items: center;
        background-color: #e5e5e5;
        padding: 10px 20px;

        .header-item {
          flex-grow: 0;

          &:not(:first-child) {
            margin-left: 30px;
          }
        }
      }

      .detail {
        display: flex;
        position: relative;

        .prev, .next {
          flex-grow: 1;
          width: 0;
          padding: 10px 20px;

          .detail-item {
            padding: 10px;

            &.first {
              margin-bottom: 10px;
            }
          }
        }

        .prev {
        }

        .next {
        }

        &:before {
          content: '';
          position: absolute;
          width: 1px;
          top: 0;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          background-color: #e5e5e5;
        }
      }
    }
  }
</style>
