<template>
  <div class="riskStrategy">
    <el-radio-group v-model="userType" size="medium" class="margin-bottom-10" @change="changeUserType">
      <el-radio-button label="1">商家</el-radio-button>
      <el-radio-button label="2">代理商</el-radio-button>
    </el-radio-group>
    <el-card class="riskStrategy-wrapper" v-loading="loadingFlag">
      <div class="riskStrategy-content">
        <!--搜索-->
        <searchModule :search-list="searchList" @searchFun="searchFun"></searchModule>
        <div class="content">
          <!--表格-->
          <el-table
              :data="tableData"
              border
              style="width: 100%"
              >
            <el-table-column
                fixed
                prop="riskControlAdStrategyId"
                label="项目编号">
            </el-table-column>
            <el-table-column
                fixed
                prop="name"
                label="项目名称">
            </el-table-column>
            <el-table-column
                prop="rate"
                label="预计单份补贴成本（元）">
              <template slot-scope="scope">{{scope.row.amount==0 ? '系统每单的补贴金额加和' : scope.row.amount.toFixed(2)}}</template>
            </el-table-column>
            <el-table-column
                prop="remark"
                label="项目备注">
            </el-table-column>
            <el-table-column
                prop="created"
                label="创建时间"
                :formatter="formatDateFun">
            </el-table-column>
            <el-table-column
                fixed="right"
                label="操作">
              <template slot-scope="scope">
                <el-button @click="openEditDialogFun(scope.row)" type="text" size="small">编辑</el-button>
                <el-button @click="openSetDialogFun(scope.row)" type="text" size="small">策略配置</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
    <!--编辑-->
    <el-dialog title="项目编辑" :visible.sync="dialogFlag.editVisible">
      <el-form :model="editData" label-position="left" :rules="dialogFlag.editRules" ref="editForm" label-width="150px" v-loading.lock="loadingFlag">
        <el-form-item label="项目名称：">
          <el-col :span="8">
            <el-input class="inline-input" disabled v-model="editData.name"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="预计单份补贴成本：" prop="amount">
          <el-col :span="8">
            <el-tooltip class="item" effect="dark"
                        content="补贴成本必须大于0" placement="top-start">
              <el-input :disabled="!editData.amountEditFlag" class="inline-input" v-model="editData.amount" placeholder="" clearable="" autocomplete="off"></el-input>
            </el-tooltip>
          </el-col>
          <el-col :span="1"> 元</el-col>
        </el-form-item>
        <el-form-item label="项目备注：">
          <el-col :span="18">
            <el-input type="textarea" :rows="3" v-model="editData.remark" maxlength="50" show-word-limit clearable=""></el-input>
          </el-col>
        </el-form-item>
        <el-form-item>
          <el-button @click="dialogFlag.editVisible=false">取消</el-button>
          <el-button type="success" @click="editSaveFun">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--策略设置-->
    <el-dialog title="策略设置" :visible.sync="dialogFlag.setVisible">
      <el-form ref="setForm" :model="setData" label-position="left" :rules="dialogFlag.setRules" label-width="20px" v-loading.lock="loadingFlag">
        <div class="set-title">打款风控策略：</div>
        <div class="set-desc">设置后将控制商户账户余额增减</div>
        <div v-show="projectType==2">
          <el-form-item label="" prop="dailyAddFansControl">
            <el-col :span="23">
              <el-checkbox class="set-checkbox" @change="handleSetCheckChangeFun('dailyAddFansControl')" v-model="setData.dailyAddFansChecked"></el-checkbox>
              如果商家下属的某个设备单日吸粉数大于
              <el-input class="set-input" :disabled="!setData.dailyAddFansChecked" v-model="setData.dailyAddFansControl" clearable=""></el-input>
              个，则预警处理。
            </el-col>
          </el-form-item>
          <el-form-item label="" prop="dailyDiscountIncome">
            <el-col :span="23">
              <el-checkbox class="set-checkbox" @change="handleSetCheckChangeFun('dailyDiscountIncome')" v-model="setData.dailyDiscountChecked"></el-checkbox>
              当日机器广告收益 / 正常支付金额占比大于等于
              <el-input class="set-input" :disabled="!setData.dailyDiscountChecked" v-model="setData.dailyDiscountIncome" clearable=""></el-input>
              %，则预警处理。
            </el-col>
          </el-form-item>
          <el-form-item label="" prop="wxAddFansPMControlVO">
            <el-col :span="23">
              <el-checkbox class="set-checkbox" @change="handleSetCheckChangeFun('wxAddFansPMControlVO')" v-model="setData.wxAddFansPMChecked"></el-checkbox>
              商家下属的某台设备在
              <el-input class="set-input" :disabled="!setData.wxAddFansPMChecked" v-model="setData.wxAddFansPMControlVO.perMinute" clearable=""></el-input>
              分钟内吸粉数超过
              <el-input class="set-input" :disabled="!setData.wxAddFansPMChecked" v-model="setData.wxAddFansPMControlVO.addFansCount" clearable=""></el-input>
              个，则预警处理。
            </el-col>
          </el-form-item>
          <el-form-item label="" prop="useAdCoinsPMControlVO">
            <el-col :span="23">
              <el-checkbox class="set-checkbox" @change="handleSetCheckChangeFun('useAdCoinsPMControlVO')" v-model="setData.useAdCoinsPMChecked"></el-checkbox>
              商家下属的某台设备在
              <el-input class="set-input" :disabled="!setData.useAdCoinsPMChecked" v-model="setData.useAdCoinsPMControlVO.perMinute" clearable=""></el-input>
              分钟内使用的广告币超过
              <el-input class="set-input" :disabled="!setData.useAdCoinsPMChecked" v-model="setData.useAdCoinsPMControlVO.useAdCoinsCount" clearable=""></el-input>
              个，则预警处理。
            </el-col>
          </el-form-item>
          <el-form-item label="" prop="averageDailyAddFans">
            <el-col :span="23">
              <el-checkbox class="set-checkbox" @change="handleSetCheckChangeFun('averageDailyAddFans')" v-model="setData.averageDailyChecked"></el-checkbox>
              如果商家下设备平均单日吸粉量大于
              <el-input class="set-input" :disabled="!setData.averageDailyChecked" v-model="setData.averageDailyAddFans" clearable=""></el-input>
              个，则预警处理。
            </el-col>
          </el-form-item>
        </div>
        <el-form-item label="" prop="dosageRiskControl">
          <el-col :span="23">
            <el-checkbox class="set-checkbox" @change="handleSetCheckChangeFun('dosageRiskControl')" v-model="setData.dosageChecked"></el-checkbox>
            每日核销量/每日分发量  >
            <el-input class="set-input" :disabled="!setData.dosageChecked" v-model="setData.dosageRiskControl" clearable=""></el-input>
            ，则预警处理。
          </el-col>
        </el-form-item>
        <el-form-item label="" prop="subsidyRiskControl">
          <el-col :span="23">
            <el-checkbox class="set-checkbox" @change="handleSetCheckChangeFun('subsidyRiskControl')" v-model="setData.subsidyChecked"></el-checkbox>
            每日实际补贴成本/ 每日预计补贴成本  >
            <el-input class="set-input" :disabled="!setData.subsidyChecked" v-model="setData.subsidyRiskControl" clearable=""></el-input>
            ，则预警处理。
          </el-col>
        </el-form-item>
        <el-form-item label="" prop="withdrawalRiskControl">
          <el-col :span="23">
            <el-checkbox class="set-checkbox" @change="handleSetCheckChangeFun('withdrawalRiskControl')" v-model="setData.withdrawChecked"></el-checkbox>
            商户每日预计打款上限 >
            <el-input class="set-input" :disabled="!setData.withdrawChecked" v-model="setData.withdrawalRiskControl" clearable=""></el-input>
            ，则预警处理。
          </el-col>
        </el-form-item>
        <el-form-item>
          <el-button @click="dialogFlag.setVisible=false">取消</el-button>
          <el-button type="success" @click="setSaveFun">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import searchModule from "@components/searchModule/searchModule";
import { formatDate} from "@js/utils";
import {getAdStrategyList, postAddStrategy, postUpdateModule} from "@api/riskManagement/riskManagement";
import {validateMoney, validatePInt} from "@js/validate";
export default {
  name: 'ristStrategy',
  data() {
    let edittValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入单份补贴成本'));
      } else if( !validateMoney(value) ){
        callback(new Error('单份补贴成本必须大于0的两位小数'));
      } else {
        callback();
      }
    };
    let setValidator1 = (rule, value, callback) => {
      if (!this.setData.dailyDiscountChecked) {
        callback();
      } else if (!value) {
        callback(new Error('请输入数值'));
      } else if( !validateMoney(value) ){
        callback(new Error('输入的数值必须大于0的两位小数'));
      } else {
        callback();
      }
    };
    let setValidator2 = (rule, value, callback) => {
      if (!this.setData.averageDailyChecked) {
        callback();
      } else if (!value) {
        callback(new Error('请输入数值'));
      } else if( !validateMoney(value) ){
        callback(new Error('输入的数值必须大于0的两位小数'));
      } else {
        callback();
      }
    };
    let setValidator3 = (rule, value, callback) => {
      if (!this.setData.subsidyChecked) {
        callback();
      } else if (!value) {
        callback(new Error('请输入数值'));
      } else if( !validateMoney(value) ){
        callback(new Error('输入的数值必须大于0的两位小数'));
      } else {
        callback();
      }
    };
    let setValidator4 = (rule, value, callback) => {
      if (!this.setData.withdrawChecked) {
        callback();
      } else if (!value) {
        callback(new Error('请输入数值'));
      } else if( !validateMoney(value) ){
        callback(new Error('输入的数值必须大于0的两位小数'));
      } else {
        callback();
      }
    };
    let setValidator5 = (rule, value, callback) => {
      if (!this.setData.dailyDiscountChecked) {
        callback();
      } else if (!value) {
        callback(new Error('请输入数值'));
      } else if( !validateMoney(value) ){
        callback(new Error('输入的数值必须大于0的两位小数'));
      } else {
        callback();
      }
    };
    let setNumberValidator = (rule, value, callback) => {
      if (!this.setData.dailyAddFansChecked) {
        callback();
      } else if (!value) {
        callback(new Error('请输入数值'));
      } else if( !validatePInt(value) ){
        callback(new Error('输入的数值必须大于0的整数'));
      } else {
        callback();
      }
    };
    let setObjectValidator1 = (rule, value, callback) => {
      if (!this.setData.wxAddFansPMChecked) {
        callback();
      } else if (!value.addFansCount && !value.perMinute) {
        callback(new Error('请输入数值'));
      } else if( !validatePInt(value.addFansCount) ){
        callback(new Error('输入的数值必须大于0的整数'));
      } else if( !validatePInt(value.perMinute) ){
        callback(new Error('输入的数值必须大于0的整数'));
      } else {
        callback();
      }
    };
    let setObjectValidator2 = (rule, value, callback) => {
      if (!this.setData.useAdCoinsPMChecked) {
        callback();
      } else if (!value.useAdCoinsCount && !value.perMinute) {
        callback(new Error('请输入数值'));
      } else if( !validatePInt(value.useAdCoinsCount) ){
        callback(new Error('输入的数值必须大于0的整数'));
      } else if( !validatePInt(value.perMinute) ){
        callback(new Error('输入的数值必须大于0的整数'));
      } else {
        callback();
      }
    };
    return {
      userType: 1, // 默认商家类型 1-商家 2-代理商
      // 搜索模块
      searchList: [
        {
          type: 'input', // 检索类型,输入框
          label: '项目编号：', // label
          placeholder: '', // 占位符
          valueName: 'code', // 返回对应值名
        },
        {
          type: 'input', // 检索类型,输入框
          label: '项目名称：', // label
          placeholder: '', // 占位符
          valueName: 'name', // 返回对应值名
        },
      ],
      loadingFlag: false, // 加载flag
      // 表格数据
      tableData: [],
      // 表单
      formData: {
        code: '',
        name: '',
        riskOrgType: 'D', // D-商家 A-代理商
      },
      // 弹窗
      dialogFlag: {
        editVisible: false,
        editRules: {
          amount: [
            { validator: edittValidator, trigger: 'blur' }
          ],
        },
        setVisible: false,
        setRules: {
          dosageRiskControl: [
            { validator: setValidator1, trigger: 'blur' }
          ],
          averageDailyAddFans: [
            { validator: setValidator2, trigger: 'blur' }
          ],
          subsidyRiskControl: [
            { validator: setValidator3, trigger: 'blur' }
          ],
          withdrawalRiskControl: [
            { validator: setValidator4, trigger: 'blur' }
          ],
          dailyDiscountIncome: [
            { validator: setValidator5, trigger: 'blur' }
          ],
          dailyAddFansControl: [
            { validator: setNumberValidator, trigger: 'blur' }
          ],
          wxAddFansPMControlVO: [
            { validator: setObjectValidator1, trigger: 'blur' }
          ],
          useAdCoinsPMControlVO: [
            { validator: setObjectValidator2, trigger: 'blur' }
          ],
        }
      },
      // 编辑
      editData: {
        name: '',
        amount: '',
        remark: '',
        amountEditFlag: false,
      },
      // 策略设置
      setData: {
        dosageRiskControl: '', //类型：Number  可有字段  备注：* 用量风控 * 每日核销量/每日分发量
        subsidyRiskControl: '',  //类型：Number  可有字段  备注：* 补贴风控 * 每日实际补贴/每日预计补贴成本
        withdrawalRiskControl: '', //类型：Number  可有字段  备注：* 提现风控 * 商户每日预计打款
        dosageChecked: false, // 用量
        subsidyChecked: false, // 津贴
        withdrawChecked: false, // 提现
        dailyAddFansChecked: false, // 单日增粉
        dailyDiscountChecked: false, // 增粉金额比
        averageDailyChecked: false, // 平均单日增粉
        useAdCoinsPMChecked: false, // 广告币
        wxAddFansPMChecked: false, // 增粉规则
        dailyAddFansControl: '',  // 类型：Number  可有字段  备注：* 单日增粉数控制
        dailyDiscountIncome: '',  // 类型：Number  可有字段  备注：* 单日机器广告收益/正常支付金额占比
        averageDailyAddFans: '',  // 类型：Number  可有字段  备注： * 平均单日吸粉量
        useAdCoinsPMControlVO:{ // 类型：Object  可有字段  备注：* 使用广告币预警规则 （颗粒度为 min） PM(Per Minute)
          perMinute: '',  // 类型：Number  可有字段  备注：无
          useAdCoinsCount: '' // 类型：Number  可有字段  备注：无
        },
        wxAddFansPMControlVO: { // 类型：Object  可有字段  备注：* 设备增粉数预警规则 （颗粒度为 min） PM(Per Minute)
          perMinute: '',  // 类型：Number  可有字段  备注：无
          addFansCount: ''  // 类型：Number  可有字段  备注：无
        }
      },
      projectType: 1, // 项目类型
    }
  },
  created() {
    this.getAdStrategyListFun();
  },
  components: {
    searchModule
  },
  methods: {
    // 校验规则
    handleSetCheckChangeFun(tags) {
      this.$refs.setForm.validateField(tags);
    },
    // 搜索
    async searchFun(datas){
      console.log(datas)
      if(JSON.stringify(datas.formData) == "{}"){
        return false
      }
      this.formData = Object.assign(this.formData,datas.formData);
      await this.getAdStrategyListFun();
    },
    // 格式化日期
    formatDateFun(row) {
      return formatDate(row.created, 'yyyy-MM-dd hh:mm:ss')
    },
    // 获取项目列表
    async getAdStrategyListFun() {
      let params = Object.assign({
        // pageIndex: this.pagination.pageIndex,
        // pageSize: this.pagination.pageSize,
      }, this.formData)
      this.loadingFlag = true
      const res = await getAdStrategyList(params)
      this.loadingFlag = false;
      if (res.result == '0') {
        // this.pagination.total = res.data.total;
        this.tableData = res.data
      }
    },
    // 编辑
    openEditDialogFun(item) {
      this.dialogFlag.editVisible = true;
      this.editData = Object.assign(this.editData, item);
      this.editData.amountEditFlag = item.amount>0;
    },
    // 保存编辑
    async editSaveFun() {
      if(this.editData.amountEditFlag && !validateMoney(this.editData.amount)){
        this.$message.error('预计单份补贴成本');
        return false
      }
      const params = {
        riskControlAdStrategyId: this.editData.riskControlAdStrategyId,
        amount: this.editData.amount,
        remark: this.editData.remark
      }
      this.loadingFlag = true;
      const res = await postUpdateModule(params);
      this.loadingFlag = false;
      if(res.result==0){
        // 刷新列表
        await this.getAdStrategyListFun();
        this.dialogFlag.editVisible = false;
        this.$message({
          message: '保存成功',
          type: 'success'
        });
      }
    },
    openSetDialogFun(item){
      this.dialogFlag.setVisible = true;
      this.projectType = item.name == '涨粉'? 2: 1
      const datas = item.rule ? JSON.parse(item.rule) : ''
      const common =  datas.common || ''
      this.setData = Object.assign(this.setData,{
        id: item.riskControlAdStrategyId,
        type: this.projectType,
        dosageChecked: common.dosageRiskControl > 0,
        dosageRiskControl: common.dosageRiskControl || '',
        subsidyChecked: common.subsidyRiskControl > 0,
        subsidyRiskControl: common.subsidyRiskControl || '',
        withdrawChecked: common.withdrawalRiskControl > 0,
        withdrawalRiskControl: common.withdrawalRiskControl || ''
      })
      if(this.projectType == 2) {
        const bussiness = datas.business || ''
        this.setData = Object.assign(this.setData, {
          dailyAddFansChecked: bussiness.dailyAddFansControl > 0, // 单日增粉
          dailyDiscountChecked: bussiness.dailyDiscountIncome >0, // 增粉金额比
          averageDailyChecked: bussiness.averageDailyAddFans > 0, // 平均单日增粉
          useAdCoinsPMChecked: bussiness.useAdCoinsPMControlVO && bussiness.useAdCoinsPMControlVO.useAdCoinsCount > 0, // 广告币
          wxAddFansPMChecked: bussiness.wxAddFansPMControlVO && bussiness.wxAddFansPMControlVO.addFansCount > 0, // 增粉规则
          dailyAddFansControl: bussiness.dailyAddFansControl || '',  // 类型：Number  可有字段  备注：* 单日增粉数控制
          dailyDiscountIncome: bussiness.dailyDiscountIncome || '',  // 类型：Number  可有字段  备注：* 单日机器广告收益/正常支付金额占比
          averageDailyAddFans: bussiness.averageDailyAddFans || '',  // 类型：Number  可有字段  备注： * 平均单日吸粉量
          useAdCoinsPMControlVO:{ // 类型：Object  可有字段  备注：* 使用广告币预警规则 （颗粒度为 min） PM(Per Minute)
            perMinute: bussiness.useAdCoinsPMControlVO ? bussiness.useAdCoinsPMControlVO.perMinute: '',  // 类型：Number  可有字段  备注：无
            useAdCoinsCount: bussiness.useAdCoinsPMControlVO ? bussiness.useAdCoinsPMControlVO.useAdCoinsCount: '' // 类型：Number  可有字段  备注：无
          },
          wxAddFansPMControlVO: { // 类型：Object  可有字段  备注：* 设备增粉数预警规则 （颗粒度为 min） PM(Per Minute)
            perMinute: bussiness.wxAddFansPMControlVO ? bussiness.wxAddFansPMControlVO.perMinute: '',  // 类型：Number  可有字段  备注：无
            addFansCount: bussiness.wxAddFansPMControlVO ? bussiness.wxAddFansPMControlVO.addFansCount: ''  // 类型：Number  可有字段  备注：无
          }
        })
      }
    },
    // 保存
    async setSaveFun() {
      let params = {
        id: this.setData.id,
        type: this.setData.type,
      }
      if(this.setData.dosageChecked){
        params.dosageRiskControl = this.setData.dosageRiskControl;
        if(!validateMoney(params.dosageRiskControl)){
          this.$message.error('请输入正确的用量风控数值');
          return false
        }
      }
      if(this.setData.subsidyChecked){
        params.subsidyRiskControl = this.setData.subsidyRiskControl;
        if(!validateMoney(params.subsidyRiskControl)){
          this.$message.error('请输入正确的补贴风控数值');
          return false
        }
      }
      if(this.setData.withdrawChecked){
        params.withdrawalRiskControl = this.setData.withdrawalRiskControl;
        if(!validateMoney(params.withdrawalRiskControl)){
          this.$message.error('请输入正确的提现风控数值');
          return false
        }
      }
      // 增粉
      if(this.setData.dailyAddFansChecked){
        params.dailyAddFansControl = this.setData.dailyAddFansControl;
        if(!validatePInt(params.dailyAddFansControl)){
          this.$message.error('请输入正确的单日设备增粉数值');
          return false
        }
      }
      if(this.setData.dailyDiscountChecked){
        params.dailyDiscountIncome = this.setData.dailyDiscountIncome;
        if(!validateMoney(params.dailyDiscountIncome)){
          this.$message.error('请输入正确的增粉金额比数值');
          return false
        }
      }
      if(this.setData.averageDailyChecked){
        params.averageDailyAddFans = this.setData.averageDailyAddFans;
        if(!validatePInt(params.averageDailyAddFans)){
          this.$message.error('请输入正确的平均增粉数数值');
          return false
        }
      }
      if(this.setData.useAdCoinsPMChecked){
        params.useAdCoinsPMControlVO = {
          perMinute: this.setData.useAdCoinsPMControlVO.perMinute,
          useAdCoinsCount: this.setData.useAdCoinsPMControlVO.useAdCoinsCount
        };
        if(!validatePInt(params.useAdCoinsPMControlVO.perMinute)){
          this.$message.error('请输入正确的使用广告币分钟数');
          return false
        }
        if(!validatePInt(params.useAdCoinsPMControlVO.useAdCoinsCount)){
          this.$message.error('请输入正确的使用广告币个数');
          return false
        }
      }
      if(this.setData.wxAddFansPMChecked){
        params.wxAddFansPMControlVO = {
          perMinute: this.setData.wxAddFansPMControlVO.perMinute,
          addFansCount: this.setData.wxAddFansPMControlVO.addFansCount
        };
        if(!validatePInt(params.wxAddFansPMControlVO.perMinute)){
          this.$message.error('请输入正确的吸粉分钟数');
          return false
        }
        if(!validatePInt(params.wxAddFansPMControlVO.addFansCount)){
          this.$message.error('请输入正确的吸粉个数');
          return false
        }
      }
      const res = await postAddStrategy(params);
      if(res.result == 0){
        // 刷新列表
        await this.getAdStrategyListFun();
        this.dialogFlag.setVisible = false;
        this.$message({
          message: '保存成功',
          type: 'success'
        });
      }
    },
    // 选择用户类型
    changeUserType(val) {
      if (val === '1') {
        this.formData.riskOrgType = 'D';
        this.getAdStrategyListFun();
      } else {
        this.formData.riskOrgType = 'A';
        this.getAdStrategyListFun();
      }
    },
  }
};
</script>

<style lang="less" scoped>
  /deep/.el-card__body{
    height:100%;
  }
  .riskStrategy{
    height:100%;
    /deep/.el-card{
      height:100%;
    }
    .riskStrategy-content{
      height:100%;
      display: flex;
      flex-direction: column;
    }
    .content{
      flex:1;
      overflow-y: auto;
    }
  }
  .set-checkbox{
    margin-right:10px;
  }
  .set-title{
    font-size:16px;
  }
  .set-desc{
    font-size:14px;
    color:red;
  }
  .set-input{
    width:100px;
  }
</style>
