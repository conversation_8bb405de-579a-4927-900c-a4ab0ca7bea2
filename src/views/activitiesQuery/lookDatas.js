export const basicsDatas = [
  {
    prop: 'promotionActivityId',
    label: '活动ID'
  },
  {
    prop: 'typeName',
    label: '产品名称'
  },
  {
    prop: 'created',
    label: '开通时间'
  },
  {
    prop: 'parameter',
    slot: 'parameter',
    label: '参数配置'
  },
  {
    prop: 'lyyEquipmentTypeNames',
    label: '使用设备'
  },
  {
    prop: 'activityStatusName',
    label: '状态'
  },
  {
    prop: 'hisRevenueAmount',
    label: '历史营收'
  },
  {
    prop: 'hisGainAmount',
    label: '增益营收'
  },
  {
    prop: 'hisUpgradeCounts',
    label: '历史升单次数'
  },
  {
    prop: 'hisUnitUpgradePrice',
    label: '历史人均单价'
  },
  {
    prop: 'hisAvgGainAmount',
    label: '历史人均增益'
  }
];

export const datasComparison = [
  {
    prop: 'time',
    label: '时间'
  },
  {
    prop: 'perpayUser',
    label: '综合客单价'
  },
  {
    prop: 'perpayOrderNew',
    label: '新客客单价'
  },
  {
    prop: 'perpayOrderOld',
    label: '老客客单价'
  },
  {
    prop: 'perUserOrder',
    label: '人均消费笔数'
  },
  {
    prop: 'package20',
    label: '20元以上套餐占比'
  },
]

export const paymentColumn = [
  {
    prop: 'day',
    label: '支付时间'
  },
  {
    prop: 'userId',
    label: '用户ID'
  },
  {
    prop: 'isNewOrOldUser',
    isHide: true,
    label: '新/老客'
  },
  {
    prop: 'guidMess',
    isHide: true,
    slot: 'guidMess',
    label: '引导依据'
  },
  {
    prop: 'guidBasis',
    isHide: true,
    slot: 'guidBasis',
    label: '依据数直'
  },
  {
    prop: 'originalAmount',
    label: '用户选择套餐价'
  },
  {
    prop: 'orderUpgradeAmount',
    label: '引导套餐价'
  },
  {
    prop: 'gainAmount',
    label: '增益营收'
  },
]