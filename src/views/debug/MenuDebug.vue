<!--
 * @Description: 菜单调试页面
 * @Author: AI Assistant
 * @Date: 2025/09/19
 -->
<template>
  <div class="menu-debug">
    <el-card>
      <div slot="header">
        <span>菜单调试工具</span>
      </div>

      <el-row :gutter="20">
        <el-col :span="12">
          <h3>当前菜单状态</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="菜单组件">
              {{ menuComponent ? '已找到' : '未找到' }}
            </el-descriptions-item>
            <el-descriptions-item label="菜单列表长度">
              {{ menuList ? menuList.length : 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="智能体菜单">
              {{ hasAiAgentMenu ? '存在' : '不存在' }}
            </el-descriptions-item>
          </el-descriptions>

          <div style="margin-top: 20px;">
            <el-button type="primary" @click="refreshMenuInfo">刷新信息</el-button>
            <el-button type="success" @click="forceAddMenu">强制添加菜单</el-button>
            <el-button type="warning" @click="testMenu">测试菜单</el-button>
          </div>
        </el-col>

        <el-col :span="12">
          <h3>操作日志</h3>
          <div class="log-container">
            <div
              v-for="(log, index) in logs"
              :key="index"
              :class="['log-item', log.type]"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-divider>菜单列表详情</el-divider>

      <el-table :data="menuTableData" border style="width: 100%">
        <el-table-column prop="name" label="菜单名称" />
        <el-table-column prop="value" label="菜单值" />
        <el-table-column prop="path" label="路径" />
        <el-table-column prop="icon" label="图标" />
        <el-table-column prop="hide" label="是否隐藏" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="navigateToMenu(scope.row)"
              v-if="scope.row.path"
            >
              跳转
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MenuDebug',
  data() {
    return {
      menuComponent: null,
      menuList: [],
      logs: [],
      refreshTimer: null
    }
  },
  computed: {
    hasAiAgentMenu() {
      return this.menuList.some(menu =>
        menu.value === 'AiAgentManage' || menu.path === '/aiAgent'
      )
    },
    menuTableData() {
      return this.menuList.map(menu => ({
        name: menu.name || '-',
        value: menu.value || '-',
        path: menu.path || '-',
        icon: menu.icon || '-',
        hide: menu.hide || 'N'
      }))
    }
  },
  mounted() {
    this.refreshMenuInfo()
    // 定时刷新菜单信息
    this.refreshTimer = setInterval(() => {
      this.refreshMenuInfo()
    }, 2000)
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },
  methods: {
    addLog(message, type = 'info') {
      const time = new Date().toLocaleTimeString()
      this.logs.unshift({ time, message, type })
      // 只保留最近20条日志
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20)
      }
    },

    findMenuComponent() {
      const app = document.querySelector('#app').__vue__
      if (app && app.$children && app.$children[0]) {
        const layout = app.$children[0]

        function findComponent(component) {
          if (component.menuList !== undefined) {
            return component
          }
          if (component.$children) {
            for (const child of component.$children) {
              const found = findComponent(child)
              if (found) return found
            }
          }
          return null
        }

        return findComponent(layout)
      }
      return null
    },

    refreshMenuInfo() {
      this.menuComponent = this.findMenuComponent()
      if (this.menuComponent) {
        this.menuList = this.menuComponent.menuList || []
      } else {
        this.menuList = []
      }
    },

    forceAddMenu() {
      if (!this.menuComponent) {
        this.addLog('未找到菜单组件，无法添加菜单', 'error')
        return
      }

      // 确保menuList是数组
      if (!Array.isArray(this.menuComponent.menuList)) {
        this.menuComponent.menuList = []
      }

      // 检查是否已存在
      const exists = this.menuComponent.menuList.some(menu =>
        menu.value === 'AiAgentManage' || menu.path === '/aiAgent'
      )

      if (!exists) {
        const aiAgentMenu = {
          id: 'ai-agent-debug-' + Date.now(),
          authMenuId: 'ai-agent-debug-' + Date.now(),
          name: '智能体应用',
          value: 'AiAgentManage',
          path: '/aiAgent',
          icon: 'el-icon-cpu',
          hide: 'N',
          children: [] // 改为空数组而不是null
        }

        this.menuComponent.menuList.push(aiAgentMenu)
        this.menuComponent.$forceUpdate()
        this.addLog('强制添加智能体应用菜单成功', 'success')
        this.refreshMenuInfo()
      } else {
        this.addLog('智能体应用菜单已存在', 'warning')
      }
    },

    testMenu() {
      if (typeof window.testAiAgentMenu === 'function') {
        const result = window.testAiAgentMenu()
        this.addLog(`菜单测试结果: ${result.message}`, result.menuExists ? 'success' : 'error')
      } else {
        this.addLog('测试方法不可用', 'error')
      }
    },

    navigateToMenu(menu) {
      if (menu.path && menu.path !== '-') {
        this.$router.push(menu.path)
        this.addLog(`跳转到: ${menu.path}`, 'info')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.menu-debug {
  padding: 20px;

  .log-container {
    height: 300px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    background-color: #f5f7fa;

    .log-item {
      margin-bottom: 5px;
      font-size: 12px;

      .log-time {
        color: #909399;
        margin-right: 10px;
      }

      .log-message {
        color: #606266;
      }

      &.success .log-message {
        color: #67c23a;
      }

      &.error .log-message {
        color: #f56c6c;
      }

      &.warning .log-message {
        color: #e6a23c;
      }
    }
  }
}
</style>
