<!--
日期：2019/12/3
功能：模板组件
作者：杨楠锋
-->
<template>
  <div v-loading="pageInfo.loading">
    <el-card>
      <div class="operation">
<!--        1076731-->
<!--        15899974725-->
        <el-form :inline="true"
                 label-position="right"
                 :model="formData"
                 ref="ruleForm"
                 autocomplete="on"
                 :rules="rules"
                 label-width="85px">
          <el-form-item label="用户ID：" prop="lyyUserId">
            <el-input class="inline-input" v-model.trim="formData.lyyUserId" placeholder="请输入用户ID"
                      @keyup.native="idChange()"
                      autocomplete="on"
                      clearable></el-input>
          </el-form-item>
          <el-form-item label="设备编号/商户账号:" label-width="140px">
            <el-input class="inline-input" v-model.trim="formData.value" placeholder="请输入设备编号/商户账号"
                      clearable></el-input>
          </el-form-item>
          <!--按钮-->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="queryBtn">查询</el-button>
            <el-button type="primary" icon="el-icon-s-tools" @click="adjustBtn">调整余额余币</el-button>
          </el-form-item>
        </el-form>
        <el-button class="record" type="primary" icon="el-icon-document" @click="record()">调整记录
        </el-button>
      </div>
      <div>
        <!--列表 start-->
        <el-table :data="pageInfo.list" border highlight-current-row
                  style="width: 100%;margin-bottom: 20px;"
                  :span-method="objectSpanMethod"
                  >
          <template v-for="(item, index) in colums">
            <el-table-column v-if="item.key === 'operation'"
                             :key="index"
                             :prop="item.key"
                             :label="item.label"
                             :width="item.width"
                             :sortable="item.sortable"
                             align="center">
              <template slot-scope="scope">
                <div>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-else-if="item.key === 'amount'"
                             :key="index"
                             :prop="item.key"
                             :label="item.label"
                             :width="item.width"
                             :sortable="item.sortable"
                             align="center">
              <template slot-scope="scope">
                <div>
                  <p v-if="scope.row.amount || scope.row.amount === 0">{{scope.row.amount}}</p>
                  <p v-else>0</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-else
              :key="index"
              :prop="item.key"
              :label="item.label"
              :width="item.width"
              :sortable="item.sortable"
              align="center"
            />
          </template>
        </el-table>
        <!--列表 end-->
      </div>
      <user-balance-record-dialog ref="recordRef"></user-balance-record-dialog>
      <user-balance-adjust-dialog ref="adjustRef" @allEmpty="openAllEmpty"
                                  @empty="openEmpty"
                                  @emptySingle="openEmptySingle"></user-balance-adjust-dialog>
      <user-balance-all-empty-dialog ref="allEmptyRef"
                                     @save="openAllEmptyConfirm"></user-balance-all-empty-dialog>
      <user-balance-all-empty-confirm-dialog ref="allEmptyConfirmRef"
                                             @save="allEmptyConfirm"
                                             @cancel="allEmptyConfirmCancel"></user-balance-all-empty-confirm-dialog>
      <user-balance-empty-dialog ref="emptyRef"
                                 @save="openEmptyConfirm"></user-balance-empty-dialog>
      <user-balance-empty-confirm-dialog ref="emptyConfirmRef" @save="emptyConfirm"
                                         @cancel="emptyConfirmCancel"></user-balance-empty-confirm-dialog>
      <user-balance-adjust-empty-single-dialog ref="emptySingleRef"
                                               @save="emptySingleConfirm"></user-balance-adjust-empty-single-dialog>
      <user-balance-adjust-empty-single-error-dialog
        ref="emptySingleErrorRef"
        @save="emptySingleErrorConfirm"
        @cancel="emptySingleErrorConfirmCancel"></user-balance-adjust-empty-single-error-dialog>
    </el-card>
  </div>
</template>

<script>
    import {userBalanceQuery, userBalanceUpdate} from '@api/baseManager/userBalance';
    import UserBalanceRecordDialog
        from '@views/baseManager/userBalance/children/userBalanceRecordDialog';
    import UserBalanceAdjustDialog
        from '@views/baseManager/userBalance/children/userBalanceAdjustDialog';
    import UserBalanceAllEmptyDialog
        from '@views/baseManager/userBalance/children/userBalanceAllEmptyDialog';
    import UserBalanceAllEmptyConfirmDialog
        from '@views/baseManager/userBalance/children/userBalanceAllEmptyConfirmDialog';
    import UserBalanceEmptyDialog
        from '@views/baseManager/userBalance/children/userBalanceEmptyDialog';
    import UserBalanceEmptyConfirmDialog
        from '@views/baseManager/userBalance/children/userBalanceEmptyConfirmDialog';
    import UserBalanceAdjustEmptySingleDialog
        from '@views/baseManager/userBalance/children/userBalanceAdjustEmptySingleDialog';
    import UserBalanceAdjustEmptySingleErrorDialog
        from '@views/baseManager/userBalance/children/userBalanceAdjustEmptySingleErrorDialog';

    export default {
        name: 'userBalance',
        props: {},
        components: {
            UserBalanceAdjustEmptySingleErrorDialog,
            UserBalanceAdjustEmptySingleDialog,
            UserBalanceEmptyConfirmDialog,
            UserBalanceEmptyDialog,
            UserBalanceAllEmptyConfirmDialog,
            UserBalanceAllEmptyDialog, UserBalanceAdjustDialog, UserBalanceRecordDialog
        },
        data () {
            return {
                formData: {
                    lyyUserId: '',
                    value: ''
                },
                rules: {
                    lyyUserId: [
                        {required: true, message: '请输入用户ID', trigger: 'change'}
                    ]
                },
                equipmentTypeArr: [],
                spanMergeArr: [],
                pageInfo: {
                    total: 0,
                    pageSize: 10,
                    pageIndex: 1,
                    loading: false,
                    list: []
                },
                // 列表每一列参数
                colums: [
                    {key: 'username', label: '商户账号'},
                    {key: 'uid', label: '用户id'},
                    {key: 'name', label: '场地名称'},
                    {key: 'coins', label: '充值币'},
                    {key: 'adCoins', label: '广告币'},
                    {key: 'discountCoins', label: '抽奖币'},
                    {key: 'redCoins', label: '红包币（通用）'},
                    {key: 'groupGrantCoins', label: '派币（通用）'},
                    {key: 'idleCoinsFree', label: '免费闲时币'},
                    {key: 'idleCoins', label: '付费闲时币'},
                    {key: 'amount', label: '充值余额'},
                    {key: 'balance', label: '广告余额'},
                    {key: 'redAmount', label: '红包余额（通用）'},
                    {key: 'grantAmount', label: '派发余额（通用）'}
                ]
            };
        },

        beforeCreate () {
        },

        created () {
        },

        beforeMount () {
        },

        mounted () {
            this.init();
        },

        beforeUpdate () {
        },

        updated () {
        },

        activated () {
        },

        deactivated () {
        },

        beforeDestroy () {
        },

        destroyed () {
        },

        errorCaptured () {
        },

        methods: {
            init () {
            },
            objectSpanMethod ({row, column, rowIndex, columnIndex}) {
                if (columnIndex === 0) {
                    return {
                        rowspan: this.spanMergeArr[rowIndex],
                        colspan: this.spanMergeArr[rowIndex] === 0 ? 0 : 1
                    };
                }
            },
            queryBtn () {
                this.isParamsComplate(() => {
                    this.pageInfo.pageIndex = 1;
                    this.getList();
                });
            },
            getList () {
                this.isParamsComplate(() => {
                    this.pageInfo.loading = true;
                    userBalanceQuery(this.getParamsSearch()).then(res => {
                        this.pageInfo.loading = false;
                        if (res.result === 0) {
                            let arr = [];
                            for (let i = 0; i < res.data.coinsInfo.length; i++) {
                                const item = res.data.coinsInfo[i];
                                if (item.groupCoinsInfoList && item.groupCoinsInfoList.length > 0) {
                                    for (let j = 0; j < item.groupCoinsInfoList.length; j++) {
                                        const children = item.groupCoinsInfoList[j];
                                        const obj = {
                                            username: item.username,
                                            uid: res.data.lyyUserId,
                                            name: children.name,
                                            coins: children.coins,
                                            adCoins: children.adCoins,
                                            discountCoins: children.discountCoins,
                                            redCoins: item.redCoins,
                                          groupGrantCoins: children.groupGrantCoins,
                                            idleCoinsFree: children.idleCoinsFree,
                                            idleCoins: children.idleCoins,
                                            amount: children.amount,
                                            balance: children.balance,
                                            redAmount: item.redAmount,
                                            grantAmount: item.grantAmount,
                                        };
                                        arr.push(obj);
                                    }
                                } else {
                                  arr.push({
                                    redCoins: item.redCoins,
                                    groupGrantCoins: item.grantCoins,
                                    username: item.username,
                                    redAmount: item.redAmount,
                                    grantAmount: item.grantAmount,
                                    lyyDistributorId: item.lyyDistributorId,
                                    uid: res.data.lyyUserId,
                                  });
                                }
                            }
                            this.pageInfo.list = arr;
                            this.pageInfo.list = this.sortList(this.pageInfo.list, 'username');
                            this.spanMergeArr = this.spanMerge(this.pageInfo.list, 'username');
                        }
                    });
                });
            },
            sortList (list, key) {
                let arr = [];
                let obj = {};
                const listFlag = (list && Array.isArray(list) && list.length > 0);
                const keyFlag = (key && typeof key === 'string' && key.length > 0);
                if (listFlag && keyFlag) {
                    list.forEach((item) => {
                        if (item[key] && obj[item[key]]) {
                            obj[item[key]].push(item);
                        } else {
                            obj[item[key]] = [];
                            obj[item[key]].push(item);
                        }
                    });
                    for (let item in obj) {
                        arr = Array.prototype.concat(arr, obj[item]);
                    }

                }
                return arr;
            },
            spanMerge (list, key) {
                let arr = [];
                const listFlag = (list && Array.isArray(list) && list.length > 0);
                const keyFlag = (key && typeof key === 'string' && key.length > 0);
                if (listFlag && keyFlag) {
                    let preKey = '';
                    let preIndex = 0;
                    list.forEach((item, index) => {
                        if (item[key] && item[key] === preKey) {
                            arr[preIndex] += 1;
                            arr.push(0);
                        } else {
                            preKey = item[key];
                            preIndex = index;
                            arr.push(1);
                        }
                    });
                }
                return arr;
            },
            getPagination () {
                return {
                    pageIndex: this.pageInfo.pageIndex,
                    pageSize: this.pageInfo.pageSize
                };
            },
            getParamsSearch () {
                let params = {
                    lyyUserId: this.formData.lyyUserId
                };
                if (this.formData.value) {
                    params['value'] = this.formData.value;
                }
                return params;
            },
            // 参数是否完整
            async isParamsComplate (call) {
                await this.$refs['ruleForm'].validate((isSuccess, valid) => {
                    if (isSuccess) {
                        call();
                        return true;
                    } else {
                        let sign = '';
                        if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
                            const key = Object.keys(valid);
                            sign = valid[key[0]][0]['message'];
                            this.$message.error(sign);
                        }
                        return false;
                    }
                });
            },
            // 改变列表每页条数
            handleSizeChange (val) {
                this.pageInfo.pageSize = val;
                this.getList();
            },
            // 改变页码
            handleCurrentChange (val) {
                this.pageInfo.pageIndex = val;
                this.getList();
            },
            idChange () {
                const num = parseInt(this.formData.lyyUserId, 10);
                if (num || num === 0) {
                    this.formData.lyyUserId = '' + num;
                } else {
                    this.formData.lyyUserId = '';
                }
            },
            record () {
                // if (this.formData.lyyUserId) {
                //     this.$refs.recordRef.open(this.formData.lyyUserId);
                // } else {
                //     this.$message.error('请输入用户ID');
                // }
                this.$refs.recordRef.open(this.formData.lyyUserId);
            },
            adjustBtn () {
                if (this.formData.lyyUserId && this.formData.value) {
                    this.$refs.adjustRef.open(this.formData.lyyUserId, this.formData.value);
                } else if (!this.formData.lyyUserId) {
                    this.$message.error('请输入用户ID');
                } else if (!this.formData.value) {
                    this.$message.error('请输入设备编号/商户账号');
                }
            },
            openAllEmpty (lyyUserId, lyyDistributorId) {
                console.log(lyyDistributorId);
                this.$refs.allEmptyRef.open(lyyUserId, lyyDistributorId);
            },
            openAllEmptyConfirm (obj) {
                this.$refs.allEmptyConfirmRef.open(obj);
            },
            async allEmptyConfirm (obj) {
                let params = {
                    operationType: 3,
                    type: [],
                    lyyUserId: obj.lyyUserId,
                    lyyDistributorId: obj.lyyDistributorId
                };
                if (obj.isCoins) {
                    params['type'].push(1);
                }
                if (obj.isAmount) {
                    params['type'].push(2);
                }
                const res = await userBalanceUpdate(params);
                if (res.result === 0) {
                    let text = '该用户全部场地';
                    if (obj.isCoins) {
                        text += '余币';
                    }
                    if (obj.isCoins && obj.isAmount) {
                        text += '和';
                    }
                    if (obj.isAmount) {
                        text += '余额';
                    }
                    text += '全部为0';
                    this.$message.success(text);
                    this.$refs.allEmptyConfirmRef.handleClose();
                    this.$refs.allEmptyRef.handleClose();
                    this.$refs.adjustRef.getList();
                } else {
                    this.$refs.allEmptyConfirmRef.loading = false;
                }
            },
            allEmptyConfirmCancel () {
                this.$refs.allEmptyRef.loading = false;
            },
            openEmpty (row, lyyUserId) {
                this.$refs.emptyRef.open(row, lyyUserId);
            },
            openEmptyConfirm (obj) {
                this.$refs.emptyConfirmRef.open(obj);
            },
            async emptyConfirm (obj) {
                let params = {
                    operationType: 2,
                    type: [],
                    lyyUserId: obj.lyyUserId,
                    lyyDistributorId: obj.lyyDistributorId,
                    lyyEquipmentGroupId: obj.lyyEquipmentGroupId
                };
                if (obj.isCoins) {
                    params['type'].push(1);
                }
                if (obj.isAmount) {
                    params['type'].push(2);
                }
                const res = await userBalanceUpdate(params);
                if (res.result === 0) {
                    let text = '该用户该场地' + obj.name;
                    if (obj.isCoins) {
                        text += '余币' + obj.coins + '个';
                    }
                    if (obj.isCoins && obj.isAmount) {
                        text += '和';
                    }
                    if (obj.isAmount) {
                        text += '余额' + obj.amount + '元';
                    }
                    text += '全部为0';
                    this.$message.success(text);
                    this.$refs.emptyConfirmRef.handleClose();
                    this.$refs.emptyRef.handleClose();
                    this.$refs.adjustRef.getList();
                } else {
                    this.$refs.emptyConfirmRef.loading = false;
                }
            },
            emptyConfirmCancel () {
                this.$refs.emptyRef.loading = false;
            },
            openEmptySingle (obj) {
                this.$refs.emptySingleRef.open(obj);
            },
            async emptySingleConfirm (obj) {
                if (parseFloat(obj.oddValue) < parseFloat(obj.newValue)) {
                    this.$refs.emptySingleErrorRef.open(obj);
                } else {
                    let params = {
                        operationType: 1,
                        type: [],
                        lyyUserId: obj.lyyUserId,
                        lyyDistributorId: obj.lyyDistributorId,
                        lyyEquipmentGroupId: obj.lyyEquipmentGroupId
                    };
                    switch (obj.label) {
                        case '充值币':
                            params['payCoins'] = obj.newValue;
                            break;
                        case '广告币':
                            params['adcoins'] = obj.newValue;
                            break;
                        case '抽奖币':
                            params['discountcoins'] = obj.newValue;
                            break;
                        case '红包币（通用）':
                            params['redCoins'] = obj.newValue;
                            break;
                        case '派币（通用）':
                            params['grantCoins'] = obj.newValue;
                            break;
                        case '免费闲时币':
                            params['idleCoinsFree'] = obj.newValue;
                            break;
                        case '付费闲时币':
                            params['idleCoins'] = obj.newValue;
                            break;
                        case '充值余额':
                            params['payAmount'] = obj.newValue;
                            break;
                        case '广告余额':
                            params['balance'] = obj.newValue;
                            break;
                        case '红包余额（通用）':
                            params['redAmount'] = obj.newValue;
                            break;
                        case '派发余额（通用）':
                            params['grantAmount'] = obj.newValue;
                            break;
                    }
                    if (obj.company === '币') {
                        params['type'].push(1);
                    } else {
                        params['type'].push(2);
                    }
                    const res = await userBalanceUpdate(params);
                    if (res.result === 0) {
                        let text = '清理该用户' + obj.label + obj.newValue + obj.company + '成功';
                        this.$message.success(text);
                        this.$refs.emptySingleRef.handleClose();
                        this.$refs.adjustRef.getList();
                    } else {
                        this.$refs.emptySingleRef.loading = false;
                    }
                }
            },
            emptySingleErrorConfirm () {
                this.$refs.emptySingleRef.loading = false;
                this.$refs.emptySingleErrorRef.visible = false;
                this.$refs.emptySingleErrorRef.loading = false;
            },
            emptySingleErrorConfirmCancel () {
                this.$refs.emptySingleRef.handleClose();
            }
        },

        computed: {},

        watch: {}

    };

</script>
<style lang="less" scoped>
  .operation {
    padding: 20px;
    position: relative;

    .record {
      position: absolute;
      right: 20px;
      top: 20px;
    }
  }
</style>
