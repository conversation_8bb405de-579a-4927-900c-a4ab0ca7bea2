<template>
  <div class="deviceDataDetail" v-loading="loading">
    <div class="activeDetail-searchBar">
      <el-form ref="form" :model="searchForm" label-width="90px" :inline="true">
        <el-row>
          <el-col :span="24">
            <el-form-item label="日期：" label-width="60">
              <el-radio-group v-model="dayPosition" @change="timeChange">
                <el-radio-button label="yesterday">昨天</el-radio-button>
                <el-radio-button label="late7">近7天</el-radio-button>
                <el-radio-button label="late30">近30天</el-radio-button>
              </el-radio-group>
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="actDate"
                @change="timeChange('self')"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :clearable="false">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="设备SN号：">
              <el-input v-model="searchForm.serialUniqueCode" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="设备编号：">
              <el-input v-model="searchForm.equipmentValue" clearable/>
            </el-form-item>
            <el-form-item label="商家编号：">
              <el-input v-model="searchForm.lyyDistributorId" clearable/>
            </el-form-item>
            <el-form-item label="设备类型：">
              <el-select placeholder="请选择" v-model="searchForm.equipmentTypeId" clearable>
                <el-option
                  v-for="item in typeList"
                  :key="item.lyyEquipmentTypeId"
                  :label="item.typeName"
                  :value="item.lyyEquipmentTypeId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-button type="primary" @click="getSearchData">搜索</el-button>
            <el-button type="primary" @click="exportData">导出</el-button>
            <el-button type="primary" @click="showListItem">展示数据</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="table">
      <el-table :data="table.tableData" border>
        <el-table-column
          :prop="v.key"
          :label="v.label"
          align="center"
          :width="v.width||'auto'"
          v-for="v in tableList"
          :key="v.key"/>
      </el-table>
    </div>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="table.pageIndex"
      :page-sizes="[10, 20, 50, 100, 300, 500]"
      :page-size="table.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="table.total"/>

    <el-dialog
      center
      title="展示数据"
      width="700px"
      :visible.sync="dialogVisible"
      :before-close="cancelShowData">
      <el-transfer
        v-model="showTableList"
        :data="allTableList"
        :titles="['隐藏字段','显示字段']"
        :button-texts="['隐藏','显示']"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmShowData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { formatDate, queryParams } from '@/utils/utils';
  import { getTimeStamp } from '@/utils';
  import { faceEquipmentDataPage, faceEquipmentDataEquipmentType } from "@api/faceBrush";
  import config from "../../../assets/js/config";
  import { downloadFileRamToken } from '@/utils/menu'

  export default {
    data() {
      return {
        tableList: [],
        showTableList: [],
        allTableList: [
          {
            key: 'serialUniqueCode',
            label: '设备SN号',
            width: '200px',
            disabled: true,
          },
          {
            key: 'equipmentTypeName',
            label: '设备类型',
            width: '120px',
            disabled: true,
          },
          {
            key: 'equipmentValue',
            label: '刷脸设备编号',
            width: '80px',
            disabled: true,
          },
          {
            key: 'associatedEquipmentValue',
            label: '关联设备编号',
            width: '80px',
            disabled: true,
          },
          {
            key: 'merchantName',
            label: '商家名称',
            disabled: true,
          },
          {
            key: 'lyyDistributorId',
            label: '商家编号',
            disabled: true,
            width: '100px'
          },
          {
            key: 'qrCodePayNum',
            label: '扫码支付笔数',
            disabled: false,
          },
          {
            key: 'facePayNum',
            label: '刷脸支付笔数',
            disabled: false,
          },
          {
            key: 'facePayNumPercent',
            label: '刷脸支付笔数占比',
            disabled: false,
          },
          {
            key: 'facePayPeopleNum',
            label: '刷脸支付人数',
            disabled: false,
          },
          {
            key: 'totalPayPeopleNum',
            label: '总支付人数',
            disabled: false,
          },
          {
            key: 'facePayPeopleNumPercent',
            label: '刷脸支付人数占比',
            disabled: false,
          },
          {
            key: 'registerDate',
            label: '注册时间',
            disabled: false,
            firstHidden: true,
            width: '160px'
          },
          {
            key: 'onlineDayNum',
            label: '在线天数',
            disabled: false,
            firstHidden: true,
          },
          {
            key: 'facePayAmount',
            label: '刷脸支付金额',
            disabled: false,
            firstHidden: true,
          },
          {
            key: 'totalAmount',
            label: '总金额',
            disabled: false,
            firstHidden: true,
          },
          {
            key: 'facePayAmountPercent',
            label: '刷脸金额占比',
            disabled: false,
            firstHidden: true,
          },
          {
            key: 'realFacePayUserNum',
            label: '刷脸≥2元用户',
            disabled: false,
            firstHidden: true,
          },
        ],
        dialogVisible: false,
        dayPosition: 'yesterday',
        actDate: [],
        searchForm: {
          equipmentValue: "",
          lyyDistributorId: "",
          serialUniqueCode: "",
          equipmentTypeId: "",
        },
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() > Date.now();
          }
        },
        loading: false,
        table: {
          total: 0,
          pageIndex: 1,
          pageSize: 10,
          tableData: []
        },
        typeList: []
      }
    },
    methods: {
      showListItem() {
        this.dialogVisible = true;
      },
      confirmShowData() {
        this.tableList = this.allTableList.filter(v => {
          return this.showTableList.includes(v.key);
        });
        this.cancelShowData();
      },
      cancelShowData() {
        this.dialogVisible = false;
      },
      exportData() {
        const [ startDate, endDate ] = this.actDate;
        const params = {
          pageIndex: this.table.pageIndex,
          pageSize: this.table.pageSize,
          startDate,
          endDate,
          ...this.searchForm
        };
        downloadFileRamToken(`/face/faceEquipmentData/export?${ queryParams(params) }`)
      },
      async getTypeList() {
        const res = await faceEquipmentDataEquipmentType();
        if (res.result === 0) {
          this.typeList = [
            {
              lyyEquipmentTypeId: '',
              typeName: '全部',
            },
            ...res.data
          ];
        }
      },
      getSearchData() {
        this.table.pageIndex = 1;
        this.getData();
      },
      async getData() {
        const [ startDate, endDate ] = this.actDate;
        if (getTimeStamp(endDate) - getTimeStamp(startDate) > 3600 * 24 * 30 * 3) {
          this.$message.error("时间跨度不能超过3个月");
          return;
        }
        this.loading = true;
        this.table.tableData = [];
        let params = {
          pageIndex: this.table.pageIndex,
          pageSize: this.table.pageSize,
          startDate,
          endDate,
          ...this.searchForm
        };
        const res = await faceEquipmentDataPage(params);
        this.loading = false;
        if (res.result === 0) {
          this.table.tableData = res.data.items.map(v => ({
            ...v,
            facePayNumPercent: `${ v.facePayNumPercent || 0 }%`,
            facePayPeopleNumPercent: `${ v.facePayPeopleNumPercent || 0 }%`,
            facePayAmountPercent: `${ v.facePayAmountPercent || 0 }%`,
          }));
          this.table.total = res.data.total;
        }
      },
      handleCurrentChange(val) {
        this.table.pageIndex = val;
        this.getData();
      },
      handleSizeChange(val) {
        this.table.pageSize = val;
        this.getData();
      },
      timeChange(item) {
        let startDate = "";
        let endDate = "";
        let today = formatDate(new Date().getTime(), 'yyyy-MM-dd');
        let yesterday = formatDate(new Date().getTime() - 24 * 60 * 60 * 1000, 'yyyy-MM-dd');
        let late7 = formatDate(new Date().getTime() - 24 * 60 * 60 * 1000 * 6, 'yyyy-MM-dd');
        let late30 = formatDate(new Date().getTime() - 24 * 60 * 60 * 1000 * 29, 'yyyy-MM-dd');
        switch (item) {
          case 'yesterday':
            startDate = yesterday;
            endDate = startDate;
            this.actDate = [ startDate, endDate ];
            break;
          case 'late7':
            startDate = late7;
            endDate = today;
            this.actDate = [ startDate, endDate ];
            break;
          case "late30":
            startDate = late30;
            endDate = today;
            this.actDate = [ startDate, endDate ];
            break;
          default:
            this.dayPosition = "";
            // 如果日期选择器的时间对应上tab日期的值，就赋值
            if (this.actDate) {
              startDate = this.actDate[0];
              endDate = this.actDate[1];
              if (startDate === yesterday && startDate === endDate) {
                this.dayPosition = 'yesterday';
              }
              if (startDate === late7 && endDate === today) {
                this.dayPosition = 'late7';
              }
              if (startDate === late30 && endDate === today) {
                this.dayPosition = 'late30';
              }
            }
        }
      },
    },
    created() {
      this.timeChange('yesterday');
      this.getData();
      this.getTypeList();
      this.allTableList.forEach(v => {
        if (!v.firstHidden) {
          this.showTableList.push(v.key);
          this.tableList.push(v);
        }
      });
    }
  }
</script>

<style scoped lang="less">
  .deviceDataDetail {
    /deep/ .el-form-item__label {
      text-align: left;
    }

    /deep/ .el-dialog__body {
      display: flex;
      justify-content: center;
    }

    .table {
      margin: 20px 0;
    }
  }
</style>
