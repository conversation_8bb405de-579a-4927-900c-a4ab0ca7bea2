<template>
  <div class="active" v-if="isShow">
    <el-form :inline="false"
             label-position="right"
             :model="formData"
             ref="ruleForm"
             :rules="rules"
             v-loading="visibleLoading"
             label-width="190px">
      <el-form-item label="设置活动名称" prop="name">
        <el-input v-model.trim="formData.name" :maxLength="maxName" placeholder="请输入活动名称"
                  class="title">
          <template slot="append">
            <span
              v-if="formData.name && typeof formData.name === 'string' && formData.name.length > 0">{{formData.name.length}}</span>
            <span v-else>0</span>
            <span>/{{maxName}}</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="设置活动链接" prop="link">
        <el-input v-model.trim="formData.link" placeholder="请输入活动链接" class="title"></el-input>
      </el-form-item>
      <el-form-item label="设置VIP客服联系微信" prop="coverImgUrl">
        <!--这个input不显示，只是为了绑定formData.imageUrlCompress，这样这个值为空的时候会显示提示信息-->
        <el-input v-show="false" v-model="formData.coverImgUrl"></el-input>
        <i v-if="formData.coverImgUrl" class="el-icon-error img-close" @click="imageClear()"></i>
        <el-upload class="avatar-uploader upload-box"
                   :action="tutorialImageUpload"
                   :headers="headerRamToken"
                   :show-file-list="false"
                   drag
                   ref="uploadRef"
                   accept="image/jpeg, image/png"
                   :on-success="handleAvatarSuccess"
                   :before-upload="beforeAvatarUpload">
          <div v-loading="imageLoading">
            <div v-if="formData.coverImgUrl">
              <img :src="imageUrlPrefix + formData.coverImgUrl"
                   ref="imgRef"
                   class="avatar">
            </div>
            <i v-else
               class="el-icon-plus avatar-uploader-icon"></i>
          </div>
        </el-upload>
        <p>{{fileMaxSize}}M内，支持JPG，PNG</p>
      </el-form-item>
      <el-form-item label="提升充值率名称" prop="increaseRateName">
        <el-input v-model.trim="formData.increaseRateName" :maxLength="maxName" placeholder="请输入"
                  class="title">
          <template slot="append">
            <span
              v-if="formData.increaseRateName && typeof formData.increaseRateName === 'string' && formData.increaseRateName.length > 0">{{formData.increaseRateName.length}}</span>
            <span v-else>0</span>
            <span>/{{maxName}}</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="链接" prop="increaseRateUrl">
        <el-input v-model.trim="formData.increaseRateUrl" placeholder="请输入链接" class="title"></el-input>
      </el-form-item>
      <el-form-item label="提升技巧banner" prop="skillBanner">
        <!--这个input不显示，只是为了绑定formData.imageUrlCompress，这样这个值为空的时候会显示提示信息-->
        <el-input v-show="false" v-model="formData.skillBanner"></el-input>
        <upload-base ref="refUploadBase" @beforeUpload="beforeUpload" @upload="upload">
          <upload-img-box ref="refUploadImgBox"></upload-img-box>
        </upload-base>
        <p>{{fileMaxSize}}M内，支持JPG，PNG</p>
      </el-form-item>
      <el-form-item label="链接" prop="skillLink">
        <el-input v-model.trim="formData.skillLink" placeholder="请输入链接" class="title"></el-input>
      </el-form-item>
    </el-form>
    <el-form>
      <el-form-item>
        <el-button type="primary" @click="handleSave">保 存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import {addActivityUrl, getActivityUrl} from "@api/deviceAnalysisSettings/deviceAnalysisSettings";
  import {tutorialImageUpload} from "@api/functionIntroduction/tutorial";
  import UploadBase from "@components/tableBase/uploadBase";
  import UploadImgBox from "@components/tableBase/uploadImgBox";
  import {materielUploadImage} from "@api/materielUpload";
  import {isEqual, isNotEmptyString} from "@components/tableBase/utilsTableBase";
  import { headerRamToken } from '@/utils/menu'

  export default {
    name: "deviceAnalysisSettingsActive",
    props: {},
    components: {UploadImgBox, UploadBase},
    data() {
      return {
        headerRamToken,
        formData: {
          name: '',
          link: '',
          increaseRateName: '',
          increaseRateUrl: '',
          coverImgUrl: '',
          skillBanner: '',
          skillLink: '',
        },
        maxName: 15,
        rules: {},
        imageLoading: false,
        tutorialImageUpload: tutorialImageUpload(),
        fileMaxSize: 15, // 图片最大体积
        imageUrlPrefix: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/',
        visibleLoading: false,
        isShow: false,
        refUploadBase: null,
        refUploadImgBox: null,
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.initRule();
      this.isShow = true;
      this.getInfo();
      this.$nextTick(() => {
        this.refUploadBase = this.$refs.refUploadBase;
        this.refUploadBase.setUploadUrlSecurityFun(materielUploadImage());
        this.refUploadBase.setAcceptSecurityFun("img");
        this.refUploadImgBox = this.$refs.refUploadImgBox;
      });
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      async getInfo() {
        this.visibleLoading = true;
        const res = await getActivityUrl();
        this.visibleLoading = false;
        if (res.result === 0 && res.description) {
          const data = JSON.parse(res.description);
          if (data.name) {
            this.formData.name = data.name;
          }
          if (data.increaseRateName) {
            this.formData.increaseRateName = data.increaseRateName;
          }
          if (data.url) {
            this.formData.link = data.url;
          }
          if (data.increaseRateUrl) {
            this.formData.increaseRateUrl = data.increaseRateUrl;
          }
          if (data.picUrl) {
            this.formData.coverImgUrl = data.picUrl;
          }
          if (data.skillBanner) {
            this.formData.skillBanner = data.skillBanner;
            this.$nextTick(() => {
              this.refUploadImgBox.setImgUrlFun(this.formData.skillBanner);
            });
          }
          if (data.skillLink) {
            this.formData.skillLink = data.skillLink;
          }
        }
      },
      initRule() {
        this.rules = {
          name: [
            {max: this.maxTitle, message: `活动名称不能超过${this.maxName}字`, trigger: 'change'}
          ],
          increaseRateName: [
            {max: this.maxTitle, message: `提升充值率名称不能超过${this.maxName}字`, trigger: 'change'}
          ],
          // link: [
          //   {required: true, message: '请输入设置活动链接', trigger: 'change'},
          // ],
          coverImgUrl: [
            {required: true, message: '请上传VIP客服联系微信', trigger: 'change'}
          ],
        };
      },
      handleSave() {
        this.isParamsComplate(() => {
          if (!this.formData.name && this.formData.link) {
            this.$message.error('填写活动链接前必须填写活动名称');
            return false;
          }
          if (!this.formData.increaseRateName && this.formData.increaseRateUrl) {
            this.$message.error('填写链接前必须填写提升充值率名称');
            return false;
          }
          this.visibleLoading = true;
          const params = {
            name: this.formData.name,
            increaseRateName: this.formData.increaseRateName,
            url: this.formData.link,
            increaseRateUrl: this.formData.increaseRateUrl,
            picUrl: this.formData.coverImgUrl,
            skillBanner: this.formData.skillBanner,
            skillLink: this.formData.skillLink,
          };
          addActivityUrl(params).then(res=>{
            this.visibleLoading = false;
            if (res.result === 0) {
              this.$message.success('保存成功');
            }
          })
        });

      },
      getParams() {
        let params = {
          name: this.formData.name,
        };

        return params;
      },
      // 参数是否完整
      async isParamsComplate(call) {
        await this.$refs['ruleForm'].validate((isSuccess, valid) => {
          if (isSuccess) {
            call();
            return true;
          } else {
            let sign = '';
            if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]['message'];
              this.$message.error(sign);
            }
            return false;
          }
        });
      },
      // 上传到后台前的图片验证
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg';
        const isPNG = file.type === 'image/png';
        const isQualified = file.size / 1024 / 1024 < this.fileMaxSize;
        if (!isJPG && !isPNG) {
          this.$message.error('上传物料图片只能是 JPG或PNG 格式!');
        }
        if (!isQualified) {
          this.$message.error(`上传头像图片大小不能超过 ${this.fileMaxSize}MB!`);
        }
        if ((isJPG || isPNG) && isQualified) {
          this.imageLoading = true;
          return true;
        }
      },
      // 上传图片的回调
      handleAvatarSuccess(res, file) {
        this.imageLoading = false;
        if (res.result === 1) {
          this.formData.coverImgUrl = res.para;
        } else {
          this.$message.error('上传失败');
          this.imageClear();
        }
      },
      // 清除图片
      imageClear() {
        this.formData.coverImgUrl = '';
      },
      beforeUpload(file) {
        if (file && file.status && isEqual(file.status, "ready")) {
          const isLt15M = file.size / 1024 / 1024 < 15;
          if (
            (file.raw.type === "image/jpeg" || file.raw.type === "image/png") &&
            isLt15M
          ) {
            this.refUploadBase.submitUpload();
            this.refUploadImgBox.reSetImgUrlFun();
            this.refUploadImgBox.showLoading();
          } else {
            this.refUploadBase.clearFiles();
            if (!isLt15M) {
              this.$message.error("图片大小超过15M");
            } else if (
              !(file.raw.type === "image/jpeg" || file.raw.type === "image/png")
            ) {
              this.$message.error("图片仅支持JPG，PNG");
            }
          }
        }
      },
      upload(response, file, fileList) {
        this.refUploadImgBox.highLoading();
        if (response.result === 1) {
          this.$message.success("上传成功!");
          this.formData.skillBanner = response.para;
          if (
            response.para &&
            response.para.split(";") &&
            response.para.split(";")[1]
          ) {
            this.refUploadImgBox.setImgUrlFun(response.para.split(";")[1]);
            this.formData.skillBanner = response.para.split(";")[0];
          } else {
            this.refUploadImgBox.setImgUrlFun(response.para);
            this.formData.skillBanner = response.para;
          }
        } else if (isNotEmptyString(response.description)) {
          this.$message.error(response.description);
        } else {
          this.$message.error("上传失败");
        }
      },
    },

    computed: {},

    watch: {},
  }
</script>

<style scoped lang="less">
  .active {
    .title, .seq, .home-seq {
      width: 30%;
    }

    .avatar-uploader .el-upload {
      border: 1px dashed #000;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 154px;
      height: 154px;
      line-height: 154px;
      text-align: center;
    }

    .avatar {
      width: 154px;
      height: 154px;
      display: block;
    }

    .upload-box {
      /deep/ .el-upload-dragger {
        width: 154px;
        height: 154px;
      }
    }

    .img-close {
      position: absolute;
      top: 0;
      left: 120px;
      z-index: 100;
      font-size: 30px;
      transform: translate(50%, -50%);
      cursor: pointer;
    }
  }
</style>
