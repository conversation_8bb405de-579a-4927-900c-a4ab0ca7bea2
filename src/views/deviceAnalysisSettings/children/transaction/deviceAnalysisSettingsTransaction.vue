<template>
  <div>
    <device-analysis-settings-transaction-list v-show="isShowList" @detail="showDetail"/>
    <device-analysis-settings-transaction-group ref="groupRef" v-if="isShowGroup" @back="back" :obj="groupObj"/>
  </div>
</template>

<script>
  import DeviceAnalysisSettingsTransactionList
    from "@views/deviceAnalysisSettings/children/transaction/deviceAnalysisSettingsTransactionList";
  import DeviceAnalysisSettingsTransactionGroup
    from "@views/deviceAnalysisSettings/children/transaction/deviceAnalysisSettingsTransactionGroup";
  export default {
    name: "deviceAnalysisSettingsTransaction",
    props: {},
    components: {DeviceAnalysisSettingsTransactionGroup, DeviceAnalysisSettingsTransactionList},
    data() {
      return {
        isShowList: true,
        isShowGroup: false,
        groupObj: {
          dateType: null,
          groupIds: [],
        },
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      showDetail(obj) {
        this.groupObj = {
          dateType: obj.dateType,
          groupIds: obj.groupIds,
        };
        this.isShowList = false;
        this.isShowGroup = true;
      },
      back(row) {
        this.isShowList = true;
        this.isShowGroup = false;
      },
    },

    computed: {},

    watch: {},
  }
</script>

<style scoped lang="less">
</style>
