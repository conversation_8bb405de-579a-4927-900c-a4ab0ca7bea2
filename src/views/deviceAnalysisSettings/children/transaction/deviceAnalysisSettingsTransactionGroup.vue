<template>
  <div v-loading="pageInfo.loading">
    <div class="operation">
      <el-form :inline="true" label-position="right" label-width="100px">
        <el-form-item label="设备编号：">
          <el-input class="inline-input" v-model.trim="formData.value" placeholder="请输入设备编号" clearable></el-input>
        </el-form-item>
        <!--按钮-->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="queryBtn">查询</el-button>
          <el-button type="success" icon="el-icon-download" @click="exportBtn">导出</el-button>
          <el-button icon="el-icon-back" @click="backBtn">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <!--列表 start-->
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;"
                >
        <template v-for="(item, index) in colums">
          <el-table-column v-if="item.key === 'operation'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'date'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <span>{{scope.row.startDate}}至{{scope.row.endDate}}</span>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'equipmentTypeName'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           column-key="equipmentTypeName"
                           :filters="equipmentTypeNameArr"
                           :filter-method="filterTag"
                           align="center">
            <template slot-scope="scope">
              <span>{{scope.row.equipmentTypeName}}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :key="index"
            :prop="item.key"
            :label="item.label"
            :width="item.width"
            :sortable="item.sortable"
            align="center"
          />
        </template>
      </el-table>
    </div>
  </div>
</template>

<script>
    import {getEquipmentDetail, exportEquipmentDetail} from "@api/deviceAnalysisSettings/deviceAnalysisSettings";
    import { downloadFileRamToken } from '@/utils/menu'

    export default {
      name: "deviceAnalysisSettingsTransactionGroup",
      props: {
        obj: {
          type: Object,
          default: {},
        }
      },
      components: {},
      data() {
        return {
          formData: {
            value: '',
            dateType: null,
            groupIds: [],
            type: null,
          },
          pageInfo: {
            total: 0,
            pageSize: 10,
            pageIndex: 1,
            loading: false,
            list: [],
            allList: [],
          },
          // 列表每一列参数
          colums: [
            {key: 'date', label: '时间'},
            {key: 'adName', label: '商家账号'},
            {key: 'groupName', label: '场地名称'},
            {key: 'value', label: '设备编码'},
            {key: 'equipmentTypeName', label: '设备类型'},
            {key: 'isDeal', label: '是否有交易'},
            {key: 'location', label: '所在城市'},
          ],
          equipmentTypeNameArr: [],
          isSearchOpen: false,
        };
      },

      beforeCreate() {
      },

      created() {
      },

      beforeMount() {
      },

      mounted() {
        this.formData.dateType = this.obj.dateType;
        this.formData.groupIds = this.obj.groupIds;
        this.getList();
      },

      beforeUpdate() {
      },

      updated() {
      },

      activated() {
      },

      deactivated() {
      },

      beforeDestroy() {
      },

      destroyed() {
      },

      errorCaptured() {
      },

      methods: {
        backBtn() {
          this.$emit('back');
        },
        exportBtn() {
          let params = [];
          if (this.formData.dateType) {
            params.push('dateType=' + this.formData.dateType);
          }
          if (this.formData.groupIds) {
            params.push('groupIds=' + this.formData.groupIds);
          }
          if (this.formData.value) {
            params.push('equipmentValue=' + this.formData.value);
          }
          downloadFileRamToken(encodeURI(exportEquipmentDetail() + '?' + params.join('&')), true);
        },
        queryBtn() {
          this.pageInfo.pageIndex = 1;
          this.getList();
        },
        getList() {
          this.pageInfo.loading = true;
          getEquipmentDetail(this.getParams()).then(res => {
            this.pageInfo.loading = false;
            if (res.result === 0) {
              this.pageInfo.list = res.data;
              this.pageInfo.allList = res.data;
              this.equipmentTypeNameArr = [];
              let arr = [];
              this.pageInfo.list.forEach(item=>{
                if (arr.indexOf(item.equipmentTypeName) === -1) {
                  this.equipmentTypeNameArr.push({
                    text: item.equipmentTypeName,
                    value: item.equipmentTypeName,
                  });
                  arr.push(item.equipmentTypeName);
                }
              });
            }
          });
        },
        getParams() {
          let params = {
            dateType: this.formData.dateType,
            groupIds: this.formData.groupIds,
          };
          if (this.formData.value) {
            params['equipmentValue'] = this.formData.value;
          }
          return params;
        },
        filterTag(value, row) {
            return row.equipmentTypeName === value;
        },
        // 改变列表每页条数
        handleSizeChange(val) {
          this.pageInfo.pageSize = val;
          this.pageInfo.total = 0;
          this.getList();
        },
        // 改变页码
        handleCurrentChange(val) {
          this.pageInfo.pageIndex = val;
          this.getList();
        },
        typeChange() {
          let arr = [];
          if (this.formData.type) {
            this.pageInfo.allList.forEach(item=>{
              if (item.equipmentTypeName === this.formData.type) {
                arr.push(item);
              }
            });
            this.pageInfo.list = arr;
          } else {
            arr = this.pageInfo.allList;
            this.pageInfo.list = [];
            this.$nextTick(()=>{
              this.pageInfo.list = arr;
            });
          }
        },
      },

      computed: {},

      watch: {},
    }
</script>

<style scoped lang="less">
  .operation {
    padding: 20px;
  }
  .search-box{
    position: relative;
    .search-bg{
      position: absolute;
    }
  }
</style>
