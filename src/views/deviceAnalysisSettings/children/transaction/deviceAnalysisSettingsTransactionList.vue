<template>
  <div v-loading="pageInfo.loading">
    <div class="operation">
      <el-form :inline="true" label-position="right" label-width="100px">
        <el-form-item label="选择日期：" prop="date">
          <el-radio-group v-model="formData.dateType" style="margin-bottom: 30px;" @change="dateChange">
            <el-radio-button v-for="(item, index) in dateArr" :key="index" :label="item.key">{{item.value}}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="在线交易率小于：" prop="incomeRate" :inline="false" label-width="125px">
          <el-input class="inline-input" v-model.trim="formData.incomeRate" placeholder="请输入在线交易率" clearable @input="valueInput">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="商家账号：" :inline="false">
          <el-input class="inline-input" v-model.trim="formData.adName" placeholder="请输入" clearable >
          </el-input>
        </el-form-item>
        <!--按钮-->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="queryBtn">查询</el-button>
          <el-button type="efault" icon="el-icon-search" @click="defaultBtn">重置</el-button>
          <el-button type="success" icon="el-icon-download" @click="exportBtn">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <!--列表 start-->
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;"
                @filter-change="filterHandlerRange"
                ref="filterTable"
                >
        <template v-for="(item, index) in colums">
          <el-table-column v-if="item.key === 'operation'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
                <el-button type="text" @click="detailBtn(scope.row)">查看设备详情</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'date'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <span>{{scope.row.startDate}}至{{scope.row.endDate}}</span>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'onlineEquipmentCount'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           column-key="onlineEquipmentCount"
                           :filters="rangeArr"
                           align="center">
            <template slot-scope="scope">
              <span>{{scope.row.onlineEquipmentCount}}</span>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'incomeEquipmentCount'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           column-key="incomeEquipmentCount"
                           :filters="rangeArr"
                           align="center">
            <template slot-scope="scope">
              <span>{{scope.row.incomeEquipmentCount}}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :key="index"
            :prop="item.key"
            :label="item.label"
            :width="item.width"
            :sortable="item.sortable"
            align="center"
          />
        </template>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 50]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <div v-if="isShow" :style="styleObj" class="bg">
      <div class="input-box">
        <span class="label">小于</span>
        <el-input suffix-icon="el-icon-search"></el-input>
      </div>
      <div class="btn-box">
        <el-button type="primary">确定</el-button>
        <el-button>取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
  import {getAnalysis, exportAnalysis} from "@api/deviceAnalysisSettings/deviceAnalysisSettings.js";
  import {dateFormat} from "@js/utils";
  import { downloadFileRamToken } from '@/utils/menu'

  export default {
    name: "deviceAnalysisSettingsTransactionList",
    props: {},
    components: {},
    data() {
      return {
        formData: {
          startDate: '',
          endDate: '',
          account: '',
          dateType: 1,
          range: [],
          onlineRange: [],
          incomeRate: null,
          adName: null,
        },
          checkList: null,
        // 日期插件的时间选择范围
        pickerOptions: {
        },
        days: 7, // 初始化时间开始日期小于当前时间的天数
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          loading: false,
          list: [],
        },
        // 列表每一列参数
        colums: [
          {key: 'date', label: '时间'},
          {key: 'adName', label: '商家账号'},
          {key: 'groupName', label: '场地名称'},
          {key: 'incomeEquipmentCount', label: '交易设备数'},
          {key: 'onlineEquipmentCount', label: '在线设备数'},
          {key: 'incomeRate', label: '在线设备交易率'},
          {key: 'totalEquipmentCount', label: '全部设备'},
          {key: 'groupRate', label: '全部设备交易率'},
          {key: 'payAmount', label: '场地交易金额'},
          {key: 'location', label: '所在城市'},
          {key: 'operation', label: '操作'},
        ],
        isShow: false,
        styleObj: {},
        dateArr: [
            {key: 1, value: '昨天'}, // 全部
            {key: 2, value: '近7天'}, // 已删除
            {key: 3, value: '近30天'}, // 投放中
        ],
        rangeArr : [
            {value: '[50,)', text: '≥50'},
            {value: '[40,49]', text: '40-49'},
            {value: '[30,39]', text: '30-39'},
            {value: '[20,29]', text: '20-29'},
            {value: '[10,19]', text: '10-19'},
            {value: '[5,10]', text: '5-10'},
            {value: '[0,5]', text: '<5'},
        ],
        onlineRangeArr : [
            {value: '[50,)', text: '≥50'},
            {value: '[40,49]', text: '40-49'},
            {value: '[30,39]', text: '30-39'},
            {value: '[20,29]', text: '20-29'},
            {value: '[10,19]', text: '10-19'},
            {value: '[5,10]', text: '5-10'},
            {value: '[0,5]', text: '<5'},
        ],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.initDataPicker();
      this.dateChange();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      valueInput() {
        if (new RegExp(/^100/).test(String(this.formData.incomeRate))) {
          this.formData.incomeRate = 100;
        } else {
          this.formData.incomeRate = String(this.formData.incomeRate).replace(/^\D*([0-9]{0,2}(\.\d{0,2})?)?.*$/, '$1');
        }
      },
      filterHandlerRange(arr) {
          if (arr['onlineEquipmentCount']) {
              this.formData.range = arr['onlineEquipmentCount'];
          }
          if (arr['incomeEquipmentCount']) {
              this.formData.onlineRange = arr['incomeEquipmentCount'];
          }
          this.getList();
      },
      dateChange() {
        if (this.formData.dateType === 1) {
          const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
          const days = 6; // 最大天数
          const end = dateFormat(new Date(new Date().setHours(0, 0, 0, 0) - times), 'yyyy-MM-dd');
          const start = dateFormat(new Date(new Date().setHours(0, 0, 0, 0) - times), 'yyyy-MM-dd');
          this.formData.startDate = start;
          this.formData.endDate = end;
        } else if (this.formData.dateType === 2) {
          const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
          const days = 6; // 最大天数
          const end = dateFormat(new Date(new Date().setHours(0, 0, 0, 0)), 'yyyy-MM-dd');
          const start = dateFormat(new Date(new Date().setHours(0, 0, 0, 0) - times * days), 'yyyy-MM-dd');
          this.formData.startDate = start;
          this.formData.endDate = end;
        } else if (this.formData.dateType === 3) {
          const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
          const days = 29; // 最大天数
          const end = dateFormat(new Date(new Date().setHours(0, 0, 0, 0)), 'yyyy-MM-dd');
          const start = dateFormat(new Date(new Date().setHours(0, 0, 0, 0) - times * days), 'yyyy-MM-dd');
          this.formData.startDate = start;
          this.formData.endDate = end;
        }
        this.getList();
      },
      detailBtn(row) {
        const params = {
          dateType: this.formData.dateType,
          groupIds: [row.lyyEquipmentGroupId],
        };
        this.$emit('detail', params)
      },
      exportBtn() {
        let params = [];
        if (this.formData.account) {
          params.push('adName=' + this.formData.account);
        }
        if (this.formData.dateType) {
          params.push('dateType=' + this.formData.dateType);
        }
        if (this.formData.incomeRate) {
          params.push('incomeRate=' + parseFloat(this.formData.incomeRate) / 100 + '');
        }
        downloadFileRamToken(encodeURI(exportAnalysis() + '?' + params.join('&')), true);
      },
      defaultBtn() {
        this.formData = {
          startDate: '',
          endDate: '',
          account: '',
          dateType: 1,
          range: [],
          onlineRange: [],
          incomeRate: null,
          adName: null,
        };
        this.$refs.filterTable.clearFilter();
          this.pageInfo.pageIndex = 1;
        this.getList();
      },
      queryBtn() {
        this.pageInfo.pageIndex = 1;
        this.getList();
      },
      getList() {
        this.pageInfo.loading = true;
        this.pageInfo.list =[];
        this.pageInfo.total = 0;
          getAnalysis(this.getParams()).then(res => {
          this.pageInfo.loading = false;
          if (res.result === 0 && res.data && res.data.items) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        });
      },
      getParams() {
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        if (this.formData.onlineRange && this.formData.onlineRange.length > 0) {
            params['range'] = this.formData.onlineRange;
        }
        if (this.formData.range && this.formData.range.length > 0) {
            params['onlineRange'] = this.formData.range;
        }
        if (this.formData.account) {
          params['adName'] = this.formData.account;
        }
        if (this.formData.dateType) {
          params['dateType'] = Number(this.formData.dateType);
        }
        if (this.formData.incomeRate) {
          params['incomeRate'] = parseFloat(this.formData.incomeRate) / 100 + '';
        }
        if (this.formData.adName) {
          params['adName'] = this.formData.adName;
        }
        return params;
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.getList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.getList();
      },
      filterHandler(value, row, column) {
        console.log(value, row, column);
        // const property = column['property'];
        // return row[property] === value;
        return true;
      },
      filterHandler2() {
        this.getList();
      },
      // 选择结算日期
      changeDate(dates) {
        if (dates) {
          this.formData.startDate = dates[0];
          this.formData.endDate = dates[1];
        } else {
          this.formData.startDate = '';
          this.formData.endDate = ''
        }
      },
      initDataPicker() {
        // 初始化date和fordate的值
        const dateStart = dateFormat(new Date(new Date(Date.now() - this.days * 24 * 60 * 60 * 1000).setHours(0, 0, 0, 0)), 'yyyy-MM-dd');
        const dateEnd = dateFormat(new Date(new Date().setHours(0, 0, 0, 0)), 'yyyy-MM-dd');
        this.formData.date = [dateStart, dateEnd];
        this.changeDate(this.formData.date);
        //初始化datepicker最大时间和跨度和选中回调和快捷选择时间
        this.pickerOptions = {
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 0; // 最大天数
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - times * days);
                end.setTime(end.getTime() - times * days);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '7天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 6; // 最大天数
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + times * days);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '30天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 29; // 最大天数
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + times * days);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '本周',
              onClick(picker) {
                const date = new Date();
                const week = date.getDay();
                const end = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 6 - week);
                console.log(date.getDate(), week);
                const start = new Date(date.getFullYear(), date.getMonth(), date.getDate() - week);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '本月',
              onClick(picker) {
                const date = new Date();
                const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);
                nextMonth.setDate(0);
                const end = new Date(date.getFullYear(), date.getMonth(), nextMonth.getDate());
                const start = new Date(date.getFullYear(), date.getMonth(), 1);
                picker.$emit('pick', [start, end]);
              }
            },
          ],
        }
      },
    },

    computed: {},

    watch: {},
  }
</script>

<style scoped lang="less">
  .operation {
    padding: 20px;
  }
  .bg{
    background-color: #fff;
    border: 1px solid #EBEEF5;
    box-sizing: border-box;
    .input-box{
      padding: 15px;
      display: flex;
      align-items: center;
      .label{
        width: 50px;
        text-align: center;
      }
    }
    .btn-box{
      border-top: 1px solid #EBEEF5;
      padding: 15px;
      text-align: right;
    }
  }
</style>
