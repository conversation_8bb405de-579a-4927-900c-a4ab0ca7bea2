<template>
  <div v-loading="pageInfo.loading">

    <div class="operation">
      <el-form :inline="true" label-position="right" label-width="100px">
        <el-form-item label="提交日期：" prop="date">
          <el-date-picker
            v-model="formData.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="changeDate"
            :picker-options="pickerOptions"
            :editable="false"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="商家账号：">
          <el-input class="inline-input" v-model.trim="formData.account" placeholder="请输入商家主账号" clearable></el-input>
        </el-form-item>
        <!--按钮-->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="queryBtn">查询</el-button>
          <el-button type="success" icon="el-icon-download" @click="exportBtn">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <!--列表 start-->
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;"
                >
        <template v-for="(item, index) in colums">
          <el-table-column v-if="item.key === 'operation'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
                <el-switch
                  style="display: block"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  active-text="已联系"
                  inactive-text="未联系"
                  v-model="scope.row.statusBoolean"
                  @change="statusChange(scope.row)">
                </el-switch>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'adPhone'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
                <p v-if="scope.row.adPhone">{{scope.row.adPhone}}</p>
                <p v-else>——</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :key="index"
            :prop="item.key"
            :label="item.label"
            :width="item.width"
            :sortable="item.sortable"
            align="center"
          />
        </template>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 50]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
  import {dateFormat} from "@js/utils";
  import {getVIPList, updateVIPStatus, exportVip} from "@api/deviceAnalysisSettings/deviceAnalysisSettings.js";
  import { downloadFileRamToken } from '@/utils/menu'

  export default {
    name: "deviceAnalysisSettingsVIP",
    props: {},
    components: {},
    data() {
      return {
        formData: {
          account: '',
          startTime: '',
          endTime: '',
          date: [], // 起止日期
        },
        // 日期插件的时间选择范围
        pickerOptions: {
        },
        days: 7, // 初始化时间开始日期小于当前时间的天数
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          loading: false,
          list: [],
        },
        // 列表每一列参数
        colums: [
          {key: 'created', label: '提交时间'},
          {key: 'adName', label: '商家账号'},
          {key: 'isapprover', label: '账号类型'},
          {key: 'merchantName', label: '商家姓名'},
          {key: 'adPhone', label: '商家联系电话'},
          {key: 'status', label: '联系状态'},
          {key: 'operation', label: '操作'},
        ],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.initDataPicker();
      this.getList();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      exportBtn() {
        let params = [];
        if (this.formData.account) {
          params.push('adName=' + this.formData.account);
        }
        if (this.formData.startTime) {
          params.push('startDate=' + this.formData.startTime);
        }
        if (this.formData.endTime) {
          params.push('endDate=' + this.formData.endTime);
        }
        downloadFileRamToken(encodeURI(exportVip() + '?' + params.join('&')), true);
      },
      queryBtn() {
        this.pageInfo.pageIndex = 1;
        this.getList();
      },
      getList() {
        this.pageInfo.loading = true;
          getVIPList(this.getParams()).then(res => {
          this.pageInfo.loading = false;
          if (res.result === 0) {
              res.data.items.forEach((item)=>{
                  item['statusBoolean'] = item.status === '已联系' ? true : false;
              });
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        });
      },
      getParams() {
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        if (this.formData.account) {
          params['adName'] = this.formData.account;
        }
        if (this.formData.startTime) {
          params['startDate'] = this.formData.startTime;
        }
        if (this.formData.endTime) {
          params['endDate'] = this.formData.endTime;
        }
        return params;
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.getList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.getList();
      },
      statusChange(item) {
        // this.getList();
          const params = {
            id: item.vipServiceMerchantId,
            status: item.status === 1 ? 2 : 1,
          };
          this.pageInfo.loading = true;
          updateVIPStatus(params).then(res => {
              this.getList();
          });
      },
      // 选择结算日期
      changeDate(dates) {
        if (dates) {
          this.formData.startTime = dates[0];
          this.formData.endTime = dates[1];
        } else {
          this.formData.startTime = '';
          this.formData.endTime = ''
        }
      },
      initDataPicker() {
        // 初始化date和fordate的值
        const dateStart = dateFormat(new Date(new Date(Date.now() - this.days * 24 * 60 * 60 * 1000).setHours(0, 0, 0, 0)), 'yyyy-MM-dd');
        const dateEnd = dateFormat(new Date(new Date().setHours(0, 0, 0, 0)), 'yyyy-MM-dd');
        this.formData.date = [dateStart, dateEnd];
        this.changeDate(this.formData.date);
        //初始化datepicker最大时间和跨度和选中回调和快捷选择时间
        this.pickerOptions = {
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 0; // 最大天数
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - times * days);
                end.setTime(end.getTime() - times * days);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '7天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 6; // 最大天数
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + times * days);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '30天',
              onClick(picker) {
                const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
                const days = 29; // 最大天数
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + times * days);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '本周',
              onClick(picker) {
                const date = new Date();
                const week = date.getDay();
                const end = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 6 - week);
                console.log(date.getDate(), week);
                const start = new Date(date.getFullYear(), date.getMonth(), date.getDate() - week);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '本月',
              onClick(picker) {
                const date = new Date();
                const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);
                nextMonth.setDate(0);
                const end = new Date(date.getFullYear(), date.getMonth(), nextMonth.getDate());
                const start = new Date(date.getFullYear(), date.getMonth(), 1);
                picker.$emit('pick', [start, end]);
              }
            },
          ],
        }
      },
    },

    computed: {},

    watch: {},
  }
</script>

<style scoped lang="less">
  .operation {
    padding: 20px;
  }
</style>
