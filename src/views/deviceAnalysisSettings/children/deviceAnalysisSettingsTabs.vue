<!--
 * @Description: 设备分析功能设置标签页
 * @Author: 杨楠锋
 * @Date: 2020-02-25 11:42:21
 * @LastEditors: 杨楠锋
 * @LastEditTime: 2020-02-27 12:05:42
 -->
<template>
  <div>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="申请VIP服务商户" name="申请VIP服务商户">
          <div v-if="activeName === '申请VIP服务商户'">
            <slot name="vip"></slot>
          </div>
        </el-tab-pane>
        <el-tab-pane label="活动链接设置" name="活动链接设置">
          <div v-if="activeName === '活动链接设置'">
            <slot name="setting"></slot>
          </div>
        </el-tab-pane>
        <el-tab-pane label="设备交易数据" name="设备交易数据">
          <div v-if="activeName === '设备交易数据'">
            <slot name="list"></slot>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

</template>

<script>

export default {
  name: 'deviceAnalysisSettingsTabs',
  props: {},
  components: {},
  data() {
    return {
      activeName: '申请VIP服务商户',
    };
  },

  beforeCreate() {
  },

  created() {
  },

  beforeMount() {
  },

  mounted() {
  },

  beforeUpdate() {
  },

  updated() {
  },

  activated() {
  },

  deactivated() {
  },

  beforeDestroy() {
  },

  destroyed() {
  },

  errorCaptured() {
  },

  methods: {
    handleClick(tab, event) {
      this.activeName = tab.label;
    }
  },

  computed: {},

  watch: {},

};

</script>
<style lang="less" scoped>

</style>
