<!--
 * @Description: 设备分析功能设置
 * @Author: 杨楠锋
 * @Date: 2020-02-25 11:42:21
 * @LastEditors: 杨楠锋
 * @LastEditTime: 2020-02-27 15:25:36
 -->
<template>
  <div>
    <el-card>
      <device-analysis-settings-tabs>
        <div slot="vip">
          <device-analysis-settings-v-i-p/>
        </div>
        <div slot="setting">
          <device-analysis-settings-active/>
        </div>
        <div slot="list">
          <device-analysis-settings-transaction/>
        </div>
      </device-analysis-settings-tabs>
    </el-card>
  </div>
</template>

<script>
import DeviceAnalysisSettingsTabs
  from "@views/deviceAnalysisSettings/children/deviceAnalysisSettingsTabs";
import DeviceAnalysisSettingsVIP
  from "@views/deviceAnalysisSettings/children/deviceAnalysisSettingsVIP";
import DeviceAnalysisSettingsActive
  from "@views/deviceAnalysisSettings/children/deviceAnalysisSettingsActive";
import DeviceAnalysisSettingsTransaction
  from "@views/deviceAnalysisSettings/children/transaction/deviceAnalysisSettingsTransaction";
export default {
  name: 'deviceAnalysisSettings',
  props: {},
  components: {
    DeviceAnalysisSettingsTransaction,
    DeviceAnalysisSettingsActive, DeviceAnalysisSettingsVIP, DeviceAnalysisSettingsTabs},
  data() {
    return {
    };
  },

  beforeCreate() {
  },

  created() {
  },

  beforeMount() {
  },

  mounted() {
  },

  beforeUpdate() {
  },

  updated() {
  },

  activated() {
  },

  deactivated() {
  },

  beforeDestroy() {
  },

  destroyed() {
  },

  errorCaptured() {
  },

  methods: {

  },

  computed: {},

  watch: {},

};

</script>
<style lang="less" scoped>

</style>
