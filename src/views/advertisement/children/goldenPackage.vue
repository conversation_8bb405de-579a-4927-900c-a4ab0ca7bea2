<template>
  <div class="golden-package-page">
    <div class="package-tabbar mb-18">
      <el-radio-group v-model="config.packageId" @change="changePackage">
        <el-radio-button label="0">吴晓波频道</el-radio-button>
        <el-radio-button label="1">美团民宿</el-radio-button>
        <el-radio-button label="2">趣头条</el-radio-button>
      </el-radio-group>
    </div>
    <div class="package-config">
      <el-form :inline="true">
        <el-form-item class="price-item" label="购买金额:">
          <el-input type="text" v-model.trim="config.amount"></el-input>
        </el-form-item>
        <el-form-item class="coin-item" label="赠送币数:">
          <el-input type="text" v-model.trim="config.coins"></el-input>
        </el-form-item>
        <el-form-item class="price-item" label="额外补贴金额:">
          <el-input type="text" v-model.trim="config.subsidy"></el-input>
        </el-form-item>
      </el-form>
      <el-button class="mb-18" type="primary" @click="saveConfigFn">保存设置</el-button>
    </div>
    <div class="package-search">
      <el-form :inline="true">
        <el-form-item label="会员ID">
          <el-input type="text" v-model.trim="config.userId"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="rangeDateSearch(1)">昨天</el-button>
          <el-button type="primary" @click="rangeDateSearch(7)">7天</el-button>
          <el-button type="primary" @click="rangeDateSearch(15)">15天</el-button>
          <el-button type="primary" @click="rangeDateSearch(30)">30天</el-button>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="config.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :unlink-panels="true"
            @change="changeDate">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="querySearch">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="exportExcel">导出Excel</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="package-table mb-18">
      <el-table :data="table.list" style="width: 100%;">
        <el-table-column prop="orderId" label="订单号" align="center"></el-table-column>
        <el-table-column prop="lyyUserId" label="会员ID" align="center"></el-table-column>
        <el-table-column prop="userName" label="会员昵称" align="center"></el-table-column>
        <el-table-column prop="created" label="购买时间" align="center"></el-table-column>
        <el-table-column prop="amount" label="购买价格" align="center"></el-table-column>
        <el-table-column prop="coins" label="赠送币数" align="center"></el-table-column>
        <el-table-column prop="lyyDistributorId" label="购买所在商户ID" align="center"></el-table-column>
      </el-table>
    </div>
    <div class="package-footer">
      <el-button type="primary" @click="handlePagation('prev')">上一页</el-button>
      <el-button type="primary" @click="handlePagation('next')">下一页</el-button>
    </div>
  </div>
</template>
<script>
import { getConfig, saveConfig, getList } from "@/api/advertisement/goldenPackage";
import { formatDate } from '@/utils/utils'
import { downloadFileRamToken } from '@/utils/menu'
export default {
  name: 'GoldenPackage',
  data() {
    return {
      config:{ // 查询配置变量集合
        packageId: 0, // 导航值,0-吴晓波频道 1-美团民宿 2-趣头条
        packageName: '', // 当前查看的套餐
        remark: '', //标识
        amount: '', // 购买金额
        coins: '', // 赠送币数
        subsidy: '', // 额外补贴金额
        range: null, // 查询的时间范围段
        date: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')], // 查询时间
        userId: null // 会员ID
      },
      table: { // 表单变量集合
        list: [], // 表格的数据列表
      },
      pagination: { // 分页器变量集合
        page: 1, // 页数
        size: 20, //条数
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.changePackage(0);
    })
  },
  methods: {
    /**
     * 获取表单数据
     */
    getListFn() {
      let params = {
        pageIndex: this.pagination.page,
        pageSize: this.pagination.size,
        startDate: this.config.date[0],
        endDate: this.config.date[1],
        lyyPackageId: this.config.packageId
      }
      if (this.config.userId) {
        params.lyyUserId = this.config.userId
      }
      getList(params).then(res => {
        if (res && res.result == 0) {
          this.table.list = res.data;
        } else {
          this.$message.error(res.description);
        }
      })
    },
    /**
     * 获取配置
     */
    getConfigFn() {
      getConfig(this.config.packageId).then(res => {
        if (res && res.result == 0) {
          this.config.amount = res.data.amount;
          this.config.coins = res.data.coins;
          this.config.subsidy = res.data.subsidy;
          this.config.packageName = res.data.packageName;
          this.config.remark = res.data.remark;
        } else {
          this.$message.error(res.description);
        }
      })
    },
    /**
     * 保存配置
     */
    saveConfigFn() {
      if (!this.validateConfig()) {
        return;
      }
      let params = {
        lyyPackageId: this.config.packageId,
        packageName: this.config.packageName,
        amount: this.config.amount,
        coins: this.config.coins,
        subsidy: this.config.subsidy,
        remark: this.config.remark
      }
      saveConfig(params).then(res => {
        if (res && res.result == 0 && res.data) {
          this.$message.success('保存成功');
        } else {
          this.$message.error(res.description);
        }
      })
    },
    /**
     * 切换套餐
     */
    changePackage(type) {
      this.config.packageId = type;
      this.getListFn();
      this.getConfigFn();
    },
    /**
     * 切换查询时间段
     */
    rangeDateSearch(range) {
      this.config.date = [formatDate(new Date() - (range * 24 * 60 * 60 *1000), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')];
      this.getListFn();
    },
    /**
     * 时间组件查询时间修改
     */
    changeDate(dates) {
      this.config.date = dates ? [dates[0], dates[1]] : [];
    },
    /**
     * 查询
     */
    querySearch() {
      let numberReg = /^\+?[0-9][0-9]*$/;
      if (this.config.date.length != 2) {
        this.$message.error('请选择正确的查询时间');
        return;
      }
      if (this.config.userId && !numberReg.test(this.config.userId)) {
        this.$message.error('请选择正确的会员ID');
        return;
      }
      this.pagination.page = 1;
      this.getListFn();
    },
    /**
     * 导出excel
     */
    exportExcel() {
      downloadFileRamToken(`/package/user/export?lyyPackageId=${this.config.packageId}`);
    },
    /**
     * 翻页(上一页，下一页)
     */
    handlePagation(type) {
      if (type == 'prev') {
        if (this.pagination.page == 1) {
          this.$message.error('已经是第一页');
          return;
        }
        this.pagination.page--;
      } else if (type == 'next') {
        this.pagination.page++;
      }
      this.getListFn();
    },
    /**
     * 校验保存配置
     */
    validateConfig() {
      let priceReg = /^(\d{1,2}(\.\d{1,2})?|100|100.0|100.00)$/; // 价格正则
      let coinsReg = /^([1-9]\d{0,2}|1000)$/; // 正整数规则1-1000
      if (!priceReg.test(this.config.amount) || this.config.amount == 0) {
        this.$message.error('请输入正确的购买金额');
        return false;
      }
      if (!coinsReg.test(this.config.coins)) {
        this.$message.error('请输入正确的赠送币数');
        return false;
      }
      if (this.config.subsidy && !priceReg.test(this.config.subsidy)) {
        this.$message.error('请输入正确的额外补贴金额');
        return false;
      }
      return true;
    }
  }
}
</script>
<style lang="less" scoped>
.price-item:after {
  content: '元';
  display: inline-block;
}
.coin-item:after {
  content: '币';
  display: inline-block;
}
.mb-18 {
  margin-bottom: 18px;
}
</style>