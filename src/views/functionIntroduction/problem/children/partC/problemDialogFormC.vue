<!--
日期：2019/10/24
功能：模板组件
作者：杨楠锋
-->
<template>
  <div>
    <el-dialog
      :title="(formData.lyyFunctionCenterQuestionId ? ' 编辑':' 新建') + '问题'"
      :visible.sync="visible"
      v-if="visible"
      width="535px"
      :before-close="handleClose">
      <el-form :inline="false"
               label-position="right"
               :model="formData"
               ref="ruleForm"
               :rules="rules"
               v-loading="visibleLoading"
               label-width="110px">
        <el-form-item label="标题" prop="title">
          <el-input v-model.trim="formData.title" :maxLength="maxTitle" placeholder="请输入标题">
            <template slot="append">
              <span v-if="formData.title && typeof formData.title === 'string' && formData.title.length > 0">{{formData.title.length}}</span>
              <span v-else>0</span>
              <span>/{{maxTitle}}</span>
            </template>
          </el-input>
        </el-form-item>
        <!-- <el-form-item label="链接：" prop="url">
          <el-input v-model.trim="formData.url" maxLength="255" placeholder="请输入链接"></el-input>
        </el-form-item> -->
        <el-form-item class="description" label="详细描述" prop="description">
          <!-- <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"
            placeholder="请输入详细描述"
            v-model="formData.description">
          </el-input> -->
          <quillEditor v-model="formData.description"></quillEditor>
        </el-form-item>
        <el-form-item label="设备类型" prop="equipmentTypeIdList">
          <el-select v-model="formData.equipmentTypeIdList" multiple placeholder="请选择设备类型">
            <el-option
              v-for="item in equipmentsTypeOptions"
              :key="item.lyyEquipmentTypeId"
              :label="item.typeName"
              :value="item.lyyEquipmentTypeId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="展示栏目" prop="columnValue">
          <el-select v-model="formData.columnValue" placeholder="请选择展示栏目">
            <el-option
              v-for="item in columnOptions"
              :key="item.value"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="图标上传：" prop="iconUrl"> -->
          <!--这个input不显示，只是为了绑定formData.imageUrlCompress，这样这个值为空的时候会显示提示信息-->
          <!-- <el-input v-show="false" v-model="formData.iconUrl"></el-input>
          <i v-if="formData.iconUrl" class="el-icon-error img-close" @click="imageClear()"></i>
          <el-upload class="avatar-uploader upload-box"
                     :action="problemImageUpload"
                     :show-file-list="false"
                     drag
                     ref="uploadRef"
                     accept="image/jpeg, image/png"
                     :on-success="handleAvatarSuccess"
                     :before-upload="beforeAvatarUpload">
            <div v-loading="imageLoading">
              <div v-if="formData.iconUrl">
                <img :src="imageUrlPrefix + formData.iconUrl"
                     ref="imgRef"
                     class="avatar">
              </div>
              <i v-else
                 class="el-icon-plus avatar-uploader-icon"></i>
            </div>
          </el-upload>
          <p>建议尺寸:60*60，{{fileMaxSize}}M内，支持JPG，PNG</p>
        </el-form-item> -->
        <el-form-item label="排序：" prop="seq">
          <el-input v-model.number="formData.seq" placeholder="请输入排序" min="0"></el-input>
        </el-form-item>
        <el-form-item label="是否上架" prop="ishomedisplay">
          <el-radio-group v-model="formData.ishomedisplay" @change="initRuleHomeSeq()">
            <el-radio v-for="(item, index) in ishomedisplayArr" :key="index" :label="item.key">{{item.value}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="首页排序：" prop="homeSeq" v-if="isShowHomeSeq">
          <el-input v-model.number="formData.homeSeq" placeholder="请输入首页排序" min="0"></el-input>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {problemIshomedisplayArr} from '../../problem';
  import {problemImageUpload, problemSaveForCustomer, getColumnValueList} from '@api/functionIntroduction/problem';
  import {equipmentTypeList} from '@api/faceBrush';
  import quillEditor from '@/components/quillEditor/quillEditor'

  export default {
    name: 'problemDialogFormC',
    props: {},
    components: {
      quillEditor,
    },
    data () {
      return {
        visible: false,
        visibleLoading: false,
        imageLoading: false,
        maxTitle: 8,
        formData: {},
        problemImageUpload: problemImageUpload(),
        fileMaxSize: 15, // 图片最大体积
        ishomedisplayArr: problemIshomedisplayArr,
        imageUrlPrefix: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/',
        isShowHomeSeq: true,
        equipmentsTypeOptions: [],
        columnOptions: []
      };
    },

    beforeCreate () {
    },

    created () {
      this.getEquipmentTypeList()
      this.getColumnValueList()
    },

    beforeMount () {
    },

    mounted () {
    },

    beforeUpdate () {
    },

    updated () {
    },

    activated () {
    },

    deactivated () {
    },

    beforeDestroy () {
    },

    destroyed () {
    },

    errorCaptured () {
    },

    methods: {
      initRule () {
        this.rules = {
          title: [
            {required: true, message: '请输入标题', trigger: 'change'},
            {max: this.maxTitle, message: `标题不能超过${this.maxTitle}字`, trigger: 'change'}
          ],
          description: [
            {required: true, message: '请输入详细描述', trigger: 'change'},
          ],
          equipmentTypeIdList: [
            {required: true, message: '请选择设备类型', trigger: 'change'},
          ],
          columnValue: [
            {required: true, message: '请选择展示栏目', trigger: 'change'},
          ],
          // url: [
          //   {required: true, message: '请选择链接', trigger: 'change'},
          //   {max: 255, message: `链接不能超过255字`, trigger: 'change'}
          // ],
          iconUrl: [
            {required: true, message: '请上传图标', trigger: 'change'}
          ],
          seq: [
            {required: true, message: '请输入教程排序值，数字越小，排序越前，0为最小值', trigger: 'change'},
            {required: true, type: 'number', message: '教程排序值只能输入数字', trigger: 'change'},
            {required: true, type: 'number', min: 0, message: `教程排序值0为最小值`, trigger: 'change'},
          ],
          ishomedisplay: [
            {required: true, message: '请选择是否上架', trigger: 'change'}
          ],
        };
      },
      initRuleHomeSeq() {
        this.isShowHomeSeq = false;
        const flag = this.formData.ishomedisplay === 'Y';
        if (flag) {
          this.rules['homeSeq'] = [
            {required: flag, message: '请输入排序值，数字越小，排序越前，0为最小值', trigger: 'change'},
            {required: flag, type: 'number', message: '排序值只能输入数字', trigger: 'change'}
          ];
        } else {
          this.rules['homeSeq'] = [
            {required: flag, message: '请输入排序值，数字越小，排序越前，0为最小值', trigger: 'change'},
          ];
        }
        this.$nextTick(() => {
          this.isShowHomeSeq = true;
        });
      },
      initFormData () {
        this.formData = {
          lyyFunctionCenterQuestionId: '',
          title: '',
          description: '',
          equipmentTypeIdList: [],
          columnValue: '',
          url: '',
          iconUrl: '',
          seq: '',
          ishomedisplay: 'Y',
          homeSeq: '',
          type: 'customer',
        };
      },
      open (item) {
        this.initRule();
        this.initFormData();
        this.formData = Object.assign(this.formData, item);
        this.initRuleHomeSeq();
        this.visible = true;
      },
      handleClose () {
        this.visible = false;
      },
      handleSave () {
        this.isParamsComplate(() => {
          this.visibleLoading = true;
          problemSaveForCustomer(this.getParams()).then(res => {
              this.visibleLoading = false;
              if (res.result === 0) {
                this.$message.success('保存成功');
                this.$emit('confirm');
                this.handleClose();
              }
            }
          );
        });
      },
      getParams () {
        let params = {
          title: this.formData.title,
          url: this.formData.url,
          seq: this.formData.seq,
          iconUrl: this.formData.iconUrl,
          ishomedisplay: this.formData.ishomedisplay,
          description: this.formData.description,
          equipmentTypeIdList: this.formData.equipmentTypeIdList,
          columnValue: this.formData.columnValue
        };
        if (this.formData.lyyFunctionCenterQuestionId) {
          params['lyyFunctionCenterQuestionId'] = this.formData.lyyFunctionCenterQuestionId;
        }
        if (this.formData.homeSeq) {
          params['homeSeq'] = this.formData.homeSeq;
        }
        return params;
      },
      // 参数是否完整
      async isParamsComplate (call) {
        await this.$refs['ruleForm'].validate((isSuccess, valid) => {
          if (isSuccess) {
            call();
            return true;
          } else {
            let sign = '';
            if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]['message'];
              this.$message.error(sign);
            }
            return false;
          }
        });
      },
      // 上传到后台前的图片验证
      beforeAvatarUpload (file) {
        const isJPG = file.type === 'image/jpeg';
        const isPNG = file.type === 'image/png';
        const isQualified = file.size / 1024 / 1024 < this.fileMaxSize;
        if (!isJPG && !isPNG) {
          this.$message.error('上传物料图片只能是 JPG或PNG 格式!');
        }
        if (!isQualified) {
          this.$message.error(`上传头像图片大小不能超过 ${this.fileMaxSize}MB!`);
        }
        if ((isJPG || isPNG) && isQualified) {
          this.imageLoading = true;
          return true;
        }
      },
      // 上传图片的回调
      handleAvatarSuccess (res, file) {
        this.imageLoading = false;
        if (res.result === 1) {
          this.formData.iconUrl = res.para;
        } else {
          this.$message.error('上传失败');
          this.imageClear();
        }
      },
      // 清除图片
      imageClear () {
        this.formData.iconUrl = '';
      },
      // 获取设备类型列表
      async getEquipmentTypeList() {
        const res= await equipmentTypeList()
        if (res.result === 0 && res.data) {
          this.equipmentsTypeOptions = res.data
        }
      },
      // 获取栏目列表
      async getColumnValueList() {
        const res = await getColumnValueList()
        if (res.result === 0 && res.data) {
          this.columnOptions = res.data.columnList
        }
      }
    },

    computed: {},

    watch: {}

  };

</script>

<style lang="less" scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #000;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }

  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }

  .upload-box {
    /deep/ .el-upload-dragger {
      width: 100px;
      height: 100px;
    }
  }
  .img-close{
    position: absolute;
    top: 0;
    left: 70px;
    z-index: 100;
    font-size: 30px;
    transform: translate(50%,-50%);
    cursor: pointer;
  }
  .remark-count{
    float: right;
  }
  .description {
    min-height: 500px;
  }
</style>
