<!--
日期：2019/10/22
功能：常见问题管理
作者：杨楠锋
-->
<template>
  <div v-loading="pageInfo.loading">
    <el-card>
      <el-form :inline="true" label-position="right" label-width="100px">
        <el-form-item label="问题标题：">
          <el-input class="inline-input" v-model.trim="formData.name" placeholder="请输入问题标题" clearable></el-input>
        </el-form-item>
        <!--按钮-->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="queryBtn">查询</el-button>
          <el-button type="success" icon="el-icon-circle-plus-outline" @click="addBtn">新建问题</el-button>
        </el-form-item>
      </el-form>
      <div>
        <!--列表 start-->
        <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;"
                  >
          <template v-for="(item, index) in colums">
            <el-table-column v-if="item.key === 'operation'"
                             :key="index"
                             :prop="item.key"
                             :label="item.label"
                             :width="item.width"
                             :sortable="item.sortable"
                             align="center">
              <template slot-scope="scope">
                <div>
                  <el-button type="text"
                             class="info-btn"
                             @click="editBtn(scope.row)">编辑
                  </el-button>
                  <el-button type="text"
                             class="info-btn"
                             @click="homedisplayBtn(scope.row)">
                    <span :class="scope.row.ishomedisplay | arrGetValue(ishomedisplayArr, 'key', 'show')">{{scope.row.ishomedisplay | arrGetValue(ishomedisplayArr, 'key', 'btnText')}}</span>
                  </el-button>
                  <el-button type="text"
                             class="info-btn"
                             @click="deleteBtn(scope.row.lyyFunctionCenterQuestionId)">
                    <span class="danger">删除</span>
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-else-if="item.key === 'iconUrl'"
                             :key="index"
                             :prop="item.key"
                             :label="item.label"
                             :width="item.width"
                             :sortable="item.sortable"
                             align="center">
              <template slot-scope="scope">
                <img :src="imageUrlPrefix + scope.row.iconUrl" width="60" height="60" v-viewer/>
              </template>
            </el-table-column>
            <el-table-column v-else-if="item.key === 'ishomedisplay'"
                             :key="index"
                             :prop="item.key"
                             :label="item.label"
                             :width="item.width"
                             :sortable="item.sortable"
                             align="center">
              <template slot-scope="scope">
                <div>
                  {{scope.row.ishomedisplay | arrGetValue(ishomedisplayArr, 'key', 'value')}}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-else
              :key="index"
              :prop="item.key"
              :label="item.label"
              :width="item.width"
              :sortable="item.sortable"
              align="center"
            />
          </template>
        </el-table>
        <el-pagination
          :page-sizes="[10, 20, 50]"
          :page-size="pageInfo.pageSize"
          :total="pageInfo.total"
          :current-page="pageInfo.pageIndex"
          background
          layout="total, prev, pager, next, sizes, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
        <!--列表 end-->
        <!--删除弹窗-->
        <problem-dialog-delete ref="deleteRef" @confirm="getList()"/>
        <!--下架弹窗-->
        <problem-dialog-homedisplay ref="homedisplayRef" @confirm="getList()"/>
        <!--新建或编辑弹窗-->
        <problem-dialog-form ref="formRef" @confirm="getList()"/>
      </div>
    </el-card>
  </div>
</template>

<script>
  import {problemIshomedisplayArr} from "@views/functionIntroduction/problem/problem";
  import {problemQuery} from "@api/functionIntroduction/problem";
  import ProblemDialogDelete from "@views/functionIntroduction/problem/children/problemDialogDelete";
  import ProblemDialogForm from "@views/functionIntroduction/problem/children/problemDialogForm";
  import 'viewerjs/dist/viewer.css';
  import Viewer from 'v-viewer';
  import Vue from 'vue';
  import ProblemDialogHomedisplay from '@views/functionIntroduction/problem/children/problemDialogHomedisplay';
  Vue.use(Viewer);
  export default {
    name: "operatingPlatformProblem",
    props: {},
    components: {ProblemDialogHomedisplay, ProblemDialogForm, ProblemDialogDelete},
    data() {
      return {
        formData: {name: ''},
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          loading: false,
          list: [],
        },
        // 列表每一列参数
        colums: [
          {key: 'operation', label: '操作'},
          {key: 'seq', label: '排序',sortable: true},
          {key: 'title', label: '标题'},
          {key: 'iconUrl', label: '图标'},
          {key: 'ishomedisplay', label: '上架首页',sortable: true},
          {key: 'homeSeq', label: '首页排序',sortable: true},
          {key: 'updated', label: '更新时间',sortable: true},
        ],
        imageUrlPrefix: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/',
        ishomedisplayArr: problemIshomedisplayArr,
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.getList();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      queryBtn() {
        this.pageInfo.pageSize = 10;
        this.pageInfo.pageIndex = 1;
        this.getList();
      },
      addBtn() {
        this.$refs.formRef.open({});
      },
      editBtn(item) {
        this.$refs.formRef.open(item);
      },
      homedisplayBtn(item) {
        this.$refs.homedisplayRef.open(item);
      },
      deleteBtn(lyyFunctionCenterQuestionId) {
        this.$refs.deleteRef.open(lyyFunctionCenterQuestionId);
      },
      getList() {
        this.pageInfo.loading = true;
        problemQuery(this.getParams()).then(res => {
          this.pageInfo.loading = false;
          if (res.result === 0) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        });
      },
      getParams() {
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        if (this.formData.name) {
          params['name'] = this.formData.name;
        }
        return params;
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.getList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.getList();
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="less" scoped>
  .danger {
    margin-left: 10px;
    clolor: #f78989;
    cursor: pointer;
  }

  .uninstall {
    color: #909399;
    margin-left: 10px;
    cursor: pointer;
  }
</style>
