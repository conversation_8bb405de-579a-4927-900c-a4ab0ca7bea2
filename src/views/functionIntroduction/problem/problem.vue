<template>
  <div class="problem">
    <el-radio-group v-model="tabPosition" style="margin-bottom: 30px;">
      <el-radio-button label="operating">运营平台常见问题管理</el-radio-button>
      <el-radio-button label="partC">C端平台常见问题管理</el-radio-button>
    </el-radio-group>
    <operatingPlatformProblem v-if="tabPosition === 'operating'"></operatingPlatformProblem>
    <CPlatformProblem v-else-if="tabPosition === 'partC'"></CPlatformProblem>
  </div>
</template>
<script>
import operatingPlatformProblem from './operatingPlatformProblem'
import CPlatformProblem from './CPlatformProblem'
export default {
  name: 'problem',
  components: {
    operatingPlatformProblem,
    CPlatformProblem,
  },
  data() {
    return {
      tabPosition: 'operating',
    }
  }
}
</script>
<style lang="less" scoped>

</style>


