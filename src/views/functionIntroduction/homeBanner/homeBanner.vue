<!--
日期：2019/10/22
功能：模板组件
作者：杨楠锋
-->
<template>
  <div v-loading="pageInfo.loading">
    <el-form :inline="true" label-position="left" label-width="100px">
      <el-form-item label="活动标题：">
        <el-input class="inline-input" v-model.trim="formData.name" placeholder="请输入活动标题" clearable></el-input>
      </el-form-item>
      <!--按钮-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="queryBtn">查询</el-button>
        <el-button type="success" icon="el-icon-circle-plus-outline" @click="addBtn">新建活动</el-button>
      </el-form-item>
    </el-form>
    <div>
      <!--列表 start-->
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;"
                >
        <template v-for="(item, index) in colums">
          <el-table-column v-if="item.key === 'operation'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
                <el-button type="text"
                           class="info-btn"
                           @click="editBtn(scope.row)">编辑
                </el-button>
                <span @click="displayBtn(scope.row)"
                      class="info-btn uninstall">
                  <span :class="scope.row.isdisplay | arrGetValue(isdisplayArr, 'key', 'show')">{{scope.row.isdisplay | arrGetValue(isdisplayArr, 'key', 'btnText')}}</span>
                </span>
                <span @click="deleteBtn(scope.row.lyyFunctionCenterHomeBannerId)"
                      class="info-btn danger">
                  删除
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'coverImgUrl'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <img :src="imageUrlPrefix + scope.row.coverImgUrl" width="60" height="60" v-viewer/>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'isdisplay'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
                {{scope.row.isdisplay | arrGetValue(isdisplayArr, 'key', 'value')}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :key="index"
            :prop="item.key"
            :label="item.label"
            :width="item.width"
            :sortable="item.sortable"
            align="center"
          />
        </template>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <!--列表 end-->
      <home-banner-dialog-delete ref="deleteRef" @confirm="getList()"/>
      <home-banner-dialog-display ref="displayRef" @confirm="getList()"/>
      <home-banner-dialog-form ref="formRef" @confirm="getList()"/>
    </div>
  </div>
</template>

<script>
  import {homeBannerQuery} from "@api/functionIntroduction/homeBanner";
  import {homeBannerIsdisplayArr} from '@views/functionIntroduction/homeBanner/homeBanner.js';
  import 'viewerjs/dist/viewer.css';
  import Viewer from 'v-viewer';
  import Vue from 'vue';
  import HomeBannerDialogDelete from '@views/functionIntroduction/homeBanner/children/homeBannerDialogDelete';
  import HomeBannerDialogDisplay from '@views/functionIntroduction/homeBanner/children/homeBannerDialogDisplay';
  import HomeBannerDialogForm from '@views/functionIntroduction/homeBanner/children/homeBannerDialogForm';
  Vue.use(Viewer);
  export default {
    name: "homeBanner",
    props: {},
    components: {
      HomeBannerDialogForm,
      HomeBannerDialogDisplay,
      HomeBannerDialogDelete},
    data() {
      return {
        formData: {name: ''},
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          loading: false,
          list: [],
        },
        // 列表每一列参数
        colums: [
          {key: 'operation', label: '操作'},
          {key: 'seq', label: '排序', sortable: true},
          {key: 'title', label: '活动标题'},
          {key: 'coverImgUrl', label: '活动封面'},
          {key: 'updated', label: '更新时间', sortable: true},
          {key: 'isdisplay', label: '上架状态', sortable: true},
        ],
        imageUrlPrefix: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/',
        isdisplayArr: homeBannerIsdisplayArr,
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.queryBtn();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      queryBtn() {
        this.pageInfo.pageSize = 10;
        this.pageInfo.pageIndex = 1;
        this.getList();
      },
      addBtn() {
        this.$refs.formRef.open({});
      },
      editBtn(item) {
        this.$refs.formRef.open(item);
      },
      displayBtn(item) {
        this.$refs.displayRef.open(item);
      },
      deleteBtn(bannerIds) {
        this.$refs.deleteRef.open(bannerIds);
      },
      getList() {
        this.pageInfo.loading = true;
        homeBannerQuery(this.getParams()).then(res => {
          this.pageInfo.loading = false;
          if (res.result === 0) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        });
      },
      getParams() {
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        if (this.formData.name) {
          params['name'] = this.formData.name;
        }
        return params;
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.getList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.getList();
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="less" scoped>
  .danger {
    margin-left: 10px;
    clolor: #f78989;
    cursor: pointer;
  }

  .uninstall {
    color: #909399;
    margin-left: 10px;
    cursor: pointer;
  }
</style>
