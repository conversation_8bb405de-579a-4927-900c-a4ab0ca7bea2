<!--
日期：2019/9/18
功能：模板组件
作者：杨楠锋
-->
<template>
  <div>
    <el-dialog
      :title="formData.isdisplay | arrGetValue(isdisplayArr, 'key', 'btnText')"
      :visible.sync="visible"
      v-if="visible"
      width="30%"
      :before-close="handleClose">
      <el-form :inline="false"
               label-position="right"
               :model="formData"
               ref="ruleForm"
               :rules="rules"
               v-loading="visibleLoading"
               label-width="110px">
        <p v-if="formData.isdisplay === 'Y'">确定从下架该活动？</p>
        <el-form-item label="排序：" prop="seq" v-else>
          <el-input v-model.number="formData.seq" placeholder="请输入排序" min="0"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

  import {homeBannerIsdisplayArr} from '@views/functionIntroduction/homeBanner/homeBanner.js';
  import {homeBannerDisplay} from '@api/functionIntroduction/homeBanner';

  export default {
    name: 'homeBannerDialogDisplay',
    props: {},
    components: {},
    data () {
      return {
        visible: false,
        visibleLoading: false,
        formData: {},
        isdisplayArr: homeBannerIsdisplayArr,
      };
    },

    beforeCreate () {
    },

    created () {
    },

    beforeMount () {
    },

    mounted () {
    },

    beforeUpdate () {
    },

    updated () {
    },

    activated () {
    },

    deactivated () {
    },

    beforeDestroy () {
    },

    destroyed () {
    },

    errorCaptured () {
    },

    methods: {
      initRule () {
        this.rules = {
          seq: [
            {required: true, message: '请输入首页排序值，数字越小，排序越前，0为最小值', trigger: 'change'},
            {required: true, type: 'number', message: '首页排序值只能输入数字', trigger: 'change'},
            {required: true, type: 'number', min: 0, message: `首页排序值0为最小值`, trigger: 'change'}
          ]
        };
      },
      initFormData () {
        this.formData = {
          lyyFunctionCenterHomeBannerId: '',
          title: '',
          url: '',
          iconUrl: '',
          seq: '',
          isdisplay: 'Y',
          homeSeq: ''
        };
      },
      open (item) {
        this.initRule();
        this.initFormData();
        this.formData = Object.assign(this.formData, item);
        this.visible = true;
      },
      handleClose () {
        this.visible = false;
      },
      handleSave () {
        if (this.formData.isdisplay === 'Y') {
          this.postSave();
        } else {
          this.isParamsComplate(() => {
            this.postSave();
          });
        }
      },
      postSave () {
        this.visibleLoading = true;
        homeBannerDisplay(this.getParams()).then(res => {
            this.visibleLoading = false;
            if (res.result === 0) {
              this.$message.success('保存成功');
              this.$emit('confirm');
              this.handleClose();
            }
          }
        );
      },
      getParams () {
        let params = {
          bannerId: this.formData.lyyFunctionCenterHomeBannerId,
          seq: null
        };
        if (this.formData.isdisplay !== 'Y') {
          params['seq'] = this.formData.seq;
        }
        return params;
      },
      // 参数是否完整
      async isParamsComplate (call) {
        await this.$refs['ruleForm'].validate((isSuccess, valid) => {
          if (isSuccess) {
            call();
            return true;
          } else {
            let sign = '';
            if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]['message'];
              this.$message.error(sign);
            }
            return false;
          }
        });
      }
    },

    computed: {},

    watch: {}

  };

</script>
<style lang="css" scoped>
</style>
