<!--
日期：2019/10/23
功能：教程内容管理
作者：杨楠锋
-->
<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="教程内容管理" name="教程内容管理">
        <div v-if="activeName === '教程内容管理'">
          <slot name="content"></slot>
        </div>
      </el-tab-pane>
      <el-tab-pane label="教程类型管理" name="教程类型管理">
        <div v-if="activeName === '教程类型管理'">
          <slot name="type"></slot>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  export default {
    name: "tutorialTabs",
    props: {},
    components: {},
    data() {
      return {
        activeName: '教程内容管理',
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      handleClick(tab, event) {
        this.activeName = tab.label;
      }
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="css" scoped>
</style>
