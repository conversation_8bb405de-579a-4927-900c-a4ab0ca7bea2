<!--
日期：2019/10/29
功能：模板组件
作者：杨楠锋
-->
<template>
  <div>
    <el-card class="tutorial-sub">
      <div slot="header" class="clearfix">
        {{formData.lyyFunctionCenterCourseId ? ' 编辑':' 新建'}}教程内容
      </div>
      <el-form :inline="false"
               v-if="visible"
               label-position="right"
               :model="formData"
               ref="ruleForm"
               :rules="rules"
               v-loading="visibleLoading"
               label-width="125px">
        <el-form-item label="教程标题" prop="title">
          <el-input v-model.trim="formData.title" :maxLength="maxTitle" placeholder="请输入标题" class="title">
            <template slot="append">
              <span v-if="formData.title && typeof formData.title === 'string' && formData.title.length > 0">{{formData.title.length}}</span>
              <span v-else>0</span>
              <span>/{{maxTitle}}</span>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="教程封面：" prop="coverImgUrl">
          <!--这个input不显示，只是为了绑定formData.imageUrlCompress，这样这个值为空的时候会显示提示信息-->
          <el-input v-show="false" v-model="formData.coverImgUrl"></el-input>
          <i v-if="formData.coverImgUrl" class="el-icon-error img-close" @click="imageClear()"></i>
          <el-upload class="avatar-uploader upload-box"
                     :action="tutorialImageUpload"
                     :headers="headerRamToken"
                     :show-file-list="false"
                     drag
                     ref="uploadRef"
                     accept="image/jpeg, image/png"
                     :on-success="handleAvatarSuccess"
                     :before-upload="beforeAvatarUpload">
            <div v-loading="imageLoading">
              <div v-if="formData.coverImgUrl">
                <img :src="imageUrlPrefix + formData.coverImgUrl"
                     ref="imgRef"
                     class="avatar">
              </div>
              <i v-else
                 class="el-icon-plus avatar-uploader-icon"></i>
            </div>
          </el-upload>
          <p>建议尺寸:344*220，{{fileMaxSize}}M内，支持JPG，PNG</p>
        </el-form-item>
        <el-form-item label="教程类型" prop="lyyFunctionCenterCourseTypeId">
          <el-select clearable
                     placeholder="请选择教程类型"
                     filterable
                     v-model="formData.lyyFunctionCenterCourseTypeId">
            <el-option v-for="(item, index) in typeListAll"
                       :key="index"
                       :label="item.name"
                       :value="item.lyyFunctionCenterCourseTypeId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序：" prop="seq">
          <el-input v-model.number="formData.seq" placeholder="请输入教程排序值，数字越小，排序越前，0为最小值" min="0" class="seq"></el-input>
        </el-form-item>
        <el-form-item label="上架首页" prop="ishomedisplay">
          <el-radio-group v-model="formData.ishomedisplay" @change="initRuleHomeSeq()">
            <el-radio v-for="(item, index) in ishomedisplayArr" :key="index" :label="item.key">{{item.value}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="首页排序" prop="homeSeq" v-if="isMustFillHomeSeq">
          <el-input v-model.number="formData.homeSeq" placeholder="请输入教程首页排序值，数字越小，排序越前，0为最小值" min="0" class="home-seq"></el-input>
        </el-form-item>
        <el-form-item label="内容格式" prop="type">
          <el-radio-group v-model="formData.type" @change="initRuleType()">
            <el-radio v-for="(item, index) in typeArr" :key="index" :label="item.key">{{item.value}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="视频链接：" prop="videoUrl" v-if="isMustFIllType">
          <el-input v-model.trim="formData.videoUrl" maxLength="255" placeholder="请输入视频链接"></el-input>
        </el-form-item>
        <el-form-item label="图文编辑：" prop="content" v-if="isMustFIllType">
          <el-input v-show="false" v-model="formData.content"></el-input>
          <tutorial-editor ref="editor" :content="formData.content" @editorBlur="editorBlur"/>
        </el-form-item>
      </el-form>

      <el-form>
        <el-form-item>
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSave">保 存</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>

  import {
    tutorialContentSave,
    tutorialImageUpload,
    tutorialTypeQuery
  } from "@api/functionIntroduction/tutorial";
  import {tutorialIshomedisplayArr} from "@views/functionIntroduction/tutorial/tutorial";
  import TutorialEditor from "@views/functionIntroduction/tutorial/children/content/tutorialEditor";
  import { headerRamToken } from '@/utils/menu'

  export default {
    name: "tutorialContentSub",
    props: {},
    components: {TutorialEditor},
    data() {
      return {
        headerRamToken,
        visible: false,
        visibleLoading: false,
        maxTitle: 8,
        formData: {
          lyyFunctionCenterCourseId: '',
          title: '',
          coverImgUrl: '',
          lyyFunctionCenterCourseTypeId: '',
          seq: '',
          ishomedisplay: 'Y',
          homeSeq: '',
          videoUrl: '',
          content: '',
          type: [1],
        },
        rules: {},
        imageLoading: false,
        isMustFillHomeSeq: false,
        isMustFIllType: false,
        tutorialImageUpload: tutorialImageUpload(),
        fileMaxSize: 15, // 图片最大体积
        imageUrlPrefix: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/',
        ishomedisplayArr: tutorialIshomedisplayArr,
        typeArr: [
          {key: 1, value: '视频'},
          {key: 2, value: '图文'},
          {key: 3, value: '视频 + 图文'},
        ],
        typeListAll: [],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.init();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      init() {
        this.getTypeListAll();
        this.initFormData();
        if (this.$route.query && Object.keys(this.$route.query) && Object.keys(this.$route.query).length > 0) {
          this.formData = Object.assign(this.formData, this.$route.query);
          this.formData.type = Number(this.formData.type);
          if (this.formData.seq) {
            this.formData.seq = Number(this.formData.seq)
          }
          if (this.formData.homeSeq) {
            this.formData.homeSeq = Number(this.formData.homeSeq)
          }
        }
        this.initRule();
        this.initRuleHomeSeq();
        this.initRuleType();
        this.visible = true;
      },
      initRule() {
        this.rules = {
          title: [
            {required: true, message: '请输入消息内容', trigger: 'change'},
            {max: this.maxTitle, message: `消息内容不能超过${this.maxTitle}字`, trigger: 'change'}
          ],
          coverImgUrl: [
            {required: true, message: '请上传图标', trigger: 'change'}
          ],
          lyyFunctionCenterCourseTypeId: [
            {required: true, message: '请选择教程类型', trigger: 'change'}
          ],
          seq: [
            {required: true, message: '请输入教程排序值，数字越小，排序越前，0为最小值', trigger: 'change'},
            {required: true, type: 'number', message: '教程排序值只能输入数字', trigger: 'change'},
            {required: true, type: 'number', min: 0, message: '教程排序值0为最小值', trigger: 'change'}
          ],
          ishomedisplay: [
            {required: true, message: '请选择是否上架首页', trigger: 'change'}
          ],
          type: [
            {required: true, message: '请选择内容格式', trigger: 'change'},
          ],

        };
      },
      initRuleHomeSeq() {
        this.isMustFillHomeSeq = false;
        const flag = this.formData.ishomedisplay === 'Y';
        if (flag) {
          this.rules['homeSeq'] = [
            {required: flag, message: '请输入教程排序值，数字越小，排序越前，0为最小值', trigger: 'change'},
            {required: flag, type: 'number', message: '教程排序值只能输入数字', trigger: 'change'},
            {required: flag, type: 'number', min: 0, message: '教程排序值0为最小值', trigger: 'change'}
          ];
        } else {
          this.rules['homeSeq'] = [
            {required: flag, message: '请输入教程排序值，数字越小，排序越前，0为最小值', trigger: 'change'},
          ];
        }
        this.$nextTick(() => {
          this.isMustFillHomeSeq = true;
        });
      },
      initRuleType() {
        this.isMustFIllType = false;
        let bothFlag = false;
        let contentFlag = false;
        let videoFlag = false;
        if (this.formData.type) {
          if (this.formData.type === 3) {
            bothFlag = true;
          } else{
            if (this.formData.type === 2) {
              contentFlag = true;
            }
            if (this.formData.type === 1) {
              videoFlag = true;
            }
          }
        }
        this.rules['content'] = [
          {required: bothFlag || contentFlag, message: '请输入图文编辑', trigger: 'change'},
        ];
        this.rules['videoUrl'] = [
          {required: bothFlag || videoFlag, message: '请输入视频链接', trigger: 'change'},
          {required: bothFlag || videoFlag, max: 255, message: '视频链接不能超过255字', trigger: 'change'},
        ];
        this.$nextTick(() => {
          this.isMustFIllType = true;
        });
      },
      initFormData() {
        this.formData = {
          lyyFunctionCenterCourseId: '',
          title: '',
          coverImgUrl: '',
          lyyFunctionCenterCourseTypeId: '',
          seq: '',
          ishomedisplay: 'Y',
          homeSeq: '',
          videoUrl: '',
          content: '',
          type: [1],
        };
      },
      getTypeListAll() {
        const params = {
          pageIndex: 1,
          pageSize: 100,
        };
        tutorialTypeQuery(params).then(res => {
          if (res.result === 0) {
            this.typeListAll = res.data.items;
          }
        })
      },
      handleClose() {
        this.$router.push({
          path: '/tutorial/list',
        })
      },
      handleSave() {
        this.formData.content = this.$refs.editor.editorContent;
        this.isParamsComplate(() => {
          this.visibleLoading = true;
          tutorialContentSave(this.getParams()).then(res => {
              this.visibleLoading = false;
              if (res.result === 0) {
                this.$message.success('保存成功');
                this.handleClose();
              }
            }
          );
        });
      },
      getParams() {
        let params = {
          title: this.formData.title,
          coverImgUrl: this.formData.coverImgUrl,
          lyyFunctionCenterCourseTypeId: this.formData.lyyFunctionCenterCourseTypeId,
          seq: this.formData.seq,
          ishomedisplay: this.formData.ishomedisplay,
          homeSeq: this.formData.homeSeq,
          videoUrl: this.formData.videoUrl,
          content: this.formData.content,
          type: Number(this.formData.type),
        };
        if (this.formData.lyyFunctionCenterCourseId) {
          params['lyyFunctionCenterCourseId'] = this.formData.lyyFunctionCenterCourseId;
        }
        return params;
      },
      // 参数是否完整
      async isParamsComplate(call) {
        await this.$refs['ruleForm'].validate((isSuccess, valid) => {
          if (isSuccess) {
            call();
            return true;
          } else {
            let sign = '';
            if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]['message'];
              this.$message.error(sign);
            }
            return false;
          }
        });
      },
      // 上传到后台前的图片验证
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg';
        const isPNG = file.type === 'image/png';
        const isQualified = file.size / 1024 / 1024 < this.fileMaxSize;
        if (!isJPG && !isPNG) {
          this.$message.error('上传物料图片只能是 JPG或PNG 格式!');
        }
        if (!isQualified) {
          this.$message.error(`上传头像图片大小不能超过 ${this.fileMaxSize}MB!`);
        }
        if ((isJPG || isPNG) && isQualified) {
          this.imageLoading = true;
          return true;
        }
      },
      // 上传图片的回调
      handleAvatarSuccess(res, file) {
        this.imageLoading = false;
        if (res.result === 1) {
          this.formData.coverImgUrl = res.para;
        } else {
          this.$message.error('上传失败');
          this.imageClear();
        }
      },
      // 清除图片
      imageClear() {
        this.formData.coverImgUrl = '';
      },
      editorBlur(html) {
        this.formData.content = html;
      }
    },

    computed: {},

    watch: {}

  };

</script>

<style lang="less" scoped>
  .tutorial-sub{
    .title, .seq, .home-seq{
      width: 30%;
    }

    .avatar-uploader .el-upload {
      border: 1px dashed #000;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100px;
      height: 100px;
      line-height: 100px;
      text-align: center;
    }

    .avatar {
      width: 154px;
      height: 98px;
      display: block;
    }

    .upload-box {
      /deep/ .el-upload-dragger {
        width: 154px;
        height: 98px;
      }
    }
    .img-close{
      position: absolute;
      top: 0;
      left: 120px;
      z-index: 100;
      font-size: 30px;
      transform: translate(50%,-50%);
      cursor: pointer;
    }
  }
</style>
