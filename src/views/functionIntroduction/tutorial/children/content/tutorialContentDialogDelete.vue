<!--
日期：2019/10/29
功能：模板组件
作者：杨楠锋
-->
<template>
  <div>
    <el-dialog
      title="温馨提示"
      :visible.sync="visible"
      v-if="visible"
      width="30%"
      :before-close="handleClose">
      <el-form :inline="false"
               label-position="right"
               :model="formData"
               ref="ruleForm"
               v-loading="visibleLoading"
               label-width="110px">
        <p>确定删除该教程？</p>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="danger" @click="handleSave">删 除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
    import {tutorialContentDelete} from "@api/functionIntroduction/tutorial";

    export default {
        name: "tutorialContentDialogDelete",
      props: {},
      components: {},
      data() {
        return {
          visible: false,
          visibleLoading: false,
          formData: {}
        };
      },

      beforeCreate() {
      },

      created() {
      },

      beforeMount() {
      },

      mounted() {
      },

      beforeUpdate() {
      },

      updated() {
      },

      activated() {
      },

      deactivated() {
      },

      beforeDestroy() {
      },

      destroyed() {
      },

      errorCaptured() {
      },

      methods: {
        initFormData() {
          this.formData = {
            courseIds: '',
          };
        },
        open(item) {
          this.initFormData();
          this.formData.courseIds = item;
          this.visible = true;
        },
        handleClose() {
          this.visible = false;
        },
        handleSave() {
          this.visibleLoading = true;
          tutorialContentDelete(this.getParams()).then(res => {
              this.visibleLoading = false;
              if (res.result === 0) {
                this.$message.success('保存成功');
                this.$emit('confirm');
                this.handleClose();
              }
            }
          );
        },
        getParams() {
          let params = {
            courseIds: this.formData.courseIds
          };
          return params;
        }
      },

      computed: {},

      watch: {}

    };

</script>
<style lang="css" scoped>
</style>
