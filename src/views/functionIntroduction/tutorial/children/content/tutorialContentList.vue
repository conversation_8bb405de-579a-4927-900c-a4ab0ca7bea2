<!--
日期：2019/10/23
功能：模板组件
作者：杨楠锋
-->
<template>
  <div v-loading="pageInfo.loading">
    <div class="operation">
      <el-form :inline="true" label-position="right" label-width="100px">
        <el-form-item label="教程标题：">
          <el-input class="inline-input" v-model.trim="formData.name" placeholder="请输入教程标题" clearable></el-input>
        </el-form-item>
        <el-form-item label="教程类型：">
          <el-select clearable
                     placeholder="请选择教程类型"
                     v-model="formData.lyyFunctionCenterCourseTypeId">
            <el-option v-for="(item, index) in typeListAll"
                       :key="index"
                       :label="item.name"
                       :value="item.lyyFunctionCenterCourseTypeId"></el-option>
          </el-select>
        </el-form-item>
        <!--按钮-->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="queryBtn">查询</el-button>
          <el-button type="success"
                     icon="el-icon-circle-plus-outline"
                     @click="addBtn">新建教程
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <!--列表 start-->
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;"
                >
        <template v-for="(item, index) in colums">
          <el-table-column v-if="item.key === 'operation'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
                <el-button type="text"
                           class="info-btn"
                           @click="editBtn(scope.row)">编辑
                </el-button>
                <el-button type="text"
                           class="info-btn"
                           @click="homedisplayBtn(scope.row)">
                  <span :class="scope.row.ishomedisplay | arrGetValue(ishomedisplayArr, 'key', 'show')">{{scope.row.ishomedisplay | arrGetValue(ishomedisplayArr, 'key', 'btnText')}}</span>
                </el-button>
                <span @click="deleteBtn(scope.row.lyyFunctionCenterCourseId)"
                      class="info-btn danger">
                  删除
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'coverImgUrl'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <img :src="imageUrlPrefix + scope.row.coverImgUrl" width="60" height="60" v-viewer/>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'type'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <span>{{scope.row.type | arrGetValue(typeArr, 'key', 'value')}}</span>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'ishomedisplay'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <span>{{scope.row.ishomedisplay | arrGetValue(ishomedisplayArr, 'key', 'value')}}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :key="index"
            :prop="item.key"
            :label="item.label"
            :width="item.width"
            :sortable="item.sortable"
            align="center"
          />
        </template>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 50]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <!--列表 end-->
      <!--删除弹窗-->
      <tutorial-content-dialog-delete ref="deleteRef" @confirm="getList()"/>
      <!--下架弹窗-->
      <tutorial-content-homedisplay ref="homedisplayRef" @confirm="getList()"/>
    </div>
  </div>
</template>

<script>
  import {tutorialQuery, tutorialTypeQuery} from "@api/functionIntroduction/tutorial";
  import 'viewerjs/dist/viewer.css';
  import Viewer from 'v-viewer';
  import Vue from 'vue';
  import {tutorialIshomedisplayArr} from "@views/functionIntroduction/tutorial/tutorial";
  import TutorialContentDialogDelete
    from "@views/functionIntroduction/tutorial/children/content/tutorialContentDialogDelete";
  import TutorialContentHomedisplay
    from "@views/functionIntroduction/tutorial/children/content/tutorialContentHomedisplay";
  Vue.use(Viewer);
  export default {
    name: "tutorialContentList",
    props: {},
    components: {TutorialContentHomedisplay, TutorialContentDialogDelete},
    data() {
      return {
        imageUrlPrefix: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/',
        lyyFunctionCenterCourseTypeIdArr: [
          {key: '', value: ''},
        ],
        formData: {
          name: '',
          lyyFunctionCenterCourseTypeId: '',
        },
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          loading: false,
          list: [],
        },
        // 列表每一列参数
        colums: [
          {key: 'operation', label: '操作'},
          {key: 'seq', label: '排序'},
          {key: 'title', label: '教程标题'},
          {key: 'coverImgUrl', label: '教程封面'},
          {key: 'courseTypeName', label: '教程类型'},
          {key: 'type', label: '格式'},
          {key: 'ishomedisplay', label: '上架首页'},
          {key: 'homeSeq', label: '首页排序'},
          {key: 'updated', label: '更新时间'},
        ],
        typeArr: [
          {key: 1, value: '视频'},
          {key: 2, value: '图文'},
          {key: 3, value: '视频/图文'},
        ],
        ishomedisplayArr: tutorialIshomedisplayArr,
        typeListAll: [],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.init();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      init() {
        this.queryBtn();
        this.getTypeListAll();
      },
      queryBtn() {
        this.pageInfo.pageIndex = 1;
        this.getList();
      },
      addBtn() {
        this.$router.push({
          path: '/tutorial/edit',
          query: {}
        })
      },
      editBtn(item) {
        this.$router.push({
          path: '/tutorial/edit',
          query: item
        })
      },
      homedisplayBtn(item) {
        this.$refs.homedisplayRef.open(item);
      },
      deleteBtn(item) {
        this.$refs.deleteRef.open(item);
      },
      getTypeListAll() {
        const params = {
          pageIndex: 1,
          pageSize: 100,
        };
        tutorialTypeQuery(params).then(res => {
          if (res.result === 0) {
            this.typeListAll = res.data.items;
          }
        })
      },
      getList() {
        this.pageInfo.loading = true;
        tutorialQuery(this.getParams()).then(res => {
          this.pageInfo.loading = false;
          if (res.result === 0) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        });
      },
      getParams() {
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        if (this.formData.name) {
          params['name'] = this.formData.name;
        }
        if (this.formData.lyyFunctionCenterCourseTypeId) {
          params['lyyFunctionCenterCourseTypeId'] = this.formData.lyyFunctionCenterCourseTypeId;
        }
        return params;
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.getList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.getList();
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="less" scoped>
  .operation {
    padding: 20px;
  }

  .danger {
    margin-left: 10px;
    clolor: #f78989;
    cursor: pointer;
  }

  .uninstall {
    color: #909399;
    margin-left: 10px;
    cursor: pointer;
  }
</style>
