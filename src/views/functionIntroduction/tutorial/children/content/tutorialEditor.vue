<!--
日期：2019/11/1
功能：富文本编辑器
作者：杨楠锋
-->
<template>
  <div>
    <div ref="editor" style="text-align:left"></div>
  </div>
</template>

<script>
  import E from 'wangeditor'
  import {tutorialImageUpload} from "@api/functionIntroduction/tutorial";

  export default {
    name: "tutorialEditor",
    props: {
      content: {
        type: String,
        default: '',
      }
    },
    components: {},
    data() {
      return {
        editorContent: '',
        fileMaxSize: 15,
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.init();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      init() {
        this.editorContent = this.content;
        var editor = new E(this.$refs.editor);
        editor.customConfig.zIndex = 100;
        // 自定义菜单配置
        editor.customConfig.menus = [
          'head',  // 标题
          'bold',  // 粗体
          'fontSize',  // 字号
          'fontName',  // 字体
          'italic',  // 斜体
          'underline',  // 下划线
          'strikeThrough',  // 删除线
          'foreColor',  // 文字颜色
          'backColor',  // 背景颜色
          'link',  // 插入链接
          'list',  // 列表
          'justify',  // 对齐方式
          'quote',  // 引用
          'emoticon',  // 表情
          'image',  // 插入图片
          'table',  // 表格
          // 'video',  // 插入视频
          'code',  // 插入代码
          'undo',  // 撤销
          'redo'  // 重复
        ];
        editor.customConfig.onchange = (html) => {
          this.editorContent = html;
        };
        editor.customConfig.onblur = (html) => {
          // html 即编辑器中的内容
          this.$emit('editorBlur', this.editorContent);
        }
        editor.customConfig.uploadImgTimeout = 1000 * 1000; // 超时时间1000秒
        editor.customConfig.pasteIgnoreImg = true; // 禁止粘贴图片
        editor.customConfig.uploadImgServer = tutorialImageUpload();   // 配置上传图片服务器端地址
        editor.customConfig.showLinkImg = false;       // 隐藏“网络图片”tab
        editor.customConfig.uploadImgMaxSize = this.fileMaxSize * 1024 * 1024; // 限制图片大小
        editor.customConfig.uploadImgMaxLength = 1; // 限制一次最多上传 1 张图片
        editor.customConfig.uploadImgHooks = {
          // 上传前验证
          before: (xhr, editor, files) => {

          },
          success: function (xhr, editor, result) {
            // 图片上传并返回结果，图片插入成功之后触发
            // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
          },
          fail: function (xhr, editor, result) {
            // 图片上传并返回结果，但图片插入错误时触发
            // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象，result 是服务器端返回的结果
          },
          error: (xhr, editor) => {
            // 图片上传出错时触发
            // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象
            if (xhr.status === 401) {
              let message = JSON.parse(xhr.response).message;
              this.$nextTick(() => {
                this.$message.error(message)
              });
            }
          },
          timeout: function (xhr, editor) {
            // 图片上传超时时触发
            // xhr 是 XMLHttpRequst 对象，editor 是编辑器对象
          },
          // 上传后回调
          customInsert: (insertImg, result, editor) => {
            if (result.result === 1) {
              insertImg(this.config.aliyunImgBaseUrl + result.para)
            } else {
              this.$message.error(result.description);
            }
          }
        };
        // 自定义提示方法
        editor.customConfig.customAlert = (info) => {
          // info 是需要提示的内容
          this.$message.error(info);
        };
        editor.create();
        editor.txt.html(this.editorContent); // 初始化编辑器内容
      },
      getContent: () => {
        console.log(this.editorContent)
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="css" scoped>
</style>
