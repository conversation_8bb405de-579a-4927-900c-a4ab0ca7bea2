<!--
日期：2019/10/29
功能：教程内容管理
作者：杨楠锋
-->
<template>
  <div>
    <el-dialog
      :title="(formData.lyyFunctionCenterCourseTypeId ? ' 编辑':' 新建') + '教程类型'"
      :visible.sync="visible"
      v-if="visible"
      width="30%"
      :before-close="handleClose">
      <el-form :inline="false"
               label-position="right"
               :model="formData"
               ref="ruleForm"
               :rules="rules"
               v-loading="visibleLoading"
               label-width="135px">
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="formData.name" :maxLength="maxName"
                    placeholder="请输入类型名称">
            <template slot="append">
              <span v-if="formData.name && typeof formData.name === 'string' && formData.name.length > 0">{{formData.name.length}}</span>
              <span v-else>0</span>
              <span>/{{maxName}}</span>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="排序：" prop="seq">
          <el-input v-model.number="formData.seq" placeholder="请输入排序值，数字越小，排序越前，0为最小值" min="0"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
    import {tutorialTypeSave} from "@api/functionIntroduction/tutorial";

    export default {
        name: "tutorialTypeDialogForm",
      props: {},
      components: {},
      data() {
        return {
          visible: false,
          visibleLoading: false,
          maxName: 8,
          formData: {},
        };
      },

      beforeCreate() {
      },

      created() {
      },

      beforeMount() {
      },

      mounted() {
      },

      beforeUpdate() {
      },

      updated() {
      },

      activated() {
      },

      deactivated() {
      },

      beforeDestroy() {
      },

      destroyed() {
      },

      errorCaptured() {
      },

      methods: {

        initRule() {
          this.rules = {
            name: [
              {required: true, message: '请输入类型名称', trigger: 'change'},
              {max: this.maxName, message: `类型名称不能超过${this.maxName}字`, trigger: 'change'}
            ],
            seq: [
              {required: true, message: '请输入排序值，数字越小，排序越前，0为最小值', trigger: 'change'},
              {required: true, type: 'number', message: '排序值只能输入数字', trigger: 'change'},
              {required: true, type: 'number', min: 0, message: `排序值0为最小值`, trigger: 'change'}
            ],
          };
        },
        initFormData() {
          this.formData = {
            lyyFunctionCenterCourseTypeId: '',
            name: '',
            seq: null,
          };
        },
        open(item) {
          this.initRule();
          this.initFormData();
          this.formData = Object.assign(this.formData, item);
          this.visible = true;
        },
        handleClose() {
          this.visible = false;
        },
        handleSave() {
          this.isParamsComplate(() => {
            this.visibleLoading = true;
            tutorialTypeSave(this.getParams()).then(res => {
                this.visibleLoading = false;
                if (res.result === 0) {
                  this.$message.success('保存成功');
                  this.$emit('confirm');
                  this.handleClose();
                }
              }
            );
          });
        },
        getParams() {
          let params = {
            name: this.formData.name,
            seq: this.formData.seq,
          };
          if (this.formData.lyyFunctionCenterCourseTypeId) {
            params['lyyFunctionCenterCourseTypeId'] = this.formData.lyyFunctionCenterCourseTypeId;
          }
          return params;
        },
        // 参数是否完整
        async isParamsComplate(call) {
          await this.$refs['ruleForm'].validate((isSuccess, valid) => {
            if (isSuccess) {
              call();
              return true;
            } else {
              let sign = '';
              if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
                const key = Object.keys(valid);
                sign = valid[key[0]][0]['message'];
                this.$message.error(sign);
              }
              return false;
            }
          });
        },
      },

      computed: {},

      watch: {}

    };

</script>

<style lang="less" scoped>
</style>
