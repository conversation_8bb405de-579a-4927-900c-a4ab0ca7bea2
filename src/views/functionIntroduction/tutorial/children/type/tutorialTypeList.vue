<!--
日期：2019/10/23
功能：教程内容管理
作者：杨楠锋
-->
<template>
  <div>
    <div class="operation">
      <el-button type="success"
                 icon="el-icon-circle-plus-outline"
                 @click="addBtn">新建类型
      </el-button>
    </div>
    <div>
      <!--列表 start-->
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;"
                v-loading="pageInfo.loading">
        <template v-for="(item, index) in colums">
          <el-table-column v-if="item.key === 'operation'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
                <el-button type="text"
                           class="info-btn"
                           @click="editBtn(scope.row)">编辑
                </el-button>
                <span @click="deleteBtn(scope.row.lyyFunctionCenterCourseTypeId)"
                      class="info-btn danger">
                删除
              </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :key="index"
            :prop="item.key"
            :label="item.label"
            :width="item.width"
            :sortable="item.sortable"
            align="center"
          />
        </template>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <!--列表 end-->
      <!--删除弹窗-->
      <tutorial-type-dialog-delete ref="deleteRef" @confirm="getList()"/>
      <!--新建或编辑弹窗-->
      <tutorial-type-dialog-form ref="formRef" @confirm="getList()"/>
    </div>
  </div>
</template>

<script>
  import {tutorialTypeQuery} from "@api/functionIntroduction/tutorial";
  import TutorialTypeDialogDelete from "@views/functionIntroduction/tutorial/children/type/tutorialTypeDialogDelete";
  import TutorialTypeDialogForm from "@views/functionIntroduction/tutorial/children/type/tutorialTypeDialogForm";
  export default {
    name: "tutorialTypeList",
    props: {},
    components: {TutorialTypeDialogForm, TutorialTypeDialogDelete},
    data() {
      return {
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          loading: false,
          list: [],
        },
        // 列表每一列参数
        colums: [
          {key: 'operation', label: '操作'},
          {key: 'seq', label: '排序', sortable: true},
          {key: 'name', label: '类型名称'},
          {key: 'created', label: '创建时间', sortable: true},
        ],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.getList();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      addBtn() {
        this.$refs.formRef.open({});
      },
      editBtn(item) {
        this.$refs.formRef.open(item);
      },
      getList() {
        this.pageInfo.loading = true;
        tutorialTypeQuery(this.getParams()).then(res => {
          this.pageInfo.loading = false;
          if (res.result === 0) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        });
      },
      getParams() {
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        return params;
      },
      deleteBtn(item) {
        this.$refs.deleteRef.open(item);
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.getList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.getList();
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="less" scoped>
  .operation {
    padding: 20px;
  }
  .danger {
    margin-left: 10px;
    clolor: #f78989;
    cursor: pointer;
  }
</style>
