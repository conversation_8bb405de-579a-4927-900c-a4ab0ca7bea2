<!--
日期：2019/10/22
功能：教程内容管理
作者：杨楠锋
-->
<template>
  <div>
    <el-card>
      <tutorial-tabs>
        <div slot="content">
            <tutorial-content-list/>
        </div>
        <div slot="type">
          <tutorial-type-list/>
        </div>
      </tutorial-tabs>
    </el-card>
  </div>
</template>

<script>
  import TutorialTabs from "@views/functionIntroduction/tutorial/children/tutorialTabs";
  import TutorialTypeList from "@views/functionIntroduction/tutorial/children/type/tutorialTypeList";
  import TutorialContentList from "@views/functionIntroduction/tutorial/children/content/tutorialContentList";

  export default {
    name: "tutorial",
    props: {},
    components: {TutorialContentList, TutorialTypeList, TutorialTabs},
    data() {
      return {
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="css" scoped>
</style>
