<!--
日期：2019/10/28
功能：系统消息推送
作者：杨楠锋
-->
<template>
  <div>
    <el-dialog
      :title="(formData.lyySystemMessageId ? ' 编辑':' 新建') + '系统消息推送'"
      :visible.sync="visible"
      v-if="visible"
      width="30%"
      :before-close="handleClose">
      <el-form :inline="false"
               label-position="right"
               :model="formData"
               ref="ruleForm"
               :rules="rules"
               v-loading="visibleLoading"
               label-width="135px">
        <el-form-item label="消息内容" prop="message">
          <el-input v-model="formData.message" type="textarea" rows="3" :maxLength="maxMessage"
                    placeholder="请输入消息内容"></el-input>
          <p class="remark-count">
            <span v-if="formData.message && typeof formData.message === 'string' && formData.message.length > 0">{{formData.message.length}}</span>
            <span v-else>0</span>
            <span>/{{maxMessage}}</span>
          </p>
        </el-form-item>
        <el-form-item label="跳转链接类型" prop="redirectType">
          <el-radio-group v-model="formData.redirectType">
            <el-radio v-for="(item, index) in redirectTypeArr" :key="index" :label="item.key">{{item.value}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="跳转链接" prop="redirectLink">
          <el-input v-model="formData.redirectLink" placeholder="请输入跳转链接" maxLength="255"></el-input>
        </el-form-item>
        <el-form-item label="设备类型" prop="typeList">
          <el-checkbox-group v-model="formData.typeList">
            <el-checkbox v-for="(item, index) in typeListArr" :key="index" :label="item"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="推送时间：" prop="lampStart">
          <el-date-picker
            v-model="formData.lampStart"
            placeholder="请选择推送时间"
            type="date"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="timestamp"
            :disabled="isCanEdit"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="走马灯-结束时间：" prop="lampEnd">
          <el-date-picker
            v-model="formData.lampEnd"
            placeholder="请选择走马灯-结束时间"
            :picker-options="pickerOptions"
            type="date"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="timestamp"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div class="remark">
        <p>注意：</p>
        <p>① <span class="color-red">若不设置 “走马灯结束时间”，则代表不开启走马灯。</span>即此消息不会显示在B端首页顶部的走马灯位置，仅显示在“消息通知”列表页。</p>
        <p>② 若设置了 “走马灯结束时间”，则代表开启走马灯。走马灯开启时间和推送时间一致。</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {systemMessageSave} from "@api/functionIntroduction/systemMessage";
  import {dateFormat} from "@js/utils";

  export default {
    name: "systemMessageDialogForm",
    props: {},
    components: {},
    data() {
      return {
        isCanEdit: false,
        visible: false,
        visibleLoading: false,
        maxMessage: 200,
        formData: {},
        redirectTypeArr: [
          {key: 1, value: '产品动态页'},
          {key: 2, value: '消息通知列表页'},
          {key: 3, value: '自定义链接'},
        ],
        typeListArr: [
          '娃娃机',
          '兑币机',
          '儿童类',
          '扭蛋机',
          '售货机',
          '洗衣机',
          '按摩椅',
          '充电桩',
          '其他'
        ],
        // 日期插件的时间选择范围
        pickerOptions: {
          // 禁止选择时间范围限制，return true 无法选择
          disabledDate(time) {
          },
          // 点击选择时间的回调，只选择一个返回默认是最小时间，｛null，minDate｝
          onPick({maxDate, minDate}) {
          },
        },
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      initDataPicker() {
        // 初始化date和fordate的值
        //初始化datepicker最大时间和跨度和选中回调和快捷选择时间
        this.pickerOptions = {
          disabledDate(time) {
            const date = new Date();
            // 不能选择今天之前的日期
            const lessToday = (pickTime) => {
              return pickTime.getTime() < date.setHours(0, 0, 0, 0);
            };
            return lessToday(time);
          },
          onPick(time) {
            const date = new Date();
            // 不能选择今天之前的日期
            const lessToday = (pickTime) => {
              return pickTime.getTime() < date.setHours(0, 0, 0, 0);
            };
            return lessToday(time);
          }
        }
      },
      initRule() {
        this.rules = {
          message: [
            {required: true, message: '请输入消息内容', trigger: 'change'},
            {max: this.maxMessage, message: `消息内容不能超过${this.maxMessage}字`, trigger: 'change'}
          ],
          redirectType: [
            {required: true, message: '请至少选择一个跳转链接类型', trigger: 'change'},
          ],
          redirectLink: [
            {required: false, max: 255, message: '跳转链接不能超过255字', trigger: 'change'},
          ],
          typeList: [
            {type: 'array', required: true, message: '请至少选择一个设备类型', trigger: 'change'},
          ],
          lampStart: [
            {required: true, message: '请选择推送时间', trigger: 'change'},
          ],
        };
      },
      initFormData() {
        this.formData = {
          lyySystemMessageId: '',
          message: '',
          redirectType: 1,
          redirectLink: '',
          typeList: [],
          lampStart: dateFormat(new Date(), 'yyyy-MM-dd'),
          lampEnd: '',
        };
      },
      open(item) {
        this.isCanEdit = false;
        this.initRule();
        this.initDataPicker();
        this.initFormData();
        this.formData = Object.assign(this.formData, item);
        if (this.formData.lyySystemMessageId) {
          this.isCanEdit = true;
        }
        this.formData.lampStart = (new Date(this.formData.lampStart)).getTime();
        if (this.formData.lampEnd) {
          this.formData.lampEnd = (new Date(this.formData.lampEnd)).getTime();
        }
        this.visible = true;
      },
      handleClose() {
        this.visible = false;
      },
      handleSave() {
        this.isParamsComplate(() => {
          this.visibleLoading = true;
          systemMessageSave(this.getParams()).then(res => {
              this.visibleLoading = false;
              if (res.result === 0) {
                this.$message.success('保存成功');
                this.$emit('confirm');
                this.handleClose();
              }
            }
          );
        });
      },
      getParams() {
        let params = {
          message: this.formData.message,
          redirectType: this.formData.redirectType,
          redirectLink: this.formData.redirectLink,
          lampStart: this.formData.lampStart,
          lampEnd: this.formData.lampEnd,
          typeList: this.formData.typeList,
        };
        if (this.formData.lyySystemMessageId) {
          params['lyySystemMessageId'] = this.formData.lyySystemMessageId;
        }
        return params;
      },
      // 参数是否完整
      async isParamsComplate(call) {
        await this.$refs['ruleForm'].validate((isSuccess, valid) => {
          if (isSuccess) {
            call();
            return true;
          } else {
            let sign = '';
            if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]['message'];
              this.$message.error(sign);
            }
            return false;
          }
        });
      },
    },

    computed: {},

    watch: {}

  };

</script>

<style lang="less" scoped>
  .color-red {
    color: red;
  }
  .remark{
    margin-top: 40px;
  }
</style>
