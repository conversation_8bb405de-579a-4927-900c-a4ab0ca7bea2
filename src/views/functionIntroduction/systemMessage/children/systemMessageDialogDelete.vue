<!--
日期：2019/10/28
功能：系统消息推送
作者：杨楠锋
-->
<template>
  <div>
    <el-dialog
      title="温馨提示"
      :visible.sync="visible"
      v-if="visible"
      width="30%"
      :before-close="handleClose">
      <el-form :inline="false"
               label-position="right"
               :model="formData"
               ref="ruleForm"
               v-loading="visibleLoading"
               label-width="110px">
        <p>删除后，将无法恢复。且该消息在B端“消息通知”列表页也会被删除。确定要删除吗？</p>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="danger" @click="handleSave">删 除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {systemMessageDelete} from "@api/functionIntroduction/systemMessage";

  export default {
    name: "systemMessageDialogDelete",
    props: {},
    components: {},
    data() {
      return {
        visible: false,
        visibleLoading: false,
        formData: {}
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      initFormData() {
        this.formData = {
          lyySystemMessageId: '',
          title: '',
          url: '',
          iconUrl: '',
          seq: '',
          ishomedisplay: 'Y',
          homeSeq: ''
        };
      },
      open(item) {
        this.initFormData();
        this.formData.lyySystemMessageId = item;
        this.visible = true;
      },
      handleClose() {
        this.visible = false;
      },
      handleSave() {
        this.visibleLoading = true;
        systemMessageDelete(this.getParams()).then(res => {
            this.visibleLoading = false;
            if (res.result === 0) {
              this.$message.success('保存成功');
              this.$emit('confirm');
              this.handleClose();
            }
          }
        );
      },
      getParams() {
        let params = {
          messageId: this.formData.lyySystemMessageId
        };
        return params;
      }
    },

    computed: {},

    watch: {}

  };

</script>
<style lang="css" scoped>
</style>
