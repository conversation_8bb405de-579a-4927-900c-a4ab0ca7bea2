<!--
日期：2019/10/22
功能：系统消息推送
作者：杨楠锋
-->
<template>
  <div>
    <div class="operation">
      <el-button type="success"
                 icon="el-icon-circle-plus-outline"
                 @click="addBtn">新建消息
      </el-button>
      <el-button type="text"
                 @click="previewBtn">查看预览图
      </el-button>
      <img ref="imgRef" src="@/assets/images/systemMessage.png" v-show="false"/>
    </div>
    <div>
      <!--列表 start-->
      <el-table :data="pageInfo.list" border highlight-current-row style="width: 100%;margin-bottom: 20px;"
                v-loading="pageInfo.loading">
        <template v-for="(item, index) in colums">
          <el-table-column v-if="item.key === 'operation'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div>
                <el-button type="text"
                           class="info-btn"
                           @click="editBtn(scope.row)">编辑
                </el-button>
                <el-button type="text"
                           class="info-btn"
                           @click="deleteBtn(scope.row.lyySystemMessageId)">
                  <span class="danger">删除</span>
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.key === 'typeList'"
                           :key="index"
                           :prop="item.key"
                           :label="item.label"
                           :width="item.width"
                           :sortable="item.sortable"
                           align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.typeList && Array.isArray(scope.row.typeList) && scope.row.typeList.length > 0">
                <span v-for="(item, index) in scope.row.typeList" :key="index">
                  <span v-if="index !== 0"> , </span>
                  <span>{{item}}</span>
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            :key="index"
            :prop="item.key"
            :label="item.label"
            :width="item.width"
            :sortable="item.sortable"
            align="center"
          />
        </template>
      </el-table>
      <el-pagination
        :page-sizes="[10, 20, 50]"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :current-page="pageInfo.pageIndex"
        background
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <!--列表 end-->
      <system-message-dialog-delete ref="deleteRef" @confirm="getList()"/>
      <system-message-dialog-form ref="formRef" @confirm="getList()"/>
    </div>
  </div>
</template>

<script>
  import 'viewerjs/dist/viewer.css';
  import Viewer from 'viewerjs';
  import {systemMessageQuery} from "@api/functionIntroduction/systemMessage";
  import SystemMessageDialogDelete from "@views/functionIntroduction/systemMessage/children/systemMessageDialogDelete";
  import SystemMessageDialogForm from "@views/functionIntroduction/systemMessage/children/systemMessageDialogForm";

  export default {
    name: "systemMessage",
    props: {},
    components: {SystemMessageDialogForm, SystemMessageDialogDelete},
    data() {
      return {
        pageInfo: {
          total: 0,
          pageSize: 10,
          pageIndex: 1,
          loading: false,
          list: [],
        },
        // 列表每一列参数
        colums: [
          {key: 'operation', label: '操作'},
          {key: 'message', label: '消息内容'},
          {key: 'redirectLink', label: '跳转链接'},
          {key: 'typeList', label: '设备类型'},
          {key: 'lampStart', label: '推送时间', sortable: true},
          {key: 'lampEnd', label: '走马灯-结束时间', sortable: true},
        ],
        viewer: null,
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.getList();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      addBtn() {
        this.$refs.formRef.open({});
      },
      editBtn(item) {
        this.$refs.formRef.open(item);
      },
      deleteBtn(id) {
        this.$refs.deleteRef.open(id);
      },
      previewBtn() {
        if (!this.viewer) {
          this.viewer = new Viewer(this.$refs.imgRef, {
            // 配置
            movable: false,
          })
        }
        this.viewer.show();
      },
      getList() {
        this.pageInfo.loading = true;
        systemMessageQuery(this.getParams()).then(res => {
          this.pageInfo.loading = false;
          if (res.result === 0) {
            this.pageInfo.list = res.data.items;
            this.pageInfo.total = res.data.total;
          }
        });
      },
      getParams() {
        let params = {
          pageIndex: this.pageInfo.pageIndex,
          pageSize: this.pageInfo.pageSize,
        };
        return params;
      },
      // 改变列表每页条数
      handleSizeChange(val) {
        this.pageInfo.pageSize = val;
        this.pageInfo.total = 0;
        this.getList();
      },
      // 改变页码
      handleCurrentChange(val) {
        this.pageInfo.pageIndex = val;
        this.getList();
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="less" scoped>
  .operation {
    padding: 20px;
  }
</style>
