<!--
日期：2019/10/28
功能：产品推文
作者：杨楠锋
-->
<template>
  <div>
    <router-view></router-view>
  </div>
</template>

<script>
    export default {
        name: "index",
        props: {},
        components: {},
        data() {
            return {};
        },

        beforeCreate() {
        },

        created() {
        },

        beforeMount() {
        },

        mounted() {
        },

        beforeUpdate() {
        },

        updated() {
        },

        activated() {
        },

        deactivated() {
        },

        beforeDestroy() {
        },

        destroyed() {
        },

        errorCaptured() {
        },

        methods: {},

        computed: {},

        watch: {}

    }

</script>
<style lang="css" scoped>
</style>
