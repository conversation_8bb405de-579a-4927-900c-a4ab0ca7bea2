<!--
日期：2019/10/28
功能：产品推文
作者：杨楠锋
-->
<template>
  <div>
    <el-dialog
      title="温馨提示"
      :visible.sync="visible"
      v-if="visible"
      width="30%"
      :before-close="handleClose">
      <el-form :inline="false"
               label-position="right"
               :model="formData"
               ref="ruleForm"
               v-loading="visibleLoading"
               label-width="110px">
        <p>删除后，将无法恢复。且该推文在B端 “产品功能更新”列表页也会被删除。确定要删除吗？</p>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="danger" @click="handleSave">删 除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {productTweetsDelete} from "@api/functionIntroduction/productTweets";

  export default {
    name: "productTweetsDialogDelete",
    props: {},
    components: {},
    data() {
      return {
        visible: false,
        visibleLoading: false,
        formData: {}
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      initFormData() {
        this.formData = {
          lyySystemArticalId: '',
        };
      },
      open(item) {
        this.initFormData();
        this.formData.lyySystemArticalId = item;
        this.visible = true;
      },
      handleClose() {
        this.visible = false;
      },
      handleSave() {
        this.visibleLoading = true;
        productTweetsDelete(this.getParams()).then(res => {
            this.visibleLoading = false;
            if (res.result === 0) {
              this.$message.success('删除成功');
              this.$emit('confirm');
              this.handleClose();
            }
          }
        );
      },
      getParams() {
        let params = {
          articalId: this.formData.lyySystemArticalId
        };
        return params;
      }
    },

    computed: {},

    watch: {}

  };

</script>
<style lang="css" scoped>
</style>
