<!--
日期：2019/10/28
功能：产品推文
作者：杨楠锋
-->
<template>
  <div>
    <el-card>
      <div slot="header" class="clearfix">
        {{formData.lyySystemArticalId ? ' 编辑':' 新建'}}产品推文
      </div>
      <el-form :inline="false"
               v-if="visible"
               label-position="right"
               :model="formData"
               ref="ruleForm"
               :rules="rules"
               v-loading="visibleLoading"
               label-width="125px">
        <el-form-item label="推送时间：" prop="pushStart">
          <el-date-picker
            v-model="formData.pushStart"
            placeholder="请选择推送时间"
            type="date"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="timestamp"
            :picker-options="pickerOptions"
            :disabled="isCanEdit"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="设备类型" prop="typeList">
          <el-checkbox-group v-model="formData.typeList">
            <el-checkbox v-for="(item, index) in typeListArr" :key="index" :label="item"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="标题" prop="headline">
          <el-input v-model.trim="formData.headline" :maxLength="maxHeadline" placeholder="请输入标题">
            <template slot="append">
              <span v-if="formData.headline && typeof formData.headline === 'string' && formData.headline.length > 0">{{formData.headline.length}}</span>
              <span v-else>0</span>
              <span>/{{maxHeadline}}</span>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="链接类型" prop="redirectType">
          <el-radio-group v-model="formData.redirectType" @change="initRedirectType()">
            <el-radio v-for="(item, index) in redirectTypeArr" :key="index" :label="item.key">{{item.value}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="外部链接：" prop="redirectLink" v-if="isShowRedirectType">
          <el-input v-model.trim="formData.redirectLink" maxLength="255" placeholder="请输入外部链接"></el-input>
        </el-form-item>
        <el-form-item label="本地编辑内容：" prop="content" v-if="isShowRedirectType">
          <el-input v-show="false" v-model="formData.content"></el-input>
          <product-tweets-editor ref="editor" :content="formData.content" @editorBlur="editorBlur"/>
        </el-form-item>
      </el-form>
      <el-form>
        <el-form-item>
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSave">保 存</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
  import {productTweetsSave} from "@api/functionIntroduction/productTweets";

  import ProductTweetsEditor from "@views/functionIntroduction/productTweets/children/productTweetsEditor";

  export default {
    name: "productTweetsSub",
    props: {},
    components: {ProductTweetsEditor},
    data() {
      return {
        isCanEdit: false,
        visible: false,
        visibleLoading: false,
        maxHeadline: 50,
        formData: {
          lyySystemArticalId: '',
          pushStart: '',
          headline: '',
          redirectType: '2',
          redirectLink: '',
          content: '',
          typeList: [],
        },
        redirectTypeArr: [
          {key: '1', value: '本地编辑内容'},
          {key: '2', value: '外部链接'},
        ],
        // 日期插件的时间选择范围
        pickerOptions: {
          // 禁止选择时间范围限制，return true 无法选择
          disabledDate(time) {
          },
          // 点击选择时间的回调，只选择一个返回默认是最小时间，｛null，minDate｝
          onPick({maxDate, minDate}) {
          },
        },
        isShowRedirectType: true,
        rules: {},
        typeListArr: [
          '娃娃机',
          '兑币机',
          '儿童类',
          '扭蛋机',
          '售货机',
          '洗衣机',
          '按摩椅',
          '充电桩',
          '其他'
        ],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.init();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      init() {
        this.initFormData();
        if (this.$route.query && Object.keys(this.$route.query) && Object.keys(this.$route.query).length > 0) {
          this.formData = Object.assign(this.formData, this.$route.query);
          this.formData.redirectType += '';
          this.formData.pushStart = (new Date(this.formData.pushStart)).getTime();
          this.isCanEdit = this.formData.lyySystemArticalId ? true : false;
        }
        this.initRule();
        this.initDataPicker();
        this.initRedirectType();
        this.visible = true;
      },
      initDataPicker() {
        // 初始化date和fordate的值
        //初始化datepicker最大时间和跨度和选中回调和快捷选择时间
        this.pickerOptions = {
          disabledDate(time) {
            const date = new Date();
            // 不能选择今天之前的日期
            const lessToday = (pickTime) => {
              return pickTime.getTime() < date.setHours(0, 0, 0, 0);
            };
            return lessToday(time);
          },
          onPick(time) {
            const date = new Date();
            // 不能选择今天之前的日期
            const lessToday = (pickTime) => {
              return pickTime.getTime() < date.setHours(0, 0, 0, 0);
            };
            return lessToday(time);
          }
        }
      },
      initRule() {
        this.rules = {
          pushStart: [
            {required: true, message: '请选择推送时间', trigger: 'change'},
          ],
          typeList: [
            {type: 'array', required: true, message: '请至少选择一个设备类型', trigger: 'change'},
          ],
          headline: [
            {required: true, message: '请输入消息内容', trigger: 'change'},
            {max: this.maxHeadline, message: `消息内容不能超过${this.maxHeadline}字`, trigger: 'change'}
          ],
          redirectType: [
            {required: true, message: '请至少选择一个跳转类型', trigger: 'change'},
          ],
          redirectLink: [
            {required: false, max: 255, message: '跳转链接不能超过255字', trigger: 'change'},
          ],
        };
      },
      initRedirectType() {
        this.isShowRedirectType = false;
        const flag = this.formData.redirectType === '1';
        this.rules['content'] = [
          {required: flag, message: '请输入本地编辑内容', trigger: 'change'},
        ];
        this.rules['redirectLink'] = [
          {required: !flag, message: '请输入外部链接', trigger: 'change'},
          {max: 255, message: '跳转链接不能超过255字', trigger: 'change'},
        ];
        this.$nextTick(() => {
          this.isShowRedirectType = true;
        });
      },
      initFormData() {
        this.formData = {
          lyySystemArticalId: '',
          pushStart: '',
          headline: '',
          redirectType: '2',
          redirectLink: '',
          content: '',
          typeList: [],
        };
      },
      handleClose() {
        this.$router.push({
          path: '/productTweets/list',
        })
      },
      handleSave() {
        this.formData.content = this.$refs.editor.editorContent;
        this.isParamsComplate(() => {
          this.visibleLoading = true;
          productTweetsSave(this.getParams()).then(res => {
              this.visibleLoading = false;
              if (res.result === 0) {
                this.$message.success('保存成功');
                this.$router.push({
                  path: '/productTweets/list',
                })
              }
            }
          );
        });
      },
      getParams() {
        let params = {
          pushStart: this.formData.pushStart,
          headline: this.formData.headline,
          content: this.formData.content,
          redirectLink: this.formData.redirectLink,
          redirectType: this.formData.redirectType,
          typeList: this.formData.typeList,
        };
        if (this.formData.lyySystemArticalId) {
          params['lyySystemArticalId'] = this.formData.lyySystemArticalId;
        }
        if(this.$route.query.whiteListType){
          params['whiteListType'] = this.$route.query.whiteListType
        }
        return params;
      },
      // 参数是否完整
      async isParamsComplate(call) {
        await this.$refs['ruleForm'].validate((isSuccess, valid) => {
          if (isSuccess) {
            call();
            return true;
          } else {
            let sign = '';
            if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]['message'];
              this.$message.error(sign);
            }
            return false;
          }
        });
      },
      editorBlur(html) {
        this.formData.content = html;
      }
    },

    computed: {},

    watch: {}

  };

</script>

<style lang="less" scoped>
</style>
