<!--
日期：2019/10/22
功能：后台品类消息推送，删除弹窗
作者：杨楠锋
-->
<template>
  <div>
    <el-dialog
      title="删除记录?"
      :visible.sync="visible"
      v-if="visible"
      width="30%"
      v-loading="visibleLoading"
      :before-close="handleClose">
      <p>您将删除掉相应的记录，该记录将不能恢复！</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="danger" @click="handleSave">删 除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {merchantMessageDelete} from "@api/functionIntroduction/merchantMessage";

  export default {
    name: "merchantMessageDialogDelete",
    props: {},
    components: {},
    data() {
      return {
        visible: false,
        visibleLoading: false,
        lyyMessagePushId: '',
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      // 打开弹窗
      open(lyyMessagePushId) {
        this.visible = true;
        this.lyyMessagePushId = lyyMessagePushId;
      },
      // 关闭弹窗
      handleClose() {
        this.visible = false;
      },
      // 保存弹窗
      handleSave() {
        this.visibleLoading = true;
        merchantMessageDelete({lyyMessagePushId: this.lyyMessagePushId}).then(res => {
          this.visibleLoading = false;
          if (res.result === 0) {
            this.$message.success('删除成功');
            // 跟父组件通信
            this.$emit('confirm');
            this.handleClose();
          }
        });
      },
    },

    computed: {},

    watch: {}

  }

</script>
<style lang="css" scoped>
</style>
