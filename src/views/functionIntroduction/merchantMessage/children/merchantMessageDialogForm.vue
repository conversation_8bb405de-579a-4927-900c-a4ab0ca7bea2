<!--
日期：2019/10/22
功能：后台品类消息推送，新建或编辑弹窗
作者：杨楠锋
-->
<template>
  <div>
    <el-dialog
      :title="(formData.lyyMessagePushId ? ' 编辑' : ' 新建') + '后台品类消息推送'"
      :visible.sync="visible"
      v-if="visible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form
        :inline="false"
        label-position="right"
        :model="formData"
        ref="ruleForm"
        :rules="rules"
        v-loading="visibleLoading"
        label-width="110px"
      >
        <el-form-item label="序号 :" prop="sort">
          <el-input v-model="formData.sort" placeholder="请输入" @input="formatValue()"></el-input>
        </el-form-item>
        <el-form-item label="设备类型" prop="equipmentTypesArr">
          <el-checkbox-group v-model="formData.equipmentTypesArr">
            <el-checkbox
              v-for="(item, index) in equipmentTypesArr"
              :key="index"
              :label="item.label"
            ></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="banner：" prop="banner">
          <!--这个input不显示，只是为了绑定formData.imageUrlCompress，这样这个值为空的时候会显示提示信息-->
          <el-input v-show="false" v-model="formData.banner"></el-input>
          <i v-if="formData.banner" class="el-icon-error img-close" @click="imageClear()"></i>
          <el-upload
            class="avatar-uploader upload-box"
            :action="merchantMessageImage"
            :headers="headerRamToken"
            :show-file-list="false"
            drag
            ref="uploadRef"
            accept="image/jpeg, image/png, image/gif"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <div v-loading="imageLoading">
              <div v-if="formData.banner">
                <img :src="formData.banner" ref="imgRef" class="avatar" />
              </div>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </div>
          </el-upload>
          <p>建议尺寸750*140，{{ fileMaxSize }}M内，支持JPG、PNG、GIF</p>
        </el-form-item>
        <el-form-item label="位置：" prop="type">
          <el-select v-model="formData.type" placeholder="Select">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="跳转类型：" prop="linkType">
          <el-radio v-model="formData.linkType" :label="1">小程序</el-radio>
          <el-radio v-model="formData.linkType" :label="2">H5</el-radio>
        </el-form-item>
        <template v-if="formData.linkType == '1'">
          <el-form-item label="appid：" prop="miniAppId">
            <el-input v-model.trim="formData.miniAppId" maxLength="255"> </el-input>
          </el-form-item>
          <el-form-item label="appSecret" prop="miniAppSecret">
            <el-input v-model.trim="formData.miniAppSecret" maxLength="255"> </el-input>
          </el-form-item>
          <el-form-item label="跳转链接：" prop="miniPage">
            <el-input v-model.trim="formData.miniPage" maxLength="255"> </el-input>
          </el-form-item>
        </template>
        <el-form-item label="链接：" prop="link" v-else>
          <el-input v-model.trim="formData.link" maxLength="255">
            <template slot="append">选填</template>
          </el-input>
        </el-form-item>
        <el-form-item label="起止日期：" prop="date">
          <el-date-picker
            v-model="formData.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="changeDate"
            :picker-options="pickerOptions"
            :editable="false"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="指定商家：" prop="showType">
          <el-radio-group v-model="formData.showType" @change="changeShowType()">
            <el-radio label="all">全部</el-radio>
            <el-radio label="code">自定义</el-radio>
          </el-radio-group>
          <el-form-item prop="whiteListType" class="white-list-code">
            <el-input
              v-if="formData.showType === 'code'"
              v-model="formData.whiteListType"
              placeholder="请输入白名单编号"
            ></el-input>
          </el-form-item>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  merchantMessageImage,
  merchantMessageSave,
} from '@api/functionIntroduction/merchantMessage';
import { dateFormat } from '@js/utils';
import { headerRamToken } from '@/utils/menu';

export default {
  name: 'merchantMessageDialogForm',
  props: {},
  components: {},
  data() {
    return {
      headerRamToken,
      visible: false,
      visibleLoading: false,
      imageLoading: false,
      formData: {},
      fileMaxSize: 15, // 图片最大体积
      // 日期插件的时间选择范围
      pickerOptions: {
        // 禁止选择时间范围限制，return true 无法选择
        disabledDate(time) {},
        // 点击选择时间的回调，只选择一个返回默认是最小时间，｛null，minDate｝
        onPick({ maxDate, minDate }) {},
      },
      days: 0, // 初始化时间开始日期小于当前时间的天数
      merchantMessageImage: merchantMessageImage(),
      rules: {},
      imageUrlPrefix: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/',
      equipmentTypesArr: [
        {
          label: '娃娃机',
          value: '娃娃机',
        },
        {
          label: '兑币机',
          value: '兑币机',
        },
        {
          label: '扭蛋机',
          value: '扭蛋机',
        },
        {
          label: '儿童类',
          value: '儿童类',
        },
        {
          label: '洗衣机',
          value: '洗衣机',
        },
        {
          label: '按摩椅',
          value: '按摩椅',
        },
        {
          label: '充电桩',
          value: '充电桩',
        },
        {
          label: '其他',
          value: '其他',
        },
      ],
      typeOptions: [
        {
          value: '-1',
          label: '首页',
        },
        {
          value: '0',
          label: '我的',
        },
        {
          value: '1',
          label: '钱包',
        },
      ],
    };
  },

  beforeCreate() {},

  created() {},

  beforeMount() {},

  mounted() {},

  beforeUpdate() {},

  updated() {},

  activated() {},

  deactivated() {},

  beforeDestroy() {},

  destroyed() {},

  errorCaptured() {},

  methods: {
    // 打开弹窗
    open(item) {
      this.initRule();
      this.initDataPicker();
      this.initFormData();
      this.initEdit(item);
      this.visible = true;
      this.changeShowType();
    },
    // 初始化验证
    initRule() {
      this.rules = {
        equipmentTypesArr: [
          { type: 'array', required: true, message: '请至少选择一个设备类型', trigger: 'change' },
        ],
        date: [{ type: 'array', required: true, message: '请选择起止日期', trigger: 'change' }],
        linkType: [{ required: true, message: '请选择跳转类型', trigger: 'change' }],
        banner: [{ required: true, message: '请上传banner图片', trigger: 'change' }],
        showType: [{ required: true, message: '请选择指定商家类型', trigger: 'change' }],
      };
    },
    changeShowType() {
      const flag = this.formData.showType === 'code';
      this.rules['whiteListType'] = [
        { required: flag, message: '请输入白名单编号', trigger: 'change' },
      ];
      if (!flag) {
        this.$nextTick(() => {
          this.$refs['ruleForm'].clearValidate('whiteListType');
        });
      }
    },
    // 初始化新建表单参数
    initFormData() {
      this.formData = {
        lyyMessagePushId: '',
        date: [], // 起止日期
        equipmentTypes: '',
        equipmentTypesArr: [],
        banner: '',
        link: '',
        linkType: 2,
        startTime: '',
        endTime: '',
        showType: 'all',
        whiteListType: '',
        sort: null,
        miniAppId: '',
        miniAppSecret: '',
        miniPage: '',
        type: '-1',
      };
    },
    // 初始化编辑表单参数
    initEdit(item) {
      this.formData = Object.assign(this.formData, item);
      if (this.formData.lyyMessagePushId) {
        if (this.formData.equipmentTypes) {
          this.formData.equipmentTypesArr = this.formData.equipmentTypes.split(',');
          this.formData.date = [this.formData.startTime, this.formData.endTime];
        }
        if (this.formData.whiteListType) {
          this.formData.showType = 'code';
        }
        // 兼容旧数据
        if (!this.formData.linkType) {
          this.formData.linkType = 2;
        }
      }
    },
    // 关闭弹窗
    handleClose() {
      this.visible = false;
    },
    // 保存弹窗
    handleSave() {
      this.isParamsComplate(() => {
        this.visibleLoading = true;
        merchantMessageSave(this.getParams()).then((res) => {
          this.visibleLoading = false;
          if (res.result === 0) {
            this.$message.success('保存成功');
            this.$emit('confirm');
            this.handleClose();
          }
        });
      });
    },
    // 获取保存参数
    getParams() {
      let params = {
        banner: this.formData.banner,
        startTime: this.formData.startTime,
        endTime: this.formData.endTime,
        equipmentTypes: this.formData.equipmentTypes,
        sort: this.formData.sort,
        linkType: this.formData.linkType,
        type: this.formData.type,
      };
      if (
        this.formData.equipmentTypesArr &&
        Array.isArray(this.formData.equipmentTypesArr) &&
        this.formData.equipmentTypesArr.length > 0
      ) {
        params['equipmentTypes'] = this.formData.equipmentTypesArr.toString();
      }
      if (this.formData.link) {
        params['link'] = this.formData.link;
      }
      if (this.formData.lyyMessagePushId) {
        params['lyyMessagePushId'] = this.formData.lyyMessagePushId;
      }
      if (this.formData.showType === 'code') {
        params['whiteListType'] = this.formData.whiteListType;
      }
      // 跳转类型为小程序才需要传miniAppId与miniPage
      if (this.formData.linkType == 1 && this.formData.miniAppId) {
        params['miniAppId'] = this.formData.miniAppId;
      }
      if (this.formData.linkType == 1 && this.formData.miniAppSecret) {
        params['miniAppSecret'] = this.formData.miniAppSecret;
      }
      if (this.formData.linkType == 1 && this.formData.miniPage) {
        params['miniPage'] = this.formData.miniPage;
      }
      return params;
    },
    // 验证form表单参数是否完整
    async isParamsComplate(call) {
      await this.$refs['ruleForm'].validate((isSuccess, valid) => {
        if (isSuccess) {
          call();
          return true;
        } else {
          let sign = '';
          if (valid && typeof valid === 'object' && !Array.isArray(valid)) {
            const key = Object.keys(valid);
            sign = valid[key[0]][0]['message'];
            this.$message.error(sign);
          }
          return false;
        }
      });
    },
    // 上传到后台前的图片验证
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isGIF = file.type === 'image/gif';
      const isQualified = file.size / 1024 / 1024 < this.fileMaxSize;
      if (!isJPG && !isPNG && !isGIF) {
        this.$message.error('上传物料图片只能是 JPG或PNG 格式!');
      }
      if (!isQualified) {
        this.$message.error(`上传头像图片大小不能超过 ${this.fileMaxSize}MB!`);
      }
      if ((isJPG || isPNG || isGIF) && isQualified) {
        this.imageLoading = true;
        return true;
      }
    },
    // 上传图片的回调
    handleAvatarSuccess(res, file) {
      this.imageLoading = false;
      if (res.result === 1) {
        this.formData.banner = this.imageUrlPrefix + res.para.split(';')[0];
      } else {
        this.$message.error('上传失败');
        this.imageClear();
      }
    },
    // 清除图片
    imageClear() {
      this.formData.banner = '';
    },
    // 选择结算日期
    changeDate(dates) {
      if (dates) {
        this.formData.startTime = dates[0];
        this.formData.endTime = dates[1];
      } else {
        this.formData.startTime = '';
        this.formData.endTime = '';
      }
    },
    initDataPicker() {
      // 初始化date和fordate的值
      const dateStart = dateFormat(
        new Date(Date.now() - this.days * 24 * 60 * 60 * 1000),
        'yyyy-MM-dd'
      );
      const dateEnd = dateFormat(new Date(new Date().setHours(0, 0, 0, 0)), 'yyyy-MM-dd');
      this.formData.date = [dateStart, dateEnd];
      this.changeDate(this.formData.date);
      //初始化datepicker最大时间和跨度和选中回调和快捷选择时间
      this.pickerOptions = {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
              const days = 0; // 最大天数
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - times * days);
              end.setTime(end.getTime() - times * days);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '7天',
            onClick(picker) {
              const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
              const days = 6; // 最大天数
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + times * days);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '30天',
            onClick(picker) {
              const times = 1 * 24 * 60 * 60 * 1000; // 最大毫秒数
              const days = 29; // 最大天数
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + times * days);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本周',
            onClick(picker) {
              const date = new Date();
              const week = date.getDay();
              const end = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 6 - week);
              const start = date;
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '本月',
            onClick(picker) {
              const date = new Date();
              const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);
              nextMonth.setDate(0);
              const end = new Date(date.getFullYear(), date.getMonth(), nextMonth.getDate());
              const start = date;
              picker.$emit('pick', [start, end]);
            },
          },
        ],
        disabledDate(time) {
          const date = new Date();
          // 不能选择今天之前的日期
          const lessToday = (pickTime) => {
            return pickTime.getTime() < date.setHours(0, 0, 0, 0);
          };
          return lessToday(time);
        },
        onPick({ maxDate, minDate }) {
          const date = new Date();
          // 不能选择今天之前的日期
          const lessToday = (pickTime) => {
            return pickTime.getTime() < date.setHours(0, 0, 0, 0);
          };
          return lessToday(minDate);
        },
      };
    },
    formatValue() {
      const val = this.formData.sort;
      if (val) {
        this.formData.sort = String(val).replace(/[^\d]/g, '');
      }
    },
  },

  computed: {},

  watch: {
    'formData.linkType': function (newValue) {
      let miniRules = {
        miniAppId: [{ required: true, message: '请输入appid', trigger: 'change' }],
        miniAppSecret: [{ required: true, message: '请输入appSecret', trigger: 'change' }],
        miniPage: [{ required: true, message: '请输入跳转链接', trigger: 'change' }],
      };
      if (newValue == 1) {
        Object.assign(this.rules, miniRules);
      } else {
        Object.keys(miniRules).map((key) => {
          delete this.rules[key];
        });
      }
      this.$nextTick(() => {
        this.$refs['ruleForm'].clearValidate();
      });
    },
  },
};
</script>
<style lang="less" scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #000;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 200px;
  height: 60px;
  line-height: 60px;
  text-align: center;
}

.avatar {
  width: 200px;
  height: 60px;
  display: block;
}

.upload-box {
  /deep/ .el-upload-dragger {
    width: 200px;
    height: 60px;
  }
}

.img-close {
  position: absolute;
  top: 0;
  left: 170px;
  z-index: 100;
  font-size: 30px;
  transform: translate(50%, -50%);
  cursor: pointer;
}
.white-list-code {
  display: inline-block;
  width: 50%;
}
</style>
