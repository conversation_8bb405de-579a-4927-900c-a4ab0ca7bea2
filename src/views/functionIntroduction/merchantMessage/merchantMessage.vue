<!--
日期：2019/10/22
功能：B端后台品类消息推送
作者：杨楠锋
-->
<template>
  <div>
    <el-card>
      <div class="operation">
        <el-button type="success" icon="el-icon-circle-plus-outline" @click="addBtn"
          >添加消息
        </el-button>
      </div>
      <div>
        <!--列表 start-->
        <el-table
          :data="pageInfo.list"
          border
          highlight-current-row
          style="width: 100%; margin-bottom: 20px"
          v-loading="pageInfo.loading"
        >
          <template v-for="(item, index) in colums">
            <el-table-column
              v-if="item.key === 'operation'"
              :key="index"
              :prop="item.key"
              :label="item.label"
              :width="item.width"
              :sortable="item.sortable"
              align="center"
            >
              <template slot-scope="scope">
                <div>
                  <el-button type="text" class="info-btn" @click="editBtn(scope.row)"
                    >编辑
                  </el-button>
                  <el-button
                    type="text"
                    class="info-btn"
                    @click="deleteBtn(scope.row.lyyMessagePushId)"
                  >
                    <span class="danger">删除</span>
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="item.key === 'banner'"
              :key="index"
              :prop="item.key"
              :label="item.label"
              :width="item.width"
              :sortable="item.sortable"
              align="center"
            >
              <template slot-scope="scope">
                <img :src="scope.row.banner" height="60" width="60" v-viewer />
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="item.key === 'linkType'"
              :key="index"
              :prop="item.key"
              :label="item.label"
              :width="item.width"
              :sortable="item.sortable"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.linkType == 1 ? '小程序' : 'h5' }}
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="item.key === 'type'"
              :key="index"
              :prop="item.key"
              :label="item.label"
              :width="item.width"
              :sortable="item.sortable"
              align="center"
            >
              <template slot-scope="scope">
                {{
                  scope.row.type == -1
                    ? '首页'
                    : scope.row.type == 0
                    ? '我的'
                    : scope.row.type == 1
                    ? '钱包'
                    : '其他'
                }}
              </template>
            </el-table-column>
            <el-table-column
              v-else-if="item.key === 'link'"
              :key="index"
              :prop="item.key"
              :label="item.label"
              :width="item.width"
              :sortable="item.sortable"
              align="center"
            >
              <template slot-scope="scope">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="点击链接即可复制"
                  placement="top-start"
                  v-if="scope.row.link && scope.row.linkType != 1"
                >
                  <el-button type="text" @click="copyLinkBtn(scope.row.link)"
                    ><span class="copy-link">{{ scope.row.link }}</span></el-button
                  >
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="点击链接即可复制"
                  placement="top-start"
                  v-if="scope.row.miniPage && scope.row.linkType == 1"
                >
                  <el-button type="text" @click="copyLinkBtn(scope.row.miniPage)"
                    ><span class="copy-link">{{ scope.row.miniPage }}</span></el-button
                  >
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              v-else
              :key="index"
              :prop="item.key"
              :label="item.label"
              :width="item.width"
              :sortable="item.sortable"
              align="center"
            />
          </template>
        </el-table>
        <el-pagination
          :page-sizes="[10, 20, 50]"
          :page-size="pageInfo.pageSize"
          :total="pageInfo.total"
          :current-page="pageInfo.pageIndex"
          background
          layout="total, prev, pager, next, sizes, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
        <!--列表 end-->
      </div>
    </el-card>
    <!--删除弹窗-->
    <merchant-message-dialog-delete ref="deleteRef" @confirm="getList()" />
    <!--新建或编辑弹窗-->
    <merchant-message-dialog-form ref="formRef" @confirm="getList()" />
  </div>
</template>

<script>
import 'viewerjs/dist/viewer.css';
import Viewer from 'v-viewer';
import Vue from 'vue';
import { merchantMessageQuery } from '@api/functionIntroduction/merchantMessage';
import Clipboard from 'clipboard';
import MerchantMessageDialogDelete from '@views/functionIntroduction/merchantMessage/children/merchantMessageDialogDelete';
import MerchantMessageDialogForm from '@views/functionIntroduction/merchantMessage/children/merchantMessageDialogForm';
Vue.use(Viewer);
export default {
  name: 'merchantMessage',
  props: {},
  components: { MerchantMessageDialogForm, MerchantMessageDialogDelete },
  data() {
    return {
      pageInfo: {
        total: 0,
        pageSize: 20,
        pageIndex: 1,
        loading: false,
        list: [],
      },
      // 列表每一列参数
      colums: [
        { key: 'operation', label: '操作' },
        { key: 'sort', label: '序号' },
        { key: 'equipmentTypes', label: '设备类型' },
        { key: 'banner', label: 'banner图片' },
        { key: 'linkType', label: '跳转类型' },
        { key: 'type', label: '位置' },
        { key: 'link', label: '链接' },
        { key: 'startTime', label: '开始时间', sortable: true },
        { key: 'endTime', label: '结束时间', sortable: true },
      ],
    };
  },

  beforeCreate() {},

  created() {},

  beforeMount() {},

  mounted() {
    this.getList();
  },

  beforeUpdate() {},

  updated() {},

  activated() {},

  deactivated() {},

  beforeDestroy() {},

  destroyed() {},

  errorCaptured() {},

  methods: {
    // 新建
    addBtn() {
      this.$refs.formRef.open({});
    },
    // 编辑
    editBtn(item) {
      this.$refs.formRef.open(item);
    },
    // 删除
    deleteBtn(lyyMessagePushId) {
      this.$refs.deleteRef.open(lyyMessagePushId);
    },
    // 点击链接复制
    copyLinkBtn(link) {
      const clipboard = new Clipboard('.copy-link', {
        text: function () {
          return link;
        },
      });
      clipboard.on('success', (e) => {
        this.$message.success('复制成功!');
        // 释放内存
        clipboard.destroy();
      });
      clipboard.on('error', (e) => {
        // 不支持复制
        this.$message.error('该浏览器不支持自动复制!');
        // 释放内存
        clipboard.destroy();
      });
    },
    // 获取列表数据
    getList() {
      this.pageInfo.loading = true;
      merchantMessageQuery(this.getParams()).then((res) => {
        this.pageInfo.loading = false;
        if (res.result === 0) {
          this.pageInfo.list = res.data.items;
          this.pageInfo.total = res.data.total;
        }
      });
    },
    // 获取列表参数
    getParams() {
      return {
        currentPage: this.pageInfo.pageIndex,
        pageSize: this.pageInfo.pageSize,
      };
    },
    // 改变列表每页条数
    handleSizeChange(val) {
      this.pageInfo.pageSize = val;
      this.pageInfo.total = 0;
      this.getList();
    },
    // 改变页码
    handleCurrentChange(val) {
      this.pageInfo.pageIndex = val;
      this.getList();
    },
  },

  computed: {},

  watch: {},
};
</script>
<style lang="less" scoped>
.operation {
  padding: 20px;
}
.copy-link {
  white-space: normal;
}
</style>
