<template>
  <div class="change-channel">
    <el-dialog title="切换算法" :visible.sync="showDialog" v-loading="loading">
      <div class="container">
        <div class="title">
          <span class="title-text">已选择{{lyyEquipmentIdList.length}}台设备</span>
          <span class="record-text" @click="handleOpenOperate" v-if="lyyEquipmentIdList.length === 1">操作记录</span>
        </div>
        <el-form :inline="true" label-position="right">
          <el-form-item label="切换算法渠道至：">
            <el-select v-model="algorithmChannel">
              <el-option
                v-for="(item, index) in switchAlgorithmChannelList"
                :key="index"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="tip-box">
          <div class="title-text">注意事项</div>
          <p>1、当切换算法渠道后，如商户设备下无原算法渠道设备时，原算法渠道下商品库内商品则会全部下架</p>
          <p>2、在切算法前所有订单都会按照原来算法规则进行结算</p>
          <p>3、当算法切换后，C端用户扫码会显示无商品，并且无法打开柜门</p>
          <p>4、如选择设备算法渠道与切换算法渠道一致则无任何操作</p>
        </div>
        <div class="bottom-btn">
          <el-button @click="showDialog=false">取消</el-button>
          <el-button type="primary" @click="handleToChange">确定切换</el-button>
        </div>
      </div>
    </el-dialog>
    <operateRecord ref="operateRecord"/>
  </div>
</template>

<script>
  import operateRecord from "@views/vending/equipmentManage/components/operateRecord";
  import { switchAlgorithm } from '@api/vending';

  export default {
    name: "changeChannelPopup",
    props: {
      switchAlgorithmChannelList: {
        type: Array,
        default: () => ([])
      }
    },
    components: {
      operateRecord,
    },
    data() {
      return {
        showDialog: false,
        algorithmChannel: '',
        list: [],
        lyyEquipmentIdList: [],
        loading: false,
        equipmentValue: '',
      };
    },
    methods: {
      open(selectedList) {
        this.algorithmChannel = '';
        this.equipmentValue = '';
        const list = [];
        selectedList.forEach(item => {
          list.push(item.lyyEquipmentId);
        });
        this.lyyEquipmentIdList = list;
        if (selectedList.length === 1) {
          this.equipmentValue = selectedList[0].equipmentValue ||'';
        }
        this.showDialog = true;
      },
      handleToChange() {
        this.$confirm('是否切换算法渠道？', '提示',{
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          if (!this.algorithmChannel) {
            this.$message.error('请选择需要切换的算法渠道');
            return;
          }
          const params = {
            algorithmChannel: this.algorithmChannel,
            lyyEquipmentIdList: this.lyyEquipmentIdList
          };
          this.loading = true;
          const res = await switchAlgorithm(params);
          this.loading = false;
          if (res && res.result === 0) {
            this.$message.success('切换算法成功');
            this.$emit('init');
            this.showDialog = false;
          }
        }).catch(() => {})
      },
      handleOpenOperate() {
        this.$refs.operateRecord.open(this.equipmentValue);
      },
    }
  }
</script>

<style scoped lang="less">
  .change-channel {
    .container {
      .title-text {
        color: #ff524c;
        font-size: 16px;
        font-weight: 600;
      }
      .title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        .record-text {
          color: #1a87ff;
        }
      }
      .tip-box {
        color: #ff524c;
        line-height: 25px;
      }
      .bottom-btn {
        text-align: center;
        margin: 12px 0;
      }
    }
  }
</style>
