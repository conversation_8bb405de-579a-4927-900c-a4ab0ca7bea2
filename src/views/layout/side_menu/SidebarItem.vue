<template>
  <div v-if="item.children !== null && item.children !== undefined">
    <template v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren)">
      <el-menu-item :index="basePath" v-if="onlyOneChild.hide !== 'Y'">
        <span class="iconfont" :class="onlyOneChild.icon"></span>
        <span slot="title">{{ onlyOneChild.name }} </span>
      </el-menu-item>
    </template>
    <el-submenu v-else-if="item.hide !== 'Y'" ref="subMenu" :index="basePath" popper-append-to-body>
      <template slot="title">
        <span>
          <span class="iconfont" :class="item.icon"></span>
          <span>{{ item.name }}</span>
        </span>
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.id"
        :is-nest="true"
        :item="child"
        :base-path="child.path"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
export default {
  name: 'SidebarItem',
  props: {
    // route object
    item: {
      type: Object,
      required: true,
    },
    isNest: {
      type: Boolean,
      default: false,
    },
    basePath: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      onlyOneChild: null,
    };
  },
  mounted() {},
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter((item) => {
        if (item.hide === 'Y') {
          return false;
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item;
          return true;
        }
      });
      // 当只有一个子路由器时，默认显示子路由器
      // 总是显示根目录的
      if (showingChildren.length === 1) {
        return false;
      }

      // 如果没有要显示的子路由器，则显示parent
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, noShowingChildren: true }
        return true;
      }

      return false;
    },
  },
};
</script>
