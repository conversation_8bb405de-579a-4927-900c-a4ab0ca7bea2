<template lang="html">
    <div class="side-menu">
        <el-menu
            :default-active="menuActiveStatus"
            @open="handleOpen"
            @close="handleClose"
            unique-opened
            ref="sideMenu"
            background-color="#252e49"
            text-color="#fff"
            active-text-color="#ffd04b"
            router
            >
            <div class="logo-panel">
                <img src="@img/nav-logo.png" alt="">
            </div>
            <el-autocomplete
              class="inline-input"
              v-model="searchMenuText"
              :fetch-suggestions="querySearch"
              placeholder="搜索菜单"
              size="mini"
              :trigger-on-focus="false"
              @select="handleSelect"
            ></el-autocomplete>
            <sidebar-item v-for="(route, index) in menuList" :key="route.id || index" :item="route" :base-path="route.path" />
        </el-menu>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getPermissionMenuTree } from '@api/menu';
import { getRecordCount } from '@/api/activitiesQuery/activities';
import { AUTHSYSTEMID, needAddRouteDynamic, setRoute, getRoute, addRoute } from '@/utils/menu';
import SidebarItem from './SidebarItem';
import { routerMap } from '@/router/routerMap';
import router from '@/router/index';
export default {
  components:{
    SidebarItem
  },
  data() {
    return {
      defaultRoute: "",
      searchMenuText: "",
      restaurants: [], //搜索的数据
      count: 0, // 申请终止人数
      menuList: [
        // {
        //     title: '系统管理',
        //     name: 'system',
        //     icon: 'el-icon-setting',
        //     children: []
        // },
        // {
        //     title: '菜单管理',
        //     name: 'menu',
        //     icon: 'el-icon-menu',
        //     children: [
        //         {
        //             title: '订单管理',
        //             name: 'menu',
        //             icon: 'el-icon-tickets'
        //         }
        //     ]
        // },
        // {
        //     title: '角色管理',
        //     name: 'role',
        //     icon: 'el-icon-star-on',
        //     children: []
        // },
        // {
        //     title: '用户管理',
        //     name: 'user',
        //     icon: 'el-icon-mobile-phone',
        //     children: []
        // },
      ]
    };
  },
  created() {
    this.defaultRoute = this.$route.name;
    // this.getRecordCountM()
  },
  mounted() {
    // this.$refs.sideMenu.open()
    this.getPermissionMenuTree()
  },
  computed: {
    ...mapGetters(["curRoutePath"]),
    // 设置搜索菜单
    allMenu() {
      let allMenuList = [];
      for (let i = 0; i < this.menuList.length; i++) {
        if (this.menuList[i].children) {
          for (let j = 0; j < this.menuList[i].children.length; j++) {
            const item = this.menuList[i].children[j];
            const menuItem = {
              value: item.name,
              path: item.path
            };
            allMenuList.push(menuItem);
          }
        }
      }
      return allMenuList;
    },
    menuActiveStatus() {
      // console.log(this.curRoutePath.split("/"), "=====curRoutePath");
      if (this.$route.name == "SassMerchantPayment") {
        this.$store.commit("setPaymentTitle", true);
      } else {
        this.$store.commit("setPaymentTitle", false);
      }
      return this.curRoutePath;
    }
  },
  methods: {
    handleOpen(key, keyPath) {},
    handleClose(key, keyPath) {},
    getRecordCountM() {
      getRecordCount().then(res => {
        if (res.result === 0) {
          this.count = res.data || 0
        }
      })
    },
    // select(index, indexPath) {
    //   if (index === '/activitiesQuery') {
    //     this.count = 0
    //   }
    // },
    // 搜索菜单
    querySearch(queryString, cb) {
      var restaurants = this.allMenu;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      console.log(results);
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return restaurant => {
        if (!queryString) {
          return false;
        }
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) !==
          -1
        );
      };
    },
    handleSelect(item) {
      if (item.path) {
        this.$router.push({
          path: item.path
        });
      }
    },
    async getPermissionMenuTree() {
      const routerPathIndex = routerMap.map(item=>{
        return item.value
      })
      const { code, body } = await getPermissionMenuTree({ authSystemId: AUTHSYSTEMID });
      if (code === '0000000' && Array.isArray(body) && body.length > 0) {
        // 处理路由的path
        this.menuList = this.traverse(body, routerPathIndex)

        // 临时添加智能体应用菜单项（用于测试）
        this.addAiAgentMenu()

        // 筛选组件库路由
        const arr = needAddRouteDynamic(this.menuList)
        // 缓存组件库页面
        const routerLocal = getRoute()
        // 没有缓存手动生成路由
        if (!routerLocal) {
          addRoute(arr)
        }
        setRoute(arr)
      } else {
        this.$message.error("获取菜单列表失败！");
        // 即使获取菜单失败，也添加临时菜单用于测试
        this.menuList = []
        this.addAiAgentMenu()
      }
    },
    // 临时添加智能体应用菜单项（用于测试）
    addAiAgentMenu() {
      const aiAgentMenu = {
        id: 'ai-agent-temp-' + Date.now(),
        authMenuId: 'ai-agent-temp-' + Date.now(),
        name: '智能体应用',
        value: 'AiAgentManage',
        path: '/aiAgent',
        icon: 'el-icon-cpu', // 使用Element UI内置图标
        hide: 'N',
        children: null,
        sort: 999 // 排序靠后
      }

      // 检查是否已存在该菜单项
      const exists = this.menuList.some(menu =>
        menu.value === 'AiAgentManage' || menu.path === '/aiAgent'
      )

      if (!exists && this.menuList && this.menuList.length > 0) {
        this.menuList.push(aiAgentMenu)
        console.log('✅ 临时添加智能体应用菜单成功:', aiAgentMenu)
      } else if (exists) {
        console.log('ℹ️ 智能体应用菜单已存在，跳过添加')
      }
    },
    // 处理路由的path
    traverse(value = [], routerPathIndex = []) {
      value.forEach((item)=>{
        if (item.children === null) {
          delete item.children;
        } else if (Array.isArray(item.children)) {
          this.traverse(item.children, routerPathIndex)
        }
        const index = routerPathIndex.indexOf(item.value)
        if (index > -1) {
            item.path = routerMap[index].html_templet || item.value
        }
        // path作为路由的index
        if (!item.path) {
          item.path = item.id || item.authMenuId
        }
        // 组件库页面
        if (!new RegExp(/^\//g).test(item.path)) {
          item.path = `/${item.path}`
        }
      })
      return value;
    },
  },
};
</script>

<style lang="css" scoped>
.side-menu {
  max-width: 250px;
  height: 100%;
  background: #252e49;
  overflow-x: hidden;
}

.el-menu {
  border-right: none;
}

.logo-panel {
  padding: 20px 0;
}

.logo-panel img {
  width: 100%;
  display: block;
  /*margin-left: 20px;*/
}

.el-autocomplete {
  width: 200px;
  display: block;
  margin: 10px auto 20px;
}

.iconfont {
  display: inline-block;
  width: 24px;
  margin-right: 5px;
  text-align: center;
  font-size: 18px;
}

.icon-htmal5icon06 {
  font-size: 20px;
}
.red {
  color: #fff;
  background: red;
  font-size: 12px;
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 50%;
  margin-left: 10px;
  text-align: center;
  font-weight: bold;
}
</style>
