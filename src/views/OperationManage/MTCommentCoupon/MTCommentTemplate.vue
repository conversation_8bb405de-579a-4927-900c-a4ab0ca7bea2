/*
* @Description: 美团点评新用户活动编辑
* @Author: 杨楠锋
* @Date: 2020-08-25 17:19:45
* @LastEditors: 杨楠锋
* @LastEditTime: 2020-08-25 17:19:45
*/
<template>
  <div class="MTCommentTemplate">
    <dialog-base ref="refDialogBase" @confirm="save">
      <el-form
        :inline="false"
        class="form-box"
        label-position="right"
        :model="formData"
        ref="ruleForm"
        :rules="rules"
        label-width="115px"
      >
        <el-form-item label="活动类型 :" prop="activityType">
          <el-select placeholder="请选择" filterable v-model="formData.activityType" disabled>
            <el-option
              v-for="(item, index) in activityTypeArr"
              :key="index"
              :label="item.activityTypeName"
              :value="item.activityType"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备派发类型 :" prop="equipmentTypeLimitList" required>
          <div class="margin-bottom-10" v-for="(item, index) in formData.equipmentTypeLimitList"
               :key="index">
            <span class="">派发</span>
            <el-select placeholder="请选择" v-model="item.receiveType" @change="couponChange(item)" clearable class="type-select">
              <el-option
                v-for="(item, index) in typeArr"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-select v-if="item.receiveType === 1" placeholder="请选择" filterable v-model="item.lyyCouponId"
                       class="coupon-select">
              <el-option
                v-for="(item, index) in couponArr"
                :key="index"
                :label="item.lyyCouponId + '-' + item.name + '-' + item.amount + '元'"
                :value="item.lyyCouponId"
              ></el-option>
            </el-select>
            <template v-if="item.receiveType === 1">
              <span class="mg-10">;</span>
              <el-select placeholder="请选择" filterable v-model="item.equipmentTypeIds"
                         class="device-select" clearable multiple>
                <el-option
                  v-for="(item1, index1) in couponEquipmentTypeArr"
                  :key="index1"
                  :disabled="item1.disabled"
                  :label="item1.typeName"
                  :value="item1.lyyEquipmentTypeId"
                ></el-option>
              </el-select>
            </template>
            <el-select v-else-if="item.receiveType === 2" placeholder="请选择" filterable v-model="item.equipmentTypeIds"
                       class="device-select" clearable multiple>
              <el-option
                v-for="(item1, index1) in amountEquipmentTypeArr"
                :key="index1"
                :disabled="item1.disabled"
                :label="item1.typeName"
                :value="item1.lyyEquipmentTypeId"
              ></el-option>
            </el-select>
            <el-select v-else-if="item.receiveType === 3" placeholder="请选择" filterable v-model="item.equipmentTypeIds"
                       class="device-select" clearable multiple>
              <el-option
                v-for="(item1, index1) in redPacketEquipmentTypeArr"
                :key="index1"
                :disabled="item1.disabled"
                :label="item1.typeName"
                :value="item1.lyyEquipmentTypeId"
              ></el-option>
            </el-select>
            <span class="">设备;</span>
            <span class="mg-10">单设备一天最多派发</span>
            <el-input class="sm-input" v-model="item.count" placeholder="请输入" clearable
                      @input="formatCount(index)" @blur="blurCount(index)"></el-input>
            <span class="">单</span>
            <span class="primary mg-10 btn" v-if="index === 0" @click="addList">增加</span>
            <span class="danger mg-10 btn" @click="deleteList(index)">删除</span>
          </div>
        </el-form-item>
      </el-form>
    </dialog-base>
  </div>
</template>

<script>
  import DialogBase from "@/components/tableBase/dialogBase.vue";
  import {
    directionalListCoupon,
    directionalSave,
    directionalDetail
  } from "@api/OperationManage/directionalCoupon.js";
  import {isNotEmptyObject} from "@/components/tableBase/utilsTableBase.js";
  import {materielUploadEquipmentType} from "@api/materielUpload";

  export default {
    name: "MTCommentTemplate",

    props: {},

    components: {DialogBase},

    mixins: [],

    data() {
      return {
        activityTypeArr: [],
        rules: {
          activityType: [
            {required: true, message: "请选择活动类型", trigger: "change"},
          ],
          lyyCouponId: [
            {required: true, message: "请选择优惠券", trigger: "change"},
          ],
        },
        formData: {
          activityType: null,
          machineOrientationActivityId: null,
          status: 2,
          description: null,
          lyyCouponId: null,
          equipmentTypeLimitList: [{equipmentTypeIds: [], count: null}],
          type: null,
        },
        couponArr: [],
        couponEquipmentTypeArr: [],
        amountEquipmentTypeArr: [],
        redPacketEquipmentTypeArr: [],
        typeArr: [
          {label: "优惠券", value: 1},
          {label: "余额", value: 2},
          {label: "广告红包", value: 3}
        ],
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.refDialogBase = this.$refs.refDialogBase;
      this.refDialogBase.setWidthFun("1255px");
      this.getDirectionalListCoupon();
      this.getEquipmentTypeArr();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {
    },

    errorCaptured() {
    },

    methods: {
      // 派发类型切换
      couponChange(item){
        item.equipmentTypeIds = [];
        item.lyyCouponId = null
        item.count = null
      },
      async getEquipmentTypeArr() {
        const res = await materielUploadEquipmentType();
        this.couponEquipmentTypeArr = [];
        this.amountEquipmentTypeArr = [];
        this.redPacketEquipmentTypeArr = [];
        if (res && res.result === 0 && res.data) {
          res.data.map(item => {
            if (['WWJ', 'DBJ', 'NDJ', 'SHJ', 'AMY', 'ZLJ', 'AMD'].includes(item.value)) {
              this.couponEquipmentTypeArr.push(item);
            } else if (['YLC', 'ETL','XYJ'].includes(item.value)) {
              this.redPacketEquipmentTypeArr.push(item);
            } else if (['XYJ', 'CDZ'].includes(item.value)) {
              this.amountEquipmentTypeArr.push(item);
            }
          });
        }
      },
      async getDirectionalListCoupon() {
        const res = await directionalListCoupon({pageIndex: 1, pageSize: 9999});
        if (res && res.result === 0) {
          this.couponArr = res.data;
        }
      },
      openFun(obj) {
        if (obj['activityType']) {
          obj['activityType'] = parseInt(obj['activityType']);
        }
        this.activityTypeArr = [{
          activityType: obj['activityType'],
          activityTypeName: obj['activityTypeName']
        }];
        this.reSetFormData();
        this.refDialogBase.showDefaultFooterFun();
        if (isNotEmptyObject(obj)) {
          this.formData = Object.assign(this.formData, obj);
          this.getDetail();
        }
        let title = "";
        if (!!this.formData.machineOrientationActivityId) {
          title = "详情";
        } else {
          title = "新建活动";
        }
        this.refDialogBase.setTitleFun(title);
        this.refDialogBase.openFun();
      },
      reSetFormData() {
        this.formData = {
          activityType: null,
          machineOrientationActivityId: null,
          status: 2,
          description: null,
          lyyCouponId: null,
          equipmentTypeLimitList: [{equipmentTypeIds: [], count: null}],
          type: null,
        };
      },
      initRule() {
        this.rules = {};
      },
      async getDetail() {
        const res = await directionalDetail({
          machineOrientationActivityId: this.formData.machineOrientationActivityId
        });
        if (res && res.result === 0 && res.data) {
          this.formData = Object.assign(this.formData, res.data);
          if (this.formData.equipmentTypeLimitList && this.formData.equipmentTypeLimitList.length > 0) {
            let arr = [];
            this.formData.equipmentTypeLimitList.forEach((item) => {
              let equipmentTypeIds = [];
              let equipmentTypeIdsStr = [];
              if (item.equipmentTypeIds) {
                equipmentTypeIdsStr = item.equipmentTypeIds.split(',');
                equipmentTypeIdsStr.forEach((item)=>{
                  equipmentTypeIds.push(parseInt(item));
                })
              }
              let obj = {equipmentTypeIds: equipmentTypeIds, count: item.count, receiveType: item.receiveType};
              if (obj.receiveType === 1) {
                obj.lyyCouponId = item.lyyCouponId;
              }
              arr.push(obj);
            });
            this.formData.equipmentTypeLimitList = arr;
          } else {
            this.formData.equipmentTypeLimitList = [{equipmentTypeIds: [], count: null, receiveType: 1}];
          }
        }
      },
      save() {
        this.isParamsComplete(async () => {
          this.refDialogBase.showDefaultFooterSaveBtnLoadingFun();
          const param = Object.assign({}, this.formData);
          if (param.equipmentTypeLimitList) {
            let arr = [];
            param.equipmentTypeLimitList.forEach((item, index) => {
              let equipmentTypeIds = '';
              if (Array.isArray(item.equipmentTypeIds)) {
                equipmentTypeIds = item.equipmentTypeIds.join(',');
              } else if (item.equipmentTypeIds) {
                equipmentTypeIds = item.equipmentTypeIds + '';
              }
              if (equipmentTypeIds && item.count) {
                let obj = {
                  equipmentTypeIds: equipmentTypeIds,
                  count: parseInt(item.count),
                  receiveType: item.receiveType,
                }
                if (item.receiveType === 1 && item.lyyCouponId) {
                  obj['lyyCouponId'] = item.lyyCouponId;
                }
                arr.push(obj)
              }
            })
            param.equipmentTypeLimitList = arr;
          }
          const res = await directionalSave(param);
          this.refDialogBase.highDefaultFooterSaveBtnLoadingFun();
          if (res && res.result === 0) {
            this.$message.success("保存成功");
            this.refDialogBase.closeFun();
            this.$emit("save");
          }
        });
      },
      // 参数是否完整
      async isParamsComplete(call) {
        await this.$refs["ruleForm"].validate((isSuccess, valid) => {
          if (isSuccess) {
            call();
            return true;
          } else {
            let sign = "";
            if (valid && typeof valid === "object" && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]["message"];
              this.$message.error(sign);
            }
            return false;
          }
        });
      },
      formatCount(index) {
        if (this.formData.equipmentTypeLimitList && this.formData.equipmentTypeLimitList[index]) {
          let val = this.formData.equipmentTypeLimitList[index]['count'];
          val = String(val).replace(/[^\d]/g, '');
          this.formData.equipmentTypeLimitList[index]['count'] = val;
        }
      },
      blurCount(index) {
        if (this.formData.equipmentTypeLimitList && this.formData.equipmentTypeLimitList[index]) {
          let val = this.formData.equipmentTypeLimitList[index]['count'];
          if (parseInt(val) < 1) {
            this.formData.equipmentTypeLimitList[index]['count'] = 1;
          } else if (parseInt(val) > 9999) {
            this.formData.equipmentTypeLimitList[index]['count'] = 9999;
          }
        }
      },
      addList() {
        this.formData.equipmentTypeLimitList.push({equipmentTypeIds: [], count: null});
      },
      deleteList(index) {
        this.formData.equipmentTypeLimitList.splice(index, 1);
      },
      optionDisabled(item, index) {
        console.log(item, index, this.formData.equipmentTypeLimitList)
        let flag = false;
        if (Array.isArray(this.formData.equipmentTypeLimitList)) {
          this.formData.equipmentTypeLimitList.forEach((item1, index1) => {
            console.log(item1.equipmentTypeIds.includes(item.lyyEquipmentTypeId))
            if (!flag && index1 !== index && item1.equipmentTypeIds.includes(item.lyyEquipmentTypeId)) {
              flag = true;
            }
          })
        }
        return flag;
      },
    },

    computed: {},

    watch: {}
  };
</script>
<style lang='css' scoped>
  .select-width {
    width: 300px;
  }

  .sm-select {
    width: 250px;
  }

  .sm-input {
    width: 100px;
  }

  .mg-10 {
    margin: 0 10px;
  }

  .btn {
    cursor: pointer;
  }

  .type-select{
    width: 90px;
  }

  .device-select{
    width: 325px;
  }

  .coupon-select{
    width: 200px;
  }
</style>
