<template>
  <div v-loading="loadingFlag">
    <section>
      <div slot="header" class="clearfix">
        <!-- 搜索栏 -->
        <el-form :inline="true">
          <el-form-item label="商家账号:">
            <el-input
              v-model.trim="query.distributorAccount"
              placeholder="请输入商家账号"
              class="width200"
              @change="getGroupListFn"
            ></el-input>
          </el-form-item>
          <el-form-item label="设备编号:">
            <el-input
              v-model.trim="query.keyword"
              placeholder="请输入设备编号"
              class="width200"
            ></el-input>
          </el-form-item>
          <el-form-item label="投放场地:">
            <el-select
              v-model="query.lyyEquipmentGroupId"
              placeholder="请选择投放场地"
              :disabled="!query.distributorAccount"
            >
              <el-option
                v-for="item in groupNameList"
                :key="item.lyyEquipmentGroupId"
                :label="item.name"
                :value="item.lyyEquipmentGroupId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getEquipmentsFn">查询</el-button>
          </el-form-item>
        </el-form>

        <!-- 活动状态栏 -->
        <el-form :inline="true">
          <el-form-item label="上架状态:">
            <el-row style="color:#0099ff">
              {{ activityStatus === "0" ? "未上架" : "上架中" }}
            </el-row>
          </el-form-item>
          <el-form-item>
            <el-button type="danger" @click="configMTActivityFn">
              {{ activityStatus === "0" ? "上" : "下" }}架
            </el-button>
          </el-form-item>
        </el-form>

        <el-row type="flex" justify="space-between" align="middle" class="margin-bottom-10">
          <!-- 设备统计 -->
          <el-col :span="8">
            <el-row type="flex">
              <el-col>
                <div>设备数: {{ equipmentDTO.totalNum }}台</div>
              </el-col>
              <el-col>
                <div style="color:#52cc00">上架中: {{ equipmentDTO.activityNum }}台</div>
              </el-col>
              <el-col>
                <div style="color:#42a1ff">未上架: {{ equipmentDTO.noActivityNum }}台</div>
              </el-col>
            </el-row>
          </el-col>

          <!-- 功能按钮 -->
          <el-col :span="4">
            <el-row type="flex" justify="space-around">
              <el-button type="success" @click="showDealGroupDialog(false)">新增设备</el-button>
              <el-upload
                class="upload-demo"
                action="#"
                :limit="1"
                :file-list="fileList"
                :http-request="upload"
                :show-file-list="false"
                ref="upload"
              >
                <el-button size="small" type="primary">导入</el-button>
              </el-upload>
              <el-button type="warning" @click="exportEquipmentFn">导出</el-button>
            </el-row>
          </el-col>
        </el-row>

        <!-- 设备列表 -->
        <el-table :data="tableData" border height="600">
          <el-table-column align="center" width="60" fixed="left">
            <template #header>
              <el-checkbox v-model="checkAll" />
            </template>
            <template #default="{row}">
              <el-checkbox-group v-model="checkedList">
                <el-checkbox
                  :label="row.meituanActivityEquipmentId"
                  :disabled="row.status === '1'"
                ></el-checkbox>
              </el-checkbox-group>
            </template>
          </el-table-column>
          <el-table-column prop="account" label="商家账号" align="center" width="120" />
          <el-table-column prop="equipmentTypeName" label="设备类型" align="center" />
          <el-table-column prop="equipmentValue" label="设备编号" align="center" width="120" />
          <el-table-column prop="cityName" label="城市范围" align="center" />
          <el-table-column prop="equipmentGroupName" label="投放场地" align="center" width="150" />
          <el-table-column label="团单ID" align="center" width="120">
            <template #default="{row}">
              <el-row v-if="row.dealGroupId">{{ row.dealGroupId }}</el-row>
              <el-link v-else type="danger" @click="showDealGroupDialog(true, row)">未设置</el-link>
            </template>
          </el-table-column>
          <el-table-column label="团单价格" align="center">
            <template #default="{row}">
              <el-row v-if="row.price">{{ row.price }}</el-row>
              <el-link v-else type="danger" @click="showDealGroupDialog(true, row)">未设置</el-link>
            </template>
          </el-table-column>
          <el-table-column label="团单总限额" align="center" width="100">
            <template #default="{row}">
              <el-row v-if="row.limitPrice">{{ row.limitPrice }}</el-row>
              <el-link v-else type="danger" @click="showDealGroupDialog(true, row)">未设置</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="上架状态" align="center">
            <template #default="{row}">
              <el-row v-if="row.status === '0'" style="color:#ff4542">未上架</el-row>
              <el-row v-else style="color:#18cd42">上架中</el-row>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="222">
            <template #default="{row}">
              <el-row type="flex" justify="center">
                <el-button
                  v-if="row.status === '1'"
                  type="primary"
                  @click="showDealGroupDialog(true, row)"
                  >编辑</el-button
                >
                <el-button
                  v-if="row.status === '1'"
                  type="danger"
                  @click="
                    configEquipmentFn('down', row.meituanActivityEquipmentId, row.equipmentValue)
                  "
                  >下架</el-button
                >
                <el-button
                  type="danger"
                  @click="delEquipmentFn(row.meituanActivityEquipmentId, row.equipmentValue)"
                  >删除</el-button
                >
              </el-row>
            </template>
          </el-table-column>
          <el-table-column label="订单数据" align="center">
            <template #default="{row}">
              <el-link type="primary" @click="showRecord(row)">订单记录</el-link>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination margin-top-10">
          <el-pagination
            background
            @size-change="pageSizeChangeFn"
            @current-change="pageChangeFn"
            :current-page="query.pageIndex"
            :page-sizes="[20, 50, 100, 200]"
            :page-size="query.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
          <el-button type="primary" @click="configEquipmentFn('up')"
            >确认上架(已选择{{ checkedList.length }}台)</el-button
          >
        </div>
      </div>
    </section>

    <!-- 弹窗-团单配置 -->
    <el-dialog
      width="30%"
      title="团单配置"
      :visible="dealGroupDialog"
      append-to-body
      @close="cancelDealGroup"
      :center="true"
    >
      <el-form ref="dealGroupForm" :model="dealGroup" :rules="rules" :inline="true">
        <el-form-item v-if="!editStatus" label="新增设备编号" prop="equipmentValue">
          <el-input v-model="dealGroup.equipmentValue" placeholder="请输入设备编号"></el-input>
        </el-form-item>
        <el-form-item label="团单ID" prop="dealGroupId">
          <el-input v-model.trim="dealGroup.dealGroupId" placeholder="请输入团单ID"></el-input>
        </el-form-item>
        <el-form-item label="团单价格" prop="price">
          <el-input
            v-model="dealGroup.price"
            placeholder="请输入团单价格"
            @blur="dealGroup.price = Number(dealGroup.price) || 0"
          ></el-input>
        </el-form-item>
        <el-form-item label="团单限额" prop="limitPrice">
          <el-input
            v-model="dealGroup.limitPrice"
            placeholder="请输入团单限额"
            @blur="dealGroup.limitPrice = Number(dealGroup.limitPrice) || 0"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelDealGroup">取 消</el-button>
        <el-button v-if="editStatus" type="primary" @click="dealGroupFn">确 定</el-button>
        <el-button v-else type="primary" @click="addEquipmentFn">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 弹窗-订单记录 -->
    <el-dialog
      title="订单记录"
      :visible="recordDialog"
      width="90%"
      :center="true"
      @close="recordDialog = false"
    >
      <el-row type="flex" class="margin-bottom-10">
        <el-col :span="4">商家账号: {{ recordParams.account }}</el-col>
        <el-col :span="4">设备编号: {{ recordParams.equipmentValue }}</el-col>
        <el-col :span="16">投放场地: {{ recordParams.equipmentGroupName }}</el-col>
      </el-row>
      <el-row type="flex" class="margin-bottom-10">
        <el-col :span="4">团单价格: {{ recordParams.price }}元</el-col>
        <el-col :span="4">团单限额: {{ recordParams.limitPrice }}元</el-col>
        <el-col :span="4">订单数: {{ recordProfile.orderNum }}单</el-col>
        <el-col :span="4">目前支付: {{ recordProfile.paymentAmount }}元</el-col>
        <el-col :span="4">离限额剩余: {{ recordProfile.balance }}元</el-col>
      </el-row>
      <el-form :inline="true">
        <el-form-item label="购买时间:">
          <el-date-picker v-model="recordParams.startDate" type="date" placeholder="选择日期时间" />
        </el-form-item>
        <el-form-item label="至">
          <el-date-picker v-model="recordParams.endDate" type="date" placeholder="选择日期时间" />
        </el-form-item>
        <el-form-item label="用户ID:">
          <el-input v-model.number="recordParams.lyyUserId" style="width:200px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getRecords('change')">查询</el-button>
          <el-button type="warning" @click="exportRecordFn">导出</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="recordList" border>
        <el-table-column prop="lyyUserId" label="用户ID" align="center"></el-table-column>
        <el-table-column prop="cityName" label="城市" align="center"></el-table-column>
        <el-table-column
          prop="equipmentGroupName"
          label="投放场地"
          align="center"
        ></el-table-column>
        <el-table-column prop="equipmentValue" label="设备编号" align="center"></el-table-column>
        <el-table-column label="购买套餐" align="center">
          <template #default="{row}">
            {{ row.price }}元{{ row.coins }}币
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="支付金额" align="center"></el-table-column>
        <el-table-column prop="created" label="支付时间" align="center"></el-table-column>
        <el-table-column prop="orderNo" label="订单编号" align="center"></el-table-column>
        <el-table-column prop="account" label="商家账号" align="center"></el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="recordParams.pageIndex"
        :page-sizes="[20, 50, 100, 200]"
        :page-size="recordParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="recordProfile.total"
      >
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
import {
  getMTActivityList,
  configMTActivity,
  getMTActivityEquipments,
  getGroupListByPhone,
  addMTActivityEquipment,
  configMTActivityEquipments,
  delMTActivityEquipment,
  exportMTActivityEquipments,
  configDealGroup,
  countEquipmentCoupon,
  getEquipmentCoupon,
  importMTActivityEquipments,
  exportEquipmentCoupon
} from "@/api/meiTuan.js";

export default {
  name: "mtTypeSet",

  data() {
    // 校验金额
    const checkMoney = (rule, val, callback) => {
      if ((val < 0.01) | (val > 99999.99)) {
        callback(new Error("金额范围0.01~99999.99元"));
      } else if (rule.field === "limitPrice" && val < this.paymentAmount) {
        callback(new Error("不能小于累计支付金额"));
      } else if (this.dealGroup.limitPrice && val > this.dealGroup.limitPrice) {
        callback(new Error("不能大于团单限额"));
      } else {
        callback();
      }
    };

    return {
      loadingFlag: false,
      activityStatus: "0", // 活动状态
      query: {
        // 搜索参数
        meituanActivityId: 1,
        distributorAccount: "",
        keyword: "",
        lyyEquipmentGroupId: "",
        pageIndex: 1,
        pageSize: 20
      },
      tableData: [], // 设备列表
      groupNameList: [], // 场地列表
      total: 0, // 设备数
      checkedList: [], // 选中列表
      equipmentDTO: {}, // 设备数量统计
      fileList: [], // 导入文件
      addDialog: false, // 弹窗-新增设备
      dealGroupDialog: false, // 弹窗-团单配置
      editStatus: false, // 是否编辑团单配置
      dealGroup: {
        // 团单配置
        meituanActivityEquipmentId: "",
        equipmentValue: "",
        dealGroupId: "",
        price: "",
        limitPrice: ""
      },
      paymentAmount: 0, // 设备累计支付金额
      rules: {
        // 校验规则
        equipmentValue: [{ required: true, message: "必填项" }],
        dealGroupId: [{ required: true, message: "必填项" }],
        price: [{ required: true, message: "必填项" }, { validator: checkMoney }],
        limitPrice: [{ required: true, message: "必填项" }, { validator: checkMoney }]
      },
      recordDialog: false, // 弹窗-订单记录
      recordParams: {
        // 请求参数-订单记录
        equipmentValue: "",
        startDate: new Date(),
        endDate: new Date(),
        lyyUserId: "",
        meituanActivityEquipmentId: 0,
        pageIndex: 1,
        pageSize: 20
      },
      recordProfile: {},
      recordList: []
    };
  },

  computed: {
    // 全选功能
    checkAll: {
      set(val) {
        this.checkedList = val
          ? this.tableData
              .filter(item => item.status === "0")
              .map(item => item.meituanActivityEquipmentId)
          : [];
      },
      get() {
        return this.checkedList.length
          ? this.checkedList.length === this.tableData.filter(item => item.status === "0").length
          : false;
      }
    }
  },

  async created() {
    this.loadingFlag = true;
    await this.getActivityFn();
    await this.getEquipmentsFn();
    this.loadingFlag = false;
  },

  methods: {
    // 美团活动查询
    async getActivityFn() {
      const { data } = await getMTActivityList({ pattern: 2 });
      this.query.meituanActivityId = data.meituanActivityId;
      this.activityStatus = data.status;
    },

    // 活动上下架
    async configMTActivityFn() {
      const status = this.activityStatus === "0" ? "1" : "0";
      if (status === "0") {
        await this.$confirm("您确定要下架全部设备的团单吗?", "下架提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        });
      }
      await configMTActivity({
        meituanActivityId: this.query.meituanActivityId,
        status
      });
      this.activityStatus = status;
      this.getEquipmentsFn();
    },

    // 获取设备列表
    async getEquipmentsFn() {
      const { data } = await getMTActivityEquipments(this.query);
      this.tableData = data.equipmentList.items;
      this.total = data.equipmentList.total;
      this.equipmentDTO = data.equipmentDTO;
    },

    // 获取场地列表
    async getGroupListFn() {
      this.query.lyyEquipmentGroupId = "";
      const { result, data } = await getGroupListByPhone({ phone: this.query.distributorAccount });
      if (result === 0) this.groupNameList = data;
    },

    // 新增设备
    async addEquipmentFn() {
      const res = await addMTActivityEquipment({
        meituanActivityId: this.query.meituanActivityId,
        ...this.dealGroup
      });
      if (res.result === 0) this.$message.success("成功新增设备");
      this.cancelDealGroup();
      this.getEquipmentsFn();
    },

    // 弹出门店团单对话框
    async showDealGroupDialog(type, item) {
      this.editStatus = type;
      if (type) {
        this.dealGroup = JSON.parse(JSON.stringify(item));
        const { data } = await countEquipmentCoupon(this.dealGroup);
        this.paymentAmount = data.paymentAmount;
      }
      this.dealGroupDialog = true;
    },

    // 配置门店团单
    async dealGroupFn() {
      await this.$refs.dealGroupForm.validate();
      await configDealGroup(this.dealGroup);
      this.cancelDealGroup();
      this.getEquipmentsFn();
    },

    // 取消配置门店团单
    cancelDealGroup() {
      this.$refs.dealGroupForm.clearValidate();
      this.$refs.dealGroupForm.resetFields();
      this.dealGroupDialog = false;
    },

    // 设备上下架
    async configEquipmentFn(type, meituanActivityEquipmentId, equipmentValue) {
      // 下架
      if (type === "down") {
        await this.$confirm(`你确定要下架 ${equipmentValue} 设备?`, "下架提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        });
        await configMTActivityEquipments({
          meituanActivityId: this.query.meituanActivityId,
          meituanActivityEquipmentIds: [meituanActivityEquipmentId],
          status: "0"
        });
        this.getEquipmentsFn();
      }
      // 上架
      if (type === "up") {
        const { result } = await configMTActivityEquipments({
          meituanActivityId: this.query.meituanActivityId,
          meituanActivityEquipmentIds: this.checkedList,
          status: "1"
        });
        if (result !== 0) return;
        if (this.checkedList.length === 1) {
          // 上架一台
          await this.$confirm("检查下是否线下物料也配置成功了", "保存成功", {
            confirmButtonText: "前往物料配置",
            cancelButtonText: "已经配好了",
            type: "warning"
          })
            .then(() => this.$router.push("/faceBrush/payMinus?activeName=activityMaterial"))
            .catch(() => {});
        } else {
          // 批量上架
          this.$alert(
            "检查下是否线下物料也配置成功了，如果不配可能导致商家使用，已配置的可以忽略.",
            "保存成功",
            { confirmButtonText: "确定" }
          );
        }
        this.checkedList = [];
        this.getEquipmentsFn();
      }
    },

    // 删除设备
    async delEquipmentFn(meituanActivityEquipmentId, equipmentValue) {
      await this.$confirm(`你确定删除 ${equipmentValue} 设备?`, "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      });
      const res = await delMTActivityEquipment({ meituanActivityEquipmentId });
      if (res.result === 0) this.$message.success("成功删除设备");
      if (this.tableData.length === 1) this.pageIndex--;
      this.getEquipmentsFn();
    },

    // 导入设备
    async upload({ file }) {
      const formdata = new FormData();
      formdata.append("file", file);
      formdata.append("meituanActivityId", this.query.meituanActivityId);
      const res = await importMTActivityEquipments(formdata);
      this.$refs.upload.clearFiles();
      if (res.result === 0) {
        if (res.data.fail) {
          this.$message.error({ message: res.data.fail, duration: 5000 });
        } else {
          this.$message.success(res.data.success);
        }
        this.getEquipmentsFn();
      }
    },

    // 导出设备列表
    async exportEquipmentFn() {
      const { data } = await exportMTActivityEquipments(this.query);
      const blob = new Blob([data], { type: "application/vnd.ms-excel" });
      const objectUrl = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = objectUrl;
      a.download = "设备列表.xlsx";
      a.click();
    },

    // 设备列表-页容量
    pageSizeChangeFn(val) {
      this.query.pageSize = val;
      this.getEquipmentsFn();
    },

    // 设备列表-页码
    pageChangeFn(val) {
      this.query.pageIndex = val;
      this.getEquipmentsFn();
    },

    // 显示弹窗-订单记录
    async showRecord(item) {
      this.recordParams = { ...this.recordParams, ...item };
      await this.getRecords();
      this.recordDialog = true;
    },

    // 获取订单记录
    async getRecords() {
      let { startDate, endDate } = this.recordParams;
      startDate = moment(startDate).format("YYYY-MM-DD 00:00:00");
      endDate = moment(endDate)
        .add(1, "d")
        .format("YYYY-MM-DD 00:00:00");
      const { data } = await getEquipmentCoupon({ ...this.recordParams, startDate, endDate });
      this.recordProfile = data.coupon;
      this.recordProfile.total = data.couponList.total;
      this.recordList = data.couponList.items;
    },

    // 导出订单记录
    async exportRecordFn() {
      const { meituanActivityId } = this.query;
      let { equipmentValue, startDate, endDate, meituanActivityEquipmentId } = this.recordParams;
      startDate = moment(startDate).format("YYYY-MM-DD 00:00:00");
      endDate = moment(endDate)
        .add(1, "d")
        .format("YYYY-MM-DD 00:00:00");
      const { data } = await exportEquipmentCoupon({
        meituanActivityId,
        meituanActivityEquipmentId,
        equipmentValue,
        startDate,
        endDate
      });
      const blob = new Blob([data], { type: "application/vnd.ms-excel" });
      const objectUrl = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = objectUrl;
      a.download = "订单记录.xlsx";
      a.click();
    },

    // 订单记录-页容量
    handleSizeChange(val) {
      this.recordParams.pageSize = val;
      this.getRecords();
    },

    // 订单记录-页码
    handleCurrentChange(val) {
      this.recordParams.pageIndex = val;
      this.getRecords();
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-checkbox__label {
  display: none;
}
.pagination {
  display: flex;
  justify-content: space-between;
}
</style>
