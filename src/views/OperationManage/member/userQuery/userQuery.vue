<!--
 * @Description: 新b端-用户余额余币查询
 * @Author:
 * @Date:
 * @LastEditors:
 * @LastEditTime:
 -->
<template>
  <div class="userQuery">
    <div class="userQuery-bg">
      <div class="outside">
        <div class="outside-item item-left card box-padding-l-r box-padding-t-b ">
          <p class="title">用户查询</p>
          <div class="form-box">
            <el-form :model="userFormData"
                     ref="ruleForm"
                     label-position="left"
                     label-width="135px"
                     :rules="userRules">
              <el-form-item label="用户ID" prop="userId">
                <el-input class="inline-input" v-model.trim="userFormData.userId"
                          placeholder="请输入"
                          clearable></el-input>
              </el-form-item>
              <el-form-item label="设备编号/商家账号" prop="telephoneOrEquipmentValue">
                <el-input class="inline-input" v-model.trim="userFormData.telephoneOrEquipmentValue"
                          placeholder="请输入"
                          clearable></el-input>
              </el-form-item>
            </el-form>
            <div class="btn" @click="userQueryFun"><p class="btn-text">查询</p></div>
            <div class="user-table-outside">
              <div v-show="unSearch.user" class="user-table-empty empty-box">
                <img src="./images/empty.png" class="img-el"/>
                <p class="empty-text">请先查询用户</p>
              </div>
              <div v-show="!unSearch.user" class="user-table">
                <table-base ref="refTableBaseUser">
                  <div slot="operation" slot-scope="scope">
                    <p v-if="chooseInfo.merchantUserId === scope.row.merchantUserId">-></p>
                    <el-button v-else type="text" @click="getDetail(scope.row)">详情</el-button>
                  </div>
                </table-base>
              </div>
            </div>
          </div>
        </div>
        <div class="outside-item item-right card box-padding-t-b">
          <p class="title box-padding-l-r">用户详情</p>
          <div class="divider"></div>
          <div class="coins-table-outside">
            <div v-show="unSearch.merchant" class="coins-table-empty empty-box">
              <img src="./images/empty.png" class="img-el"/>
              <p class="empty-text">请先查询用户</p>
            </div>
            <div v-show="!unSearch.merchant" class="box-padding-t-b box-padding-l-r">
              <div>
                <el-form :inline="true"
                         label-position="left"
                         label-width="75px">
                  <el-form-item label="查询条件">
                    <div class="equipment">
                      <div class="equipment-outside">
                        <el-select placeholder="场地名称" filterable v-model="coinsFormData.storeIds"
                                   clearable>
                          <el-option
                            v-for="(item, index) in groupList"
                            :key="index"
                            :label="item.name"
                            :value="item.equipmentGroupId"
                          ></el-option>
                        </el-select>
                      </div>
                      <div class="equipment-outside">
                        <el-select placeholder="设备类型" filterable
                                   v-model="coinsFormData.equipmentTypeIds"
                                   multiple collapse-tags
                                   clearable>
                          <el-option
                            v-for="(item, index) in typeList"
                            :key="index"
                            :label="item.name"
                            :value="item.equipmentTypeId"
                          ></el-option>
                        </el-select>
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item>
                    <div class="btn" @click="coinsQuery"><p class="btn-text">查询</p></div>
                  </el-form-item>
                  <br/>
                  <el-form-item>
                    <div class="date-box">
                      <div v-for="(item, index) in dateList" :key="index" class="date-item"
                           :class="{'active': dateType === item.value}"
                           @click="dateListFn(item.value)">
                        <p class="date-item-text ">{{item.label}}</p>
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item>
                    <el-date-picker
                      v-model="coinsFormData.date"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="yyyy-MM-dd"
                      :editable="false"
                      @change="setDateType('custom')"
                    ></el-date-picker>
                  </el-form-item>
                </el-form>
              </div>
              <div class="coins">
                <div class="coins-wrapper">
                  <p class="title">余额余币</p>
                  <el-button type="danger" @click="openAllCoinsPopup">清空余额余币</el-button>
                </div>
                <el-table class="coins-table" :data="userTableData">
                  <template v-for="item in userTableData[0]">
                    <el-table-column :label="item.classifyName">
                      <template slot-scope="scope">
                        <el-button type="text"
                                   @click="openSingleCoinsPopup(item)"
                                   v-if="![19, 29, 30, 102].includes(item.classify) && item.balance>0">
                          {{item.balance}}
                        </el-button>
                        <p v-else>{{item.balance}}</p>
                      </template>
                    </el-table-column>
                  </template>
                </el-table>
              </div>
              <div class="record" v-if="showRecord">
                <p class="title">交易记录</p>
                <div class="record-search">
                  <el-form :inline="true">
                    <el-form-item>
                      <el-select placeholder="请选择" filterable v-model="recordFormData.tradeType"
                                 clearable @change="merchantConsumptionRecord">
                        <el-option
                          v-for="(item, index) in consumptionTypeList"
                          :key="index"
                          :label="item.name"
                          :value="item.code"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item>
                      <el-input class="inline-input" v-model.trim="recordFormData.equipmentValue"
                                placeholder="请输入商户账号/设备编号"
                                @keyup.enter.native="merchantConsumptionRecord"
                                clearable></el-input>
                    </el-form-item>
                  </el-form>
                </div>
                <table-base ref="refTableBaseRecord" @callBack="merchantConsumptionRecord()">
                  <div slot="tradeAmount" slot-scope="scope">
                    <p v-if="scope.row.tradeAmount">
                      {{scope.row.tradeAmount}}{{getUnit(scope.row.benefitClassifyGroup)}}
                    </p>
                  </div>
                  <div slot="actualBenefit" slot-scope="scope">
                    <p v-if="scope.row.recordTypeGroup === 3">{{scope.row.actualBenefit}}</p>
                  </div>
                  <div slot="storeId" slot-scope="scope">
                    <p v-if="scope.row.storeId">{{getGroupName(scope.row.storeId)}}</p>
                  </div>
                </table-base>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <dialog-base ref="refDialogBase" @confirm="firstConfirm">
      <p slot="title" class="primary popup-title">清空操作</p>
      <p>是否清空当前搜索条件下的所有
        <template v-if="all">余额余币</template>
        <template v-else>{{singleCoinsInfo.name}}</template>
        ？</p>
    </dialog-base>
    <dialog-base ref="refDialogBaseSecond">
      <p slot="title" class="danger popup-title">是否确认？</p>
      <p>清空操作无法撤回，是否确认清空？</p>
      <div class="btn-wrapper">
        <el-button type="danger" @click="secondConfirm" :disabled="clearBtnLoading"
                   :loading="clearBtnLoading">确认清空
        </el-button>
        <el-button type="default" @click="closeSecond">取消</el-button>
      </div>
    </dialog-base>
  </div>
</template>

<script>

  import TableBase from "@components/tableBase/tableBase";
  import {
    merchantBriefsFind,
    merchantConsumptionRecord,
    merchantEquipmentList,
    merchantGroupPage,
    merchantUserBenefitClear,
    merchantUserBenefitQuery,
    merchantUserConsumptionTypeList
  } from "@api/OperationManage/member";
  import {dateFormat} from "@js/utils";
  import DialogBase from "@components/tableBase/dialogBase";

  export default {
    name: "userQuery",

    props: {},

    components: {DialogBase, TableBase},

    mixins: [],

    data() {
      return {
        refTableBaseUser: null,
        refTableBaseRecord: null,
        refTableBaseCoins: null,
        userFormData: {
          userId: null,
          telephoneOrEquipmentValue: null,
        },
        recordFormData: {},
        coinsFormData: {
          date: [new Date(), new Date()],
          storeIds: null,
          equipmentTypeIds: [],
        },
        userRules: {
          userId: [
            {required: true, message: '请输入用户ID', trigger: 'change'},
          ],
          telephoneOrEquipmentValue: [
            {required: true, message: '请输入设备编号/商家账号', trigger: 'change'},
          ]
        },
        unSearch: {
          user: true,
          merchant: true,
        },
        columnsUser: [
          {key: "userId", label: "用户id"},
          {key: "merchantPhone", label: "商户账号"},
          {key: "operation", label: "操作", slot: true},
        ],
        pageSize: 20,
        pageSizeArr: [20, 30, 40, 50],
        columnsRecord: [
          {key: "recordTypeGroupName", label: "交易类型"},
          {key: "createTime", label: "交易时间"},
          {key: "tradeTypeName", label: "支付方式"},
          {key: "tradeAmount", label: "支付金额", slot: true},
          {key: "storeId", label: "场地名称", slot: true},
          {key: "equipmentTypeName", label: "设备类型"},
          {key: "equipmentValue", label: "设备编号"},
          {key: "actualBenefit", label: "充值金额", slot: true},
        ],
        userTableData: [],
        columns: [
          {key: "充值币", label: "充值币", slot: true},
          {key: "商户派币", label: "商户派币", slot: true},
          {key: "平台币", label: "平台币", slot: true},
          {key: "广告币", label: "广告币", slot: true},
          {key: "闲时币", label: "闲时币", slot: true},
          {key: "红包币", label: "红包币", slot: true},
          {key: "延迟结算余币", label: "延迟结算余币", slot: true},
          {key: "充值余额", label: "充值余额", slot: true},
          {key: "商家派发余额", label: "商家派发余额", slot: true},
          {key: "平台余额", label: "平台余额", slot: true},
          {key: "红包余额", label: "红包余额", slot: true},
          {key: "广告红包", label: "广告红包", slot: true},
          {key: "延迟结算余额", label: "延迟结算余额", slot: true},
        ],
        groupList: [],
        typeList: [],
        chooseInfo: {},
        consumptionTypeList: [],
        singleCoinsInfo: {},
        all: false,
        refDialogBase: null,
        refDialogBaseSecond: null,
        clearBtnLoading: false,
        globalInfo: {},
        pickerOptions: {
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                picker.$emit('pick', [new Date(), new Date()]);
              }
            },
            {
              text: '昨天',
              onClick(picker) {
                const yesterday = dateFormat(new Date().getTime() - 1 * 24 * 60 * 60 * 1000) + ' 00:00:00';
                picker.$emit('pick', [new Date(yesterday), new Date(yesterday)]);
              }
            },
            {
              text: '最近7天',
              onClick(picker) {
                const today = new Date().getTime()
                const end = new Date(today);
                const start = new Date(today);
                start.setTime(start.getTime() - 86400000 * 7);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '最近30天',
              onClick(picker) {
                const today = new Date().getTime()
                const end = new Date(today);
                const start = new Date(today);
                start.setTime(start.getTime() - 86400000 * 30);
                picker.$emit('pick', [start, end]);
              }
            },
          ],
        },
        showRecord: false,
        dateList: [
          {label: '全部', value: 'all'},
          {label: '今天', value: 'today'},
          {label: '昨天', value: 'yesterday'},
          {label: '近7天', value: 'seven'},
          {label: '近30天', value: 'thirty'},
          {label: '自定义', value: 'custom'},
        ],
        dateType: 'all',
      };
    },

    beforeCreate() {
    },

    created() {
    },

    beforeMount() {
    },

    mounted() {
      this.refTableBaseUser = this.$refs.refTableBaseUser;
      this.refTableBaseUser.setColumnsSecurity(this.columnsUser);
      this.refTableBaseUser.setIsShowPagination(false);
      this.refTableBaseUser.setHighlightCurrentRow = () => {
        return false
      };
      this.refTableBaseUser.setRowClassName = ({row, rowIndex}) => {
        if (row.merchantUserId === this.chooseInfo.merchantUserId) {
          return 'success-row';
        }
      };

      // this.refTableBaseCoins = this.$refs.refTableBaseCoins;
      // this.refTableBaseCoins.setColumnsSecurity(this.columns);
      // this.refTableBaseCoins.setIsShowPagination(false);

      if (this.showRecord) {
        this.refTableBaseRecord = this.$refs.refTableBaseRecord;
        this.refTableBaseRecord.setColumnsSecurity(this.columnsRecord);
        this.refTableBaseRecord.setPageSizeArrSecurity(this.pageSizeArr);
        this.refTableBaseRecord.setPageSizeSecurity(this.pageSize);
      }

      this.refDialogBase = this.$refs.refDialogBase;
      this.refDialogBase.setDefaultFooterSaveBtnTextFun('确认清空');

      this.refDialogBaseSecond = this.$refs.refDialogBaseSecond;
      this.refDialogBaseSecond.highDefaultFooterFun();

      // this.merchantUserConsumptionTypeList();
    },

    beforeUpdate() {
    },

    updated() {
    },

    activated() {
    },

    deactivated() {
    },

    beforeDestroy() {
    },

    destroyed() {

    },

    errorCaptured() {
    },

    methods: {
      userQueryFun() {
        this.isParamsComplete(async () => {
          this.reSetRight();
          this.resetChoose();
          this.unSearch.user = false;
          this.globalInfo = {};
          this.refTableBaseUser.showLoadingFun();
          this.refTableBaseUser.reSetListFun();
          const res = await merchantBriefsFind(this.userFormData);
          this.refTableBaseUser.closeLoadingFun();
          if (res && res.result === 0 && res.data && Array.isArray(res.data.merchantUsers)) {
            this.refTableBaseUser.setListSecurity(res.data.merchantUsers);
            this.globalInfo = res.data.effectiveParam || {};
            if (res.data.merchantUsers.length === 1) {
              this.getDetail(res.data.merchantUsers[0]);
              this.coinsQuery();
            }
          }
        })
      },
      reSetRight() {
        this.unSearch.merchant = true;
        this.clearBtnLoading = false;
        this.coinsFormData = {
          date: [],
          storeIds: null,
          equipmentTypeIds: [],
        };
        this.dateType = 'all';
        this.recordFormData = {};
        this.singleCoinsInfo = {};
      },
      // 参数是否完整
      async isParamsComplete(call) {
        await this.$refs["ruleForm"].validate((isSuccess, valid) => {
          if (isSuccess) {
            call();
            return true;
          } else {
            let sign = "";
            if (valid && typeof valid === "object" && !Array.isArray(valid)) {
              const key = Object.keys(valid);
              sign = valid[key[0]][0]["message"];
              this.$message.error(sign);
            }
            return false;
          }
        });
      },
      async merchantConsumptionRecord() {
        let params = {
          ...this.getCoinsParams(),
          pageIndex: this.refTableBaseRecord.pageInfo.pageIndex,
          pageSize: this.refTableBaseRecord.pageInfo.pageSize,
        }
        if (this.recordFormData.tradeType) {
          params.tradeType = [this.recordFormData.tradeType]
        }
        if (this.recordFormData.equipmentValue) {
          params.equipmentValue = this.recordFormData.equipmentValue
        }
        this.refTableBaseRecord.showLoadingFun();
        this.refTableBaseRecord.reSetListFun();
        const res = await merchantConsumptionRecord(params);
        this.refTableBaseRecord.closeLoadingFun();
        if (res && res.result === 0 && res.data && Array.isArray(res.data.records)) {
          this.refTableBaseRecord.setListSecurity(res.data.records);
        }
      },
      getDetail(row) {
        this.reSetRight();
        if (typeof row === 'object') {
          if (typeof this.globalInfo === 'object' && this.globalInfo.byEquipment) {
            if (this.globalInfo.storeId) {
              this.coinsFormData.storeIds = this.globalInfo.storeId;
            }
            if (this.globalInfo.equipmentTypeId) {
              this.coinsFormData.equipmentTypeIds = [this.globalInfo.equipmentTypeId];
            }
            if (this.globalInfo.equipmentValue) {
              this.recordFormData.equipmentValue = this.globalInfo.equipmentValue;
            }
          }
          this.chooseInfo = JSON.parse(JSON.stringify(row));
          this.unSearch.merchant = false;
          this.getMerchantGroupPage();
          this.getMerchantEquipmentList();
        }
      },
      resetChoose() {
        this.chooseInfo = {};
      },
      async getMerchantGroupPage() {
        this.groupList = [];
        const res = await merchantGroupPage({
          pageIndex: 1,
          pageSize: 9999,
          merchantId: this.chooseInfo.merchantId,
        });
        if (res && res.result === 0 && res.data && Array.isArray(res.data.list)) {
          this.groupList = res.data.list;
        }
      },
      async getMerchantEquipmentList() {
        this.typeList = [];
        const res = await merchantEquipmentList({
          merchantId: this.chooseInfo.merchantId,
        });
        if (res && res.result === 0 && res.data && Array.isArray(res.data)) {
          this.typeList = res.data;
        }
      },
      coinsQuery() {
        this.getCoinsList();
        if (this.showRecord) {
          this.merchantConsumptionRecord();
        }
      },
      getTranslate(classifyName) {
        let name = '';
        switch (classifyName) {
          case '用户充值余币':
            name = '充值币';
            break;
          case '商户派发余币':
            name = '商户派币';
            break;
          case '平台派发余币':
            name = '平台币';
            break;
          case '广告币':
            name = '广告币';
            break;
          case '闲时币':
            name = '闲时币';
            break;
          case '红包币':
            name = '红包币';
            break;
          case '用户充值余额':
            name = '充值余额';
            break;
          case '商户派发余额':
            name = '商家派发余额';
            break;
          case '平台派发余额':
            name = '平台余额';
            break;
          case '红包余额':
            name = '红包余额';
            break;
          case '广告红包':
            name = '广告红包';
            break;
          default:
            name = classifyName
            break;
        }
        return name;
      },
      async getCoinsList() {
        let params = this.getCoinsParams();
        // this.refTableBaseCoins.showLoadingFun();
        // this.refTableBaseCoins.reSetListFun();
        const res = await merchantUserBenefitQuery(params);
        // this.refTableBaseCoins.closeLoadingFun();
        if (res && res.result === 0 && Array.isArray(res.data) && res.data.length > 0) {
          const translate = {};
          res.data.forEach(item => {
            const name = this.getTranslate(item.classifyName);
            if (name) {
              translate[name] = {...item, name};
            }
          })
          this.userTableData = [translate]
          // this.refTableBaseCoins.setListSecurity([translate]);
        }
      },
      getCoinsParams() {
        let params = {
          merchantUserId: this.chooseInfo.merchantUserId,
          merchantId: this.chooseInfo.merchantId,
        };
        if (this.userFormData.userId) {
          params.userId = this.userFormData.userId;
        }
        if (this.coinsFormData.storeIds) {
          params.storeIds = [this.coinsFormData.storeIds];
        }
        if (Array.isArray(this.coinsFormData.equipmentTypeIds) && this.coinsFormData.equipmentTypeIds.length > 0) {
          params.equipmentTypeIds = this.coinsFormData.equipmentTypeIds;
        }
        if (Array.isArray(this.coinsFormData.date) && this.coinsFormData.date.length > 0) {
          params.startTime = dateFormat(new Date(this.coinsFormData.date[0])) + ' 00:00:00';
          params.endTime = dateFormat(new Date(this.coinsFormData.date[1]).getTime() + 1 * 24 * 60 * 60 * 1000) + ' 00:00:00';
        }
        return params;
      },
      async merchantUserConsumptionTypeList() {
        const res = await merchantUserConsumptionTypeList();
        if (res && res.result === 0 && Array.isArray(res.data)) {
          this.consumptionTypeList = res.data;
        }
      },
      openAllCoinsPopup() {
        this.all = true;
        this.refDialogBase.openFun();
        this.clearBtnLoading = false;
      },
      openSingleCoinsPopup(row, item) {
        if (typeof row === 'object') {
          this.all = false;
          const list = Object.keys(row);
          if (Array.isArray(list) && list.length > 0) {
            this.singleCoinsInfo = row;
          }
          this.refDialogBase.openFun();
          this.clearBtnLoading = false;
        }
      },
      firstConfirm() {
        this.refDialogBaseSecond.openFun();
      },
      async secondConfirm() {
        this.clearBtnLoading = true;
        let params = this.getCoinsParams();
        if (this.all) {
          params.classify = []
        } else {
          params.classify = [this.singleCoinsInfo.classify]
        }
        const res = await merchantUserBenefitClear(params);
        this.clearBtnLoading = false;
        if (res && res.result === 0) {
          this.closeSecond();
          this.closeFirst();
          this.$message.success('清空成功');
          this.coinsQuery();
        }
      },
      closeFirst() {
        this.refDialogBase.closeFun();
      },
      closeSecond() {
        this.refDialogBaseSecond.closeFun();
      },
      getGroupName(id) {
        let group = this.groupList.find(item => item.equipmentGroupId === id) || {};
        return group.name || '';
      },
      getUnit(unit) {
        let name = '';
        if (unit) {
          switch (unit) {
            case 1:
              name = '币';
              break;
            case 2:
              name = '元';
              break;
            default:
              break;
          }
        }
        return name;
      },
      dateListFn(value) {
        if (value !== 'custom') {
          this.setDateType(value);
        }
      },
      setDateType(value) {
        this.dateType = value;
        const today = new Date().getTime()
        const end = new Date(today);
        const start = new Date(today);
        switch (value) {
          case 'today':
            this.coinsFormData['date'] = [new Date(), new Date()];
            break;
          case 'yesterday':
            const yesterday = dateFormat(new Date().getTime() - 1 * 24 * 60 * 60 * 1000) + ' 00:00:00';
            this.coinsFormData['date'] = [new Date(yesterday), new Date(yesterday)];
            break;
          case 'seven':
            start.setTime(start.getTime() - 86400000 * 6);
            this.coinsFormData['date'] = [start, end];
            break;
          case 'thirty':
            start.setTime(start.getTime() - 86400000 * 29);
            this.coinsFormData['date'] = [start, end];
            break;
          case 'all':
            this.coinsFormData['date'] = [];
            break;
          default:
            break;
        }
      },
    },

    computed: {},

    watch: {},
  }
</script>

<style lang="less" scoped>
  .userQuery {
    width: 100%;
    height: 100%;
    position: relative;

    .userQuery-bg {
      background-color: #f5f5f5;
      position: absolute;
      top: -30px;
      right: -30px;
      bottom: -30px;
      left: -30px;
      padding: 27px;
    }

    .outside {
      display: flex;
      height: 100%;
      overflow: hidden;
      overflow-x: auto;

      .outside-item {
        flex-grow: 1;
        flex-shrink: 0;
        height: 100%;
        overflow: hidden;
        overflow-y: auto;

        /deep/ .el-table {
          .el-table__header-wrapper {
            .el-table__header {
              thead {
                tr {
                  th {
                    &.el-table__cell {
                      background-color: #f8f8f8;
                    }
                  }
                }
              }
            }
          }

          .el-table__body-wrapper {
            .el-table__body {
              tbody {
                .el-table__row {
                  &.success-row {
                    background-color: #f0f9eb;
                  }
                }
              }
            }
          }
        }

        /deep/ .el-form {
          .el-form-item {
            margin-bottom: 20px;

            .el-form-item__label {
              font-size: 13px;
              font-family: PingFangSC, PingFangSC-Regular;
              font-weight: 500;
              color: #666666;
              padding-right: 8px;
            }
          }
        }

        &.item-left {
          margin-right: 12px;
          width: 416px;
          flex-grow: 0;

          .form-box {
            margin-top: 20px;
          }

          .user-table-outside {
            .user-table-empty {
              padding: 177px 0 200px;
            }

            .user-table {
              margin-top: 20px;
            }
          }
        }

        &.item-right {
          width: 772px;

          .divider {
            border-top: 1px solid #eeeeee;
            margin-top: 20px;
          }

          .coins-table-outside {
            .coins-table-empty {
              padding: 312px 0 200px;
            }

            .coins {
              .coins-wrapper {
                display: flex;
                align-items: center;
                justify-content: space-between;
              }

              .coins-table {
                margin-top: 12px;
              }
            }

            .record {
              margin-top: 32px;

              .record-search {
                /deep/ .el-form {
                  .el-form-item {
                    margin: 12px 9px 12px 0;
                  }
                }
              }
            }
          }
        }
      }

      .title {
        font-weight: 700;
        height: 22px;
        font-size: 16px;
        font-family: PingFangSC, PingFangSC-Semibold;
        text-align: left;
        color: #333333;
        line-height: 22px;
      }

      .card {
        background-color: #ffffff;
        border-radius: 10px;
      }

      .box-padding-l-r {
        padding-left: 20px;
        padding-right: 20px;
      }

      .box-padding-t-b {
        padding-top: 18px;
        padding-bottom: 18px;
      }

      .btn {
        background-color: #1188fe;
        display: flex;
        align-items: center;
        width: 88px;
        height: 32px;
        border-radius: 4px;

        .btn-text {
          flex-grow: 1;
          font-weight: 500;
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          text-align: center;
          color: #ffffff;
          line-height: 20px;
        }
      }

      .empty-box {
        text-align: center;

        .img-el {
          width: 68px;
          height: 68px;
        }

        .empty-text {
          margin-top: 16px;
          height: 25px;
          font-size: 18px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 500;
          color: #999999;
          line-height: 25px;
        }
      }
    }

    .popup-title {
      font-size: 20px;
    }

    .btn-wrapper {
      text-align: right;
      margin-top: 20px;
    }

    .equipment {
      display: flex;
      align-items: center;

      .equipment-outside {
        width: 220px;

        .el-select {
          width: 100%;
        }

        &:not(:first-child) {
          margin-left: 8px;
        }
      }
    }

    .date-box {
      display: flex;
      align-items: center;

      .date-item {
        flex-grow: 0;
        width: 72px;
        height: 32px;
        background: #eeeeee;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .date-item-text {
          height: 20px;
          font-size: 14px;
          font-family: PingFangSC, PingFangSC-Regular;
          font-weight: 400;
          text-align: center;
          color: #666666;
          line-height: 20px;
          user-select: none
        }

        &:not(:first-child) {
          margin-left: 8px;
        }

        &.active {
          background-color: #1188FE;

          .date-item-text {
            color: #FFFFFF;
          }
        }
      }
    }
  }
</style>
