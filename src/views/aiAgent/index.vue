<!--
 * @Description: 智能体应用管理
 * @Author: AI Assistant
 * @Date: 2025/09/19
 * @LastEditors: AI Assistant
 * @LastEditTime: 2025/09/19
 -->
<template>
  <div class="ai-agent-container">
    <!-- 搜索栏 -->
    <SearchBar
      :searching="loading"
      :refreshing="loading"
      @search="handleSearch"
      @reset="handleReset"
      @refresh="handleRefresh"
      ref="searchBar"
    />

    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" icon="el-icon-plus">
        新增应用
      </el-button>
      <el-button
        type="success"
        @click="handleRefresh"
        icon="el-icon-refresh"
        :loading="loading"
      >
        刷新
      </el-button>
      <el-button
        type="warning"
        @click="handleBatchDelete"
        icon="el-icon-delete"
        :disabled="selectedRows.length === 0"
        v-if="selectedRows.length > 0"
      >
        批量删除 ({{ selectedRows.length }})
      </el-button>
      <el-button
        type="info"
        @click="handleExport"
        icon="el-icon-download"
      >
        导出
      </el-button>
    </div>

    <!-- 应用列表 -->
    <div class="table-section">
      <AgentTable
        :table-data="tableData"
        :loading="loading"
        :pagination="pagination"
        :permissions="permissions"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @status-change="handleToggleStatus"
        @view-detail="handleViewDetail"
        @view="handleView"
        @edit="handleEdit"
        @copy="handleCopy"
        @export="handleExportConfig"
        @logs="handleViewLogs"
        @delete="handleDelete"
      />
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        background
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="appForm"
        :model="appForm"
        :rules="appFormRules"
        label-width="100px"
      >
        <el-form-item label="应用名称" prop="appName">
          <el-input v-model="appForm.appName" placeholder="请输入应用名称" />
        </el-form-item>
        <el-form-item label="应用描述" prop="description">
          <el-input
            type="textarea"
            v-model="appForm.description"
            placeholder="请输入应用描述"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="应用类别" prop="category">
          <el-select v-model="appForm.category" placeholder="请选择应用类别" style="width: 100%;">
            <el-option label="对话助手" value="chat" />
            <el-option label="文本生成" value="text" />
            <el-option label="图像处理" value="image" />
            <el-option label="数据分析" value="analysis" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="appForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAiAgentList,
  createAiAgent,
  updateAiAgent,
  deleteAiAgent,
  toggleAiAgentStatus,
  batchDeleteAiAgent,
  exportAiAgentList,
  copyAiAgent
} from '@/api/aiAgent'
import SearchBar from './components/SearchBar.vue'
import AgentTable from './components/AgentTable.vue'

export default {
  name: 'AiAgent',
  components: {
    SearchBar,
    AgentTable
  },
  data() {
    return {
      // 搜索参数
      searchParams: {},
      // 表格数据
      tableData: [],
      loading: false,
      // 分页
      pagination: {
        pageIndex: 1,
        pageSize: 20,
        total: 0
      },
      // 选中的行
      selectedRows: [],
      // 权限配置
      permissions: {
        view: true,
        edit: true,
        delete: true,
        copy: true,
        toggle: true,
        export: true,
        logs: true
      },
      // 对话框
      dialogVisible: false,
      dialogTitle: '',
      submitLoading: false,
      // 表单数据
      appForm: {
        id: null,
        appName: '',
        description: '',
        category: '',
        status: 1
      },
      // 表单验证规则
      appFormRules: {
        appName: [
          { required: true, message: '请输入应用名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入应用描述', trigger: 'blur' },
          { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择应用类别', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const params = {
          ...this.searchParams,
          pageIndex: this.pagination.pageIndex,
          pageSize: this.pagination.pageSize
        }
        const response = await getAiAgentList(params)
        if (response.code === '0000000') {
          this.tableData = response.data.list || []
          this.pagination.total = response.data.total || 0
        } else {
          this.$message.error(response.message || '获取数据失败')
        }
      } catch (error) {
        this.$message.error('获取数据失败')
        console.error('获取智能体应用列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 搜索
    handleSearch(params = {}) {
      this.searchParams = { ...params }
      this.pagination.pageIndex = 1
      this.getList()
    },
    // 重置搜索
    handleReset() {
      this.searchParams = {}
      this.pagination.pageIndex = 1
      this.getList()
    },
    // 刷新
    handleRefresh() {
      this.getList()
    },
    // 新增
    handleAdd() {
      this.dialogTitle = '新增应用'
      this.appForm = {
        id: null,
        appName: '',
        description: '',
        category: '',
        status: 1
      }
      this.dialogVisible = true
    },
    // 查看详情
    handleViewDetail(row) {
      this.$message.info(`查看应用详情: ${row.appName}`)
      // TODO: 实现查看详情功能
    },
    // 查看
    handleView(row) {
      this.$message.info(`查看应用: ${row.appName}`)
      // TODO: 实现查看功能
    },
    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑应用'
      this.appForm = { ...row }
      this.dialogVisible = true
    },
    // 切换状态
    async handleToggleStatus(row) {
      const action = row.status === 1 ? '禁用' : '启用'
      try {
        await this.$confirm(`确认${action}该应用吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await toggleAiAgentStatus({
          id: row.id,
          status: row.status === 1 ? 0 : 1
        })

        if (response.code === '0000000') {
          this.$message.success(`${action}成功`)
          this.getList()
        } else {
          this.$message.error(response.message || `${action}失败`)
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(`${action}失败`)
          console.error('切换状态失败:', error)
        }
      }
    },
    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该应用吗？删除后不可恢复！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await deleteAiAgent({ id: row.id })

        if (response.code === '0000000') {
          this.$message.success('删除成功')
          this.getList()
        } else {
          this.$message.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
          console.error('删除失败:', error)
        }
      }
    },
    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.appForm.validate()
        this.submitLoading = true

        const isEdit = !!this.appForm.id
        const apiMethod = isEdit ? updateAiAgent : createAiAgent
        const response = await apiMethod(this.appForm)

        if (response.code === '0000000') {
          this.$message.success(`${isEdit ? '更新' : '创建'}成功`)
          this.dialogVisible = false
          this.getList()
        } else {
          this.$message.error(response.message || `${isEdit ? '更新' : '创建'}失败`)
        }
      } catch (error) {
        if (error !== false) { // 表单验证失败时返回false
          this.$message.error(`${this.appForm.id ? '更新' : '创建'}失败`)
          console.error('提交失败:', error)
        }
      } finally {
        this.submitLoading = false
      }
    },
    // 关闭对话框
    handleDialogClose() {
      this.$refs.appForm.resetFields()
    },
    // 选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageIndex = 1
      this.getList()
    },
    // 当前页变化
    handleCurrentChange(page) {
      this.pagination.pageIndex = page
      this.getList()
    },

    // 排序变化
    handleSortChange({ prop, order }) {
      this.searchParams.sortBy = prop
      this.searchParams.sortOrder = order === 'ascending' ? 'asc' : 'desc'
      this.getList()
    },

    // 复制应用
    async handleCopy(row) {
      try {
        const { value: newAppName } = await this.$prompt('请输入新应用名称', '复制应用', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: `${row.appName}_副本`,
          inputValidator: (value) => {
            if (!value || value.trim() === '') {
              return '应用名称不能为空'
            }
            if (value.length > 50) {
              return '应用名称不能超过50个字符'
            }
            return true
          }
        })

        const response = await copyAiAgent({
          id: row.id,
          newAppName: newAppName.trim()
        })

        if (response.code === '0000000') {
          this.$message.success('复制成功')
          this.getList()
        } else {
          this.$message.error(response.message || '复制失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('复制失败')
          console.error('复制应用失败:', error)
        }
      }
    },

    // 批量删除
    async handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的应用')
        return
      }

      try {
        await this.$confirm(`确认删除选中的 ${this.selectedRows.length} 个应用吗？删除后不可恢复！`, '批量删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const ids = this.selectedRows.map(row => row.id)
        const response = await batchDeleteAiAgent({ ids })

        if (response.code === '0000000') {
          this.$message.success('批量删除成功')
          this.selectedRows = []
          this.getList()
        } else {
          this.$message.error(response.message || '批量删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('批量删除失败')
          console.error('批量删除失败:', error)
        }
      }
    },

    // 导出
    async handleExport() {
      try {
        this.$message.info('正在导出，请稍候...')
        const response = await exportAiAgentList(this.searchParams)

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `智能体应用列表_${new Date().toISOString().slice(0, 10)}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('导出成功')
      } catch (error) {
        this.$message.error('导出失败')
        console.error('导出失败:', error)
      }
    },

    // 导出配置
    handleExportConfig(row) {
      this.$message.info(`导出应用配置: ${row.appName}`)
      // TODO: 实现导出配置功能
    },

    // 查看日志
    handleViewLogs(row) {
      this.$message.info(`查看应用日志: ${row.appName}`)
      // TODO: 实现查看日志功能
    }
  }
}
</script>

<style lang="less" scoped>
.ai-agent-container {
  padding: 20px;

  .search-section {
    background: #fff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-form {
      margin-bottom: 0;
    }
  }

  .action-section {
    margin-bottom: 20px;
  }

  .table-section {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .pagination-section {
    margin-top: 20px;
    text-align: right;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
