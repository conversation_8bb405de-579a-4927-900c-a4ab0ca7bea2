<!--
 * @Description: 智能体应用搜索栏组件
 * @Author: AI Assistant
 * @Date: 2025/09/19
 * @LastEditors: AI Assistant
 * @LastEditTime: 2025/09/19
 -->
<template>
  <div class="search-bar">
    <el-form 
      ref="searchForm"
      :inline="true" 
      :model="searchForm" 
      class="search-form"
      label-width="80px"
    >
      <!-- 应用名称搜索 -->
      <el-form-item label="应用名称">
        <el-input
          v-model="searchForm.appName"
          placeholder="请输入应用名称"
          clearable
          @keyup.enter.native="handleSearch"
          @clear="handleSearch"
          style="width: 200px;"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </el-form-item>
      
      <!-- 应用类别 -->
      <el-form-item label="应用类别">
        <el-select
          v-model="searchForm.category"
          placeholder="请选择类别"
          clearable
          style="width: 150px;"
          @change="handleSearch"
        >
          <el-option label="全部" value="" />
          <el-option 
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <!-- 状态 -->
      <el-form-item label="状态">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          style="width: 120px;"
          @change="handleSearch"
        >
          <el-option label="全部" value="" />
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      
      <!-- 创建时间 -->
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="searchForm.createTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          style="width: 240px;"
          @change="handleSearch"
        />
      </el-form-item>
      
      <!-- 创建人 -->
      <el-form-item label="创建人">
        <el-input
          v-model="searchForm.creator"
          placeholder="请输入创建人"
          clearable
          @keyup.enter.native="handleSearch"
          @clear="handleSearch"
          style="width: 150px;"
        />
      </el-form-item>
      
      <!-- 操作按钮 -->
      <el-form-item>
        <el-button 
          type="primary" 
          @click="handleSearch"
          :loading="searching"
        >
          <i class="el-icon-search"></i>
          查询
        </el-button>
        
        <el-button @click="handleReset">
          <i class="el-icon-refresh-left"></i>
          重置
        </el-button>
        
        <el-button 
          type="success" 
          @click="handleRefresh"
          :loading="refreshing"
        >
          <i class="el-icon-refresh"></i>
          刷新
        </el-button>
      </el-form-item>
    </el-form>
    
    <!-- 高级搜索 -->
    <div class="advanced-search" v-if="showAdvanced">
      <el-form 
        :inline="true" 
        :model="advancedForm" 
        class="advanced-form"
        label-width="100px"
      >
        <!-- 使用次数范围 -->
        <el-form-item label="使用次数">
          <el-input-number
            v-model="advancedForm.usageCountMin"
            placeholder="最小值"
            :min="0"
            style="width: 120px;"
          />
          <span style="margin: 0 10px;">-</span>
          <el-input-number
            v-model="advancedForm.usageCountMax"
            placeholder="最大值"
            :min="0"
            style="width: 120px;"
          />
        </el-form-item>
        
        <!-- 更新时间 -->
        <el-form-item label="更新时间">
          <el-date-picker
            v-model="advancedForm.updateTimeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 240px;"
          />
        </el-form-item>
        
        <!-- 排序方式 -->
        <el-form-item label="排序方式">
          <el-select
            v-model="advancedForm.sortBy"
            placeholder="请选择排序字段"
            style="width: 150px;"
          >
            <el-option label="创建时间" value="createTime" />
            <el-option label="更新时间" value="updateTime" />
            <el-option label="使用次数" value="usageCount" />
            <el-option label="应用名称" value="appName" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="排序方向">
          <el-radio-group v-model="advancedForm.sortOrder">
            <el-radio label="desc">降序</el-radio>
            <el-radio label="asc">升序</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 高级搜索切换 -->
    <div class="search-toggle">
      <el-button 
        type="text" 
        @click="toggleAdvanced"
        class="toggle-btn"
      >
        {{ showAdvanced ? '收起高级搜索' : '展开高级搜索' }}
        <i :class="showAdvanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SearchBar',
  props: {
    searching: {
      type: Boolean,
      default: false
    },
    refreshing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showAdvanced: false,
      searchForm: {
        appName: '',
        category: '',
        status: '',
        createTimeRange: [],
        creator: ''
      },
      advancedForm: {
        usageCountMin: null,
        usageCountMax: null,
        updateTimeRange: [],
        sortBy: 'createTime',
        sortOrder: 'desc'
      },
      categoryOptions: [
        { label: '对话助手', value: 'chat' },
        { label: '文本生成', value: 'text' },
        { label: '图像处理', value: 'image' },
        { label: '数据分析', value: 'analysis' },
        { label: '其他', value: 'other' }
      ]
    }
  },
  methods: {
    // 搜索
    handleSearch() {
      const searchParams = {
        ...this.searchForm,
        ...this.advancedForm
      }
      
      // 处理时间范围
      if (searchParams.createTimeRange && searchParams.createTimeRange.length === 2) {
        searchParams.createTimeStart = searchParams.createTimeRange[0]
        searchParams.createTimeEnd = searchParams.createTimeRange[1]
      }
      delete searchParams.createTimeRange
      
      if (searchParams.updateTimeRange && searchParams.updateTimeRange.length === 2) {
        searchParams.updateTimeStart = searchParams.updateTimeRange[0]
        searchParams.updateTimeEnd = searchParams.updateTimeRange[1]
      }
      delete searchParams.updateTimeRange
      
      // 过滤空值
      const filteredParams = {}
      Object.keys(searchParams).forEach(key => {
        if (searchParams[key] !== '' && searchParams[key] !== null && searchParams[key] !== undefined) {
          filteredParams[key] = searchParams[key]
        }
      })
      
      this.$emit('search', filteredParams)
    },
    
    // 重置
    handleReset() {
      this.searchForm = {
        appName: '',
        category: '',
        status: '',
        createTimeRange: [],
        creator: ''
      }
      this.advancedForm = {
        usageCountMin: null,
        usageCountMax: null,
        updateTimeRange: [],
        sortBy: 'createTime',
        sortOrder: 'desc'
      }
      this.$refs.searchForm.resetFields()
      this.$emit('reset')
    },
    
    // 刷新
    handleRefresh() {
      this.$emit('refresh')
    },
    
    // 切换高级搜索
    toggleAdvanced() {
      this.showAdvanced = !this.showAdvanced
    },
    
    // 获取搜索参数
    getSearchParams() {
      const searchParams = {
        ...this.searchForm,
        ...this.advancedForm
      }
      
      // 处理时间范围
      if (searchParams.createTimeRange && searchParams.createTimeRange.length === 2) {
        searchParams.createTimeStart = searchParams.createTimeRange[0]
        searchParams.createTimeEnd = searchParams.createTimeRange[1]
      }
      delete searchParams.createTimeRange
      
      if (searchParams.updateTimeRange && searchParams.updateTimeRange.length === 2) {
        searchParams.updateTimeStart = searchParams.updateTimeRange[0]
        searchParams.updateTimeEnd = searchParams.updateTimeRange[1]
      }
      delete searchParams.updateTimeRange
      
      return searchParams
    },
    
    // 设置搜索参数
    setSearchParams(params) {
      Object.keys(params).forEach(key => {
        if (this.searchForm.hasOwnProperty(key)) {
          this.searchForm[key] = params[key]
        }
        if (this.advancedForm.hasOwnProperty(key)) {
          this.advancedForm[key] = params[key]
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  
  .search-form {
    margin-bottom: 0;
    
    .el-form-item {
      margin-bottom: 15px;
    }
  }
  
  .advanced-search {
    border-top: 1px solid #e4e7ed;
    padding-top: 20px;
    margin-top: 20px;
    
    .advanced-form {
      .el-form-item {
        margin-bottom: 15px;
      }
    }
  }
  
  .search-toggle {
    text-align: center;
    margin-top: 10px;
    
    .toggle-btn {
      color: #409eff;
      font-size: 14px;
      
      &:hover {
        color: #66b1ff;
      }
    }
  }
}
</style>
