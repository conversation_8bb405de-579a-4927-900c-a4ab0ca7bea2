<!--
 * @Description: 智能体应用列表表格组件
 * @Author: AI Assistant
 * @Date: 2025/09/19
 * @LastEditors: AI Assistant
 * @LastEditTime: 2025/09/19
 -->
<template>
  <div class="agent-table">
    <el-table
      ref="agentTable"
      :data="tableData"
      border
      v-loading="loading"
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      :row-class-name="tableRowClassName"
    >
      <!-- 多选框 -->
      <el-table-column 
        type="selection" 
        width="55" 
        align="center"
        :selectable="checkSelectable"
      />
      
      <!-- 序号 -->
      <el-table-column 
        type="index" 
        label="序号" 
        width="60" 
        align="center"
        :index="indexMethod"
      />
      
      <!-- 应用名称 -->
      <el-table-column 
        prop="appName" 
        label="应用名称" 
        min-width="150"
        sortable="custom"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <el-link 
            type="primary" 
            @click="handleViewDetail(scope.row)"
            :underline="false"
          >
            {{ scope.row.appName }}
          </el-link>
        </template>
      </el-table-column>
      
      <!-- 应用描述 -->
      <el-table-column 
        prop="description" 
        label="应用描述" 
        min-width="200"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span :title="scope.row.description">
            {{ scope.row.description || '-' }}
          </span>
        </template>
      </el-table-column>
      
      <!-- 应用类别 -->
      <el-table-column 
        prop="category" 
        label="应用类别" 
        width="120"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag 
            :type="getCategoryTagType(scope.row.category)"
            size="small"
          >
            {{ getCategoryLabel(scope.row.category) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <!-- 状态 -->
      <el-table-column 
        prop="status" 
        label="状态" 
        width="100" 
        align="center"
        sortable="custom"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="handleStatusChange(scope.row)"
            :disabled="!hasPermission('toggle')"
          />
        </template>
      </el-table-column>
      
      <!-- 使用次数 -->
      <el-table-column 
        prop="usageCount" 
        label="使用次数" 
        width="100"
        align="center"
        sortable="custom"
      >
        <template slot-scope="scope">
          <span class="usage-count">
            {{ scope.row.usageCount || 0 }}
          </span>
        </template>
      </el-table-column>
      
      <!-- 创建人 -->
      <el-table-column 
        prop="creator" 
        label="创建人" 
        width="120"
        align="center"
        show-overflow-tooltip
      />
      
      <!-- 创建时间 -->
      <el-table-column 
        prop="createTime" 
        label="创建时间" 
        width="180"
        align="center"
        sortable="custom"
      >
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      
      <!-- 更新时间 -->
      <el-table-column 
        prop="updateTime" 
        label="更新时间" 
        width="180"
        align="center"
        sortable="custom"
      >
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.updateTime) }}
        </template>
      </el-table-column>
      
      <!-- 操作 -->
      <el-table-column 
        label="操作" 
        width="220" 
        fixed="right"
        align="center"
      >
        <template slot-scope="scope">
          <el-button 
            type="text" 
            size="small" 
            @click="handleView(scope.row)"
            v-if="hasPermission('view')"
          >
            查看
          </el-button>
          
          <el-button 
            type="text" 
            size="small" 
            @click="handleEdit(scope.row)"
            v-if="hasPermission('edit')"
          >
            编辑
          </el-button>
          
          <el-button 
            type="text" 
            size="small" 
            @click="handleCopy(scope.row)"
            v-if="hasPermission('copy')"
          >
            复制
          </el-button>
          
          <el-dropdown 
            @command="handleCommand"
            v-if="hasMoreActions()"
          >
            <el-button type="text" size="small">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item 
                :command="{action: 'export', row: scope.row}"
                v-if="hasPermission('export')"
              >
                导出配置
              </el-dropdown-item>
              <el-dropdown-item 
                :command="{action: 'logs', row: scope.row}"
                v-if="hasPermission('logs')"
              >
                查看日志
              </el-dropdown-item>
              <el-dropdown-item 
                :command="{action: 'delete', row: scope.row}"
                v-if="hasPermission('delete')"
                divided
              >
                <span style="color: #f56c6c;">删除</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'AgentTable',
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: Object,
      default: () => ({
        pageIndex: 1,
        pageSize: 20
      })
    },
    permissions: {
      type: Object,
      default: () => ({
        view: true,
        edit: true,
        delete: true,
        copy: true,
        toggle: true,
        export: true,
        logs: true
      })
    }
  },
  data() {
    return {
      categoryMap: {
        chat: '对话助手',
        text: '文本生成',
        image: '图像处理',
        analysis: '数据分析',
        other: '其他'
      },
      categoryTagTypeMap: {
        chat: 'primary',
        text: 'success',
        image: 'warning',
        analysis: 'info',
        other: 'default'
      }
    }
  },
  methods: {
    // 获取类别标签
    getCategoryLabel(category) {
      return this.categoryMap[category] || category || '-'
    },
    
    // 获取类别标签类型
    getCategoryTagType(category) {
      return this.categoryTagTypeMap[category] || 'default'
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    
    // 序号方法
    indexMethod(index) {
      return (this.pagination.pageIndex - 1) * this.pagination.pageSize + index + 1
    },
    
    // 检查是否可选择
    checkSelectable(row) {
      // 可以根据业务需求添加选择条件
      return true
    },
    
    // 表格行类名
    tableRowClassName({ row, rowIndex }) {
      if (row.status === 0) {
        return 'disabled-row'
      }
      return ''
    },
    
    // 权限检查
    hasPermission(action) {
      return this.permissions[action] !== false
    },
    
    // 是否有更多操作
    hasMoreActions() {
      return this.hasPermission('export') || 
             this.hasPermission('logs') || 
             this.hasPermission('delete')
    },
    
    // 选择变化
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    },
    
    // 排序变化
    handleSortChange({ column, prop, order }) {
      this.$emit('sort-change', { prop, order })
    },
    
    // 状态切换
    handleStatusChange(row) {
      this.$emit('status-change', row)
    },
    
    // 查看详情
    handleViewDetail(row) {
      this.$emit('view-detail', row)
    },
    
    // 查看
    handleView(row) {
      this.$emit('view', row)
    },
    
    // 编辑
    handleEdit(row) {
      this.$emit('edit', row)
    },
    
    // 复制
    handleCopy(row) {
      this.$emit('copy', row)
    },
    
    // 下拉菜单命令
    handleCommand({ action, row }) {
      this.$emit(action, row)
    }
  }
}
</script>

<style lang="less" scoped>
.agent-table {
  .usage-count {
    font-weight: 500;
    color: #409eff;
  }
  
  /deep/ .disabled-row {
    background-color: #f5f7fa;
    color: #c0c4cc;
  }
  
  /deep/ .el-table__body-wrapper {
    .el-table__row:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
