// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'

// 开发环境下引入菜单测试工具
if (process.env.NODE_ENV === 'development') {
  import('./utils/menuTest')
}
import Es6Promise from 'es6-promise'
import proInstall from '@js/install' // 引入项目install文件
import {getHttpConfig} from '@/api/render-config.js'
import '@css/index.css' // 引入全局样式（包含element样式）
import '@js/permission.js' // 全局权限控制文件
import Config from './assets/js/config.js'

// ===saas2.0 引入和安装插件
import saasJs from './utils/saas/tools'
import VueAwesomeSwiper from 'vue-awesome-swiper'
import 'swiper/dist/css/swiper.css'
import { moduleFederationPlugin } from "@/utils/moduleFederationPlugin";
import { getRoute, addRoute, AUTHSYSTEMID } from '@/utils/menu';
Vue.use(VueAwesomeSwiper)
Vue.use(saasJs)
// ===saas2.0 引入和安装插件

Es6Promise.polyfill();
Vue.prototype.config = Config;
Vue.config.productionTip = false
console.log(process.env.NODE_ENV, 8888);

// Vue对象扩展安装
Vue.use(proInstall)

// 动态加载组件库页面
const addRouteDynamic = () => {
  const routerLocal = getRoute()
  if (routerLocal) {
      addRoute(routerLocal)
  }
}
addRouteDynamic()
/* eslint-disable no-new */
// 下面组件库做接口统一headers处理，需要把数据放入缓存，headerPayloads自动获取
window.localStorage.setItem('AUTHSYSTEMID', AUTHSYSTEMID)
// const loadRenderTemplate = moduleFederationPlugin('renderTemplate','leyaoyaoRenderTemplate', './renderTemplate')
const loadPcRender = moduleFederationPlugin('pcRender','leyaoyaoPcRender', './lyyRender')
Promise.all([loadPcRender]).then(([res2]) => {
  const {httpConfig, createRender} = res2
  getHttpConfig('1060551741250347008').then(res=>{
    if(res){
      httpConfig.setHttpConfig(res)
    }else{
      httpConfig.setHttpConfig({
        baseURL: '/gw/admin',
        "headerPayloads": [
          {
            "key": "ram-token",
            "value": "sessionStorage.token"
          },
          {
            "key": "ram-system",
            "value": "1060551741250347008"
          }
        ],
        customOption: {
          jsonPath: 'body',
          loading: true,
          failedFeedback: {
            type: 'notification',
            placement: 'topRight',
            status: 'error',
            title: '提示信息',
          },
          codeKey: 'code',
          messageKey: 'message',
          codeValue: '0000000',
        },
      })
    }
  })
  new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App),
  })
}).catch(err => {
  new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App),
  })
})
