/*
 * @Description:
 * @Author: z<PERSON><PERSON><PERSON>
 * @Email: zhao<PERSON><EMAIL>
 * @Date: 2022-04-12 18:32:56
 * @LastEditTime: 2022-05-06 17:20:31
 * @LastEditors: zhaorubo
 */

export default {
  data(){
    return {
      tableH:0
    }
  },
  mounted() {
    this.setTableH()
  },
  methods: {
    setTableH(){
      this.$nextTick(()=>{
        const headerDom = document.getElementsByClassName('header')[0]
        if(headerDom){
          const totalPadding = 100 + 36 + 20
          const tableH =  document.body.clientHeight - (headerDom.offsetHeight + headerDom.offsetTop + totalPadding)
          this.tableH = Math.max(tableH,200)
          window.addEventListener('resize',this.setTableH)
          // console.log('【setTableH调用】',headerDom)
        }
      })
    },
  },
  beforeDestroy() {
      window.removeEventListener('resize',this.setTableH)
  },
}

