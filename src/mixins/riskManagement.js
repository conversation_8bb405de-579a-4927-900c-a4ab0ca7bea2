import { getRiskDetail, getAdStrategyList, updateStatus } from "@/api/riskManagement/riskManagement";
export default {
  data() {
    return {
      isCheck: 0,
      //   status: "", //状态：0不通过；1通过；2待审核；3处罚; 101不通过，撤销补贴；102不通过，撤销处罚；103不通过，撤销补贴失败
      //   ids: [] //统计ID数组

      detailType: false, //设置详情类型，以显示不同的搜索条件
      tableData: [],
      // 分页
      pageIndex: 1,
      pageSize: 20,
      totalNum: 0,
      isLoading: false,
      // 其他查询条件
      statisticsDate: "", //日期
      riskControlAdStrategyId: "", //补贴项目
      lyyDistributorId: "", //商户id
      username: "", //手机号码

      idsArr: [],//批量操作的id数组

      // 审核状态
      //   checkStatus: {
      //     type: "select", // 检索类型,下拉框
      //     label: "审核状态：", // label
      //     placeholder: "", // 占位符
      //     valueName: "status", // 返回对应值名
      //     options: [
      //       { label: "全部", value: "" },
      //       { label: "不通过", value: 0 },
      //       { label: "通过", value: 1 },
      //       { label: "待审核", value: 2 },
      //       { label: "处罚", value: 3 },
      //       { label: "不通过，撤销补贴", value: 101 },
      //       { label: "不通过，撤销处罚", value: 102 },
      //       { label: "不通过，撤销补贴失败", value: 103 }
      //     ]
      //   },

      // 搜索
      searchList: [],
      //仅一个搜索框
      searchList1: [
        {
          type: "input", // 检索类型,输入框
          label: "商户手机号", // label
          placeholder: "", // 占位符
          valueName: "username" // 返回对应值名
        }
      ],
      //一个搜索框和一个下拉列表
      searchList2: [
        {
          type: "input", // 检索类型,输入框
          label: "商户手机号", // label
          placeholder: "", // 占位符
          valueName: "username" // 返回对应值名
        },
        {
          type: "select", // 检索类型,下拉框
          label: "审核状态：", // label
          placeholder: "", // 占位符
          valueName: "status", // 返回对应值名
          options: [
            { label: "全部", value: "" },
            { label: "不通过", value: 0 },
            { label: "通过", value: 1 },
            { label: "待审核", value: 2 },
            { label: "处罚", value: 3 },
            { label: "不通过，撤销补贴", value: 101 },
            { label: "不通过，撤销处罚", value: 102 },
            { label: "不通过，撤销补贴失败", value: 103 }
          ]
        }
      ],
      //仅一个下拉列表
      searchList3: [
        {
          type: "select", // 检索类型,下拉框
          label: "补贴项目：", // label
          placeholder: "", // 占位符
          valueName: "riskControlAdStrategyId", // 返回对应值名
          options: [
            {
              label: "全部",
              value: ""
            }
          ]
        }
      ],
      //两个下拉列表
      searchList4: [
        {
          type: "select", // 检索类型,下拉框
          label: "补贴项目：", // label
          placeholder: "", // 占位符
          valueName: "riskControlAdStrategyId", // 返回对应值名
          options: [
            {
              label: "全部",
              value: ""
            }
          ]
        },
        {
          type: "select", // 检索类型,下拉框
          label: "审核状态：", // label
          placeholder: "", // 占位符
          valueName: "status", // 返回对应值名
          options: [
            { label: "全部", value: "" },
            { label: "不通过", value: 0 },
            { label: "通过", value: 1 },
            { label: "待审核", value: 2 },
            { label: "处罚", value: 3 },
            { label: "不通过，撤销补贴", value: 101 },
            { label: "不通过，撤销处罚", value: 102 },
            { label: "不通过，撤销补贴失败", value: 103 }
          ]
        }
      ],
      searchFormData: {
        username: ""
        // riskControlAdStrategyId:'',
      },
      // 查询表单
      formData: {
        username: ""
        // riskControlAdStrategyId:'',
      },
      
    };
  },
  created() {
    this.getParams();
    this.getAdStrategyListFun();
  },
  mounted() {
    this.setDetailType();
    this.riskDetail();
  },
  methods: {
    // 审核商户
    setStatus(status, ids) {
      this.isLoading = true;
      let data = {
        status: status,
        ids: ids
      };
      updateStatus(data)
        .then(res => {
          this.isLoading = false;
          if (res && res.result === 0) {
            this.$message.success("操作成功");
            this.riskDetail(); // 操作成功后刷新列表
          } else {
            this.$message.error("操作失败，请重试");
          }
        })
        .catch(() => {
          this.isLoading = false;
        });;
    },
    // 操作弹窗
    updateMessageBox(status, ids) {
      console.log(ids);

      let msg = "",
        title = "";
      switch (status) {
        case 0: //不通过
          title = "审核不通过";
          msg = "确定审核不通过？操作后限制该项目收入商家提现。";
          break;
        case 1: //通过
          title = "审核通过";
          msg = "确定审核通过？操作后该项目收入商家可正常提现。";
          break;
        case 3: //处罚
          title = "处罚商户";
          msg = "禁止该商户下所有设备再次接受该项目任务？";
          break;
        case 101: //不通过，撤销补贴
          title = "撤销补贴";
          msg = "确定撤销补贴吗？";
          break;
        case 102: //不通过，撤销处罚不通过，撤销处罚
          title = "撤销处罚";
          msg = "恢复该项目，商户可正常参与该项目的补贴？";
          break;
      }
      this.$confirm(msg, title, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true
      })
        .then(() => {
          this.setStatus(status, ids);
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除"
          // });
        });
    },
    //////////////////////////////////////////////////
    handleSizeChange(size) {
      this.pageSize = size;
      this.riskDetail();
    },
    handleCurrentChange(index) {
      this.pageIndex = index;
      this.riskDetail();
    },
    serchFun(datas) {
      this.pageIndex = 1;
      this.formData = Object.assign(this.formData, datas.formData);
      this.riskDetail();
    },

    // 设置详情类型，以显示不同的搜索条件
    setDetailType() {
      if (this.detailType == "adStrategy") {
        //查补贴项目，显示输入框
        if (this.isCheck == 0) {
            this.searchList = this.searchList1;
        } else if (this.isCheck == 1) {
            this.searchList = this.searchList2;
        }
      } else if (this.detailType == "distributor") {
        //查商户，显示下拉列表
        // this.getAdStrategyListFun();
        if (this.isCheck == 0) {
            this.searchList = this.searchList3;
        } else if (this.isCheck == 1) {
            // alert(JSON.stringify(this.searchList4));
            this.searchList = this.searchList4;
        }
      } else {
        //默认显示输入框
        this.searchList = this.searchList1;
      }
    },

    // 获取数据列表
    riskDetail() {
      this.isLoading = true;
      let data = Object.assign(
        {
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
          statisticsDate: this.statisticsDate,
          riskControlAdStrategyId: this.riskControlAdStrategyId,
          lyyDistributorId: this.lyyDistributorId,
          isCheck: this.isCheck
        },
        this.formData
      );
      // alert(JSON.stringify(data))
      getRiskDetail(data)
        .then(res => {
          this.isLoading = false;
          if (res && res.result === 0 && res.data) {
            this.tableData = res.data.items;
            this.totalNum = res.data.total;
          } else {
            this.isLoading = false;
          }
        })
        .catch(err => {
          this.isLoading = false;
        });
    },
    // 获取url参数
    getParams() {
      if (this.$route.query.riskControlAdStrategyId) {
        //通过补贴项目查询
        this.riskControlAdStrategyId = this.$route.query.riskControlAdStrategyId;
      } else if (this.$route.query.lyyDistributorId) {
        //通过商户id查询
        this.lyyDistributorId = this.$route.query.lyyDistributorId;
      }
      this.statisticsDate = this.$route.query.statisticsDate;
      this.detailType = this.$route.query.detailType;
    },
    // 获取项目列表-copy
    async getAdStrategyListFun() {
      const res = await getAdStrategyList();
      if (res.result == 0) {
        let projectOptions = [
          {
            label: "全部",
            value: ""
          }
        ];
        res.data.map(item => {
          projectOptions.push({
            label: item.name,
            value: item.riskControlAdStrategyId
          });
          this.searchList3[0].options = projectOptions;
          this.searchList4[0].options = projectOptions;
        });
      }
    //   alert(JSON.stringify(this.searchList2));
    },
    // 批量选择项目
    handleSelectionChange(val) {
      this.idsArr = val;
    },
    // 批量操作
    batchOperation(status) {
      if (!this.idsArr.length) {
        this.$message.warning('请先选中项目再操作');
        return;
      }
      let idArr = [];
      this.idsArr.map(item => {
        idArr.push(item.riskControlAdIncomeId)
      })
      this.updateMessageBox(status, idArr);
    }
  }
};
