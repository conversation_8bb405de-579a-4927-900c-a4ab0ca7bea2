const list = {
  data() {
    return {
      listParams: {
        pageIndex: 1,
        pageSize: 10
      },
      total: 0,
      list: [],
      loading: false
    }
  },
  methods: {
    getList() {},
    handleSizeChange(size) {
      this.listParams.pageSize = size
      this.getList()
    },
    changePage(page) {
      this.listParams.pageIndex = page
      this.getList()
    }
  }
}
export default list
