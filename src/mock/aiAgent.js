// 智能体应用模拟数据
const mockData = {
  list: [
    {
      id: 1,
      appName: '智能客服助手',
      description: '基于大语言模型的智能客服系统，能够自动回答用户常见问题，提供7x24小时服务',
      category: 'chat',
      status: 1,
      usageCount: 1250,
      creator: '张三',
      createTime: '2024-01-15 10:30:00',
      updateTime: '2024-03-20 14:20:00'
    },
    {
      id: 2,
      appName: '文档生成器',
      description: '自动生成各类技术文档、用户手册和API文档的智能工具',
      category: 'text',
      status: 1,
      usageCount: 890,
      creator: '李四',
      createTime: '2024-02-01 09:15:00',
      updateTime: '2024-03-18 16:45:00'
    },
    {
      id: 3,
      appName: '图像识别分析',
      description: '基于深度学习的图像识别和分析工具，支持多种图像格式处理',
      category: 'image',
      status: 0,
      usageCount: 456,
      creator: '王五',
      createTime: '2024-01-20 14:22:00',
      updateTime: '2024-02-28 11:30:00'
    },
    {
      id: 4,
      appName: '数据分析助手',
      description: '智能数据分析工具，支持数据清洗、统计分析和可视化报告生成',
      category: 'analysis',
      status: 1,
      usageCount: 2100,
      creator: '赵六',
      createTime: '2024-01-10 08:45:00',
      updateTime: '2024-03-22 10:15:00'
    },
    {
      id: 5,
      appName: '代码审查机器人',
      description: '自动化代码审查工具，检测代码质量、安全漏洞和性能问题',
      category: 'other',
      status: 1,
      usageCount: 678,
      creator: '孙七',
      createTime: '2024-02-15 13:20:00',
      updateTime: '2024-03-19 15:30:00'
    },
    {
      id: 6,
      appName: '智能翻译助手',
      description: '多语言智能翻译工具，支持实时翻译和文档批量翻译',
      category: 'text',
      status: 1,
      usageCount: 1580,
      creator: '周八',
      createTime: '2024-01-25 11:10:00',
      updateTime: '2024-03-21 09:25:00'
    },
    {
      id: 7,
      appName: '语音识别转换',
      description: '高精度语音识别和转换工具，支持多种音频格式和语言',
      category: 'other',
      status: 0,
      usageCount: 234,
      creator: '吴九',
      createTime: '2024-02-10 16:40:00',
      updateTime: '2024-02-25 14:50:00'
    },
    {
      id: 8,
      appName: '智能推荐引擎',
      description: '基于用户行为和偏好的智能推荐系统，提供个性化内容推荐',
      category: 'analysis',
      status: 1,
      usageCount: 3200,
      creator: '郑十',
      createTime: '2024-01-05 12:30:00',
      updateTime: '2024-03-23 17:20:00'
    },
    {
      id: 9,
      appName: '自动化测试工具',
      description: '智能化软件测试工具，支持功能测试、性能测试和安全测试',
      category: 'other',
      status: 1,
      usageCount: 945,
      creator: '钱十一',
      createTime: '2024-02-20 10:15:00',
      updateTime: '2024-03-17 13:40:00'
    },
    {
      id: 10,
      appName: '内容创作助手',
      description: 'AI驱动的内容创作工具，支持文章写作、创意策划和营销文案生成',
      category: 'text',
      status: 1,
      usageCount: 1890,
      creator: '孙十二',
      createTime: '2024-01-30 15:25:00',
      updateTime: '2024-03-24 11:55:00'
    }
  ],
  total: 10
}

// 模拟API响应
export function mockGetAiAgentList(params = {}) {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredList = [...mockData.list]
      
      // 按名称搜索
      if (params.appName) {
        filteredList = filteredList.filter(item => 
          item.appName.toLowerCase().includes(params.appName.toLowerCase())
        )
      }
      
      // 按类别筛选
      if (params.category) {
        filteredList = filteredList.filter(item => item.category === params.category)
      }
      
      // 按状态筛选
      if (params.status !== undefined && params.status !== '') {
        filteredList = filteredList.filter(item => item.status === parseInt(params.status))
      }
      
      // 按创建人筛选
      if (params.creator) {
        filteredList = filteredList.filter(item => 
          item.creator.includes(params.creator)
        )
      }
      
      // 排序
      if (params.sortBy) {
        filteredList.sort((a, b) => {
          let aVal = a[params.sortBy]
          let bVal = b[params.sortBy]
          
          if (params.sortBy === 'createTime' || params.sortBy === 'updateTime') {
            aVal = new Date(aVal).getTime()
            bVal = new Date(bVal).getTime()
          }
          
          if (params.sortOrder === 'asc') {
            return aVal > bVal ? 1 : -1
          } else {
            return aVal < bVal ? 1 : -1
          }
        })
      }
      
      // 分页
      const pageIndex = parseInt(params.pageIndex) || 1
      const pageSize = parseInt(params.pageSize) || 20
      const start = (pageIndex - 1) * pageSize
      const end = start + pageSize
      const pagedList = filteredList.slice(start, end)
      
      resolve({
        code: '0000000',
        message: 'success',
        data: {
          list: pagedList,
          total: filteredList.length,
          pageIndex,
          pageSize
        }
      })
    }, 500) // 模拟网络延迟
  })
}

// 模拟创建应用
export function mockCreateAiAgent(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newApp = {
        id: mockData.list.length + 1,
        ...data,
        usageCount: 0,
        creator: '当前用户',
        createTime: new Date().toLocaleString('zh-CN'),
        updateTime: new Date().toLocaleString('zh-CN')
      }
      mockData.list.unshift(newApp)
      mockData.total = mockData.list.length
      
      resolve({
        code: '0000000',
        message: '创建成功',
        data: newApp
      })
    }, 300)
  })
}

// 模拟更新应用
export function mockUpdateAiAgent(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockData.list.findIndex(item => item.id === data.id)
      if (index !== -1) {
        mockData.list[index] = {
          ...mockData.list[index],
          ...data,
          updateTime: new Date().toLocaleString('zh-CN')
        }
        resolve({
          code: '0000000',
          message: '更新成功',
          data: mockData.list[index]
        })
      } else {
        resolve({
          code: '0000001',
          message: '应用不存在'
        })
      }
    }, 300)
  })
}

// 模拟删除应用
export function mockDeleteAiAgent(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockData.list.findIndex(item => item.id === data.id)
      if (index !== -1) {
        mockData.list.splice(index, 1)
        mockData.total = mockData.list.length
        resolve({
          code: '0000000',
          message: '删除成功'
        })
      } else {
        resolve({
          code: '0000001',
          message: '应用不存在'
        })
      }
    }, 300)
  })
}

// 模拟切换状态
export function mockToggleAiAgentStatus(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockData.list.findIndex(item => item.id === data.id)
      if (index !== -1) {
        mockData.list[index].status = data.status
        mockData.list[index].updateTime = new Date().toLocaleString('zh-CN')
        resolve({
          code: '0000000',
          message: '状态更新成功',
          data: mockData.list[index]
        })
      } else {
        resolve({
          code: '0000001',
          message: '应用不存在'
        })
      }
    }, 300)
  })
}
