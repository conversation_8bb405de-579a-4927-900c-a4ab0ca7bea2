#!/usr/bin/env node

/**
 * 智能体应用菜单配置验证脚本
 * 用于检查菜单相关配置是否正确
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证智能体应用菜单配置...\n')

// 检查文件是否存在
function checkFileExists(filePath, description) {
  const fullPath = path.resolve(__dirname, '..', filePath)
  const exists = fs.existsSync(fullPath)
  console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`)
  return exists
}

// 检查文件内容是否包含指定字符串
function checkFileContent(filePath, searchString, description) {
  try {
    const fullPath = path.resolve(__dirname, '..', filePath)
    const content = fs.readFileSync(fullPath, 'utf8')
    const contains = content.includes(searchString)
    console.log(`${contains ? '✅' : '❌'} ${description}`)
    return contains
  } catch (error) {
    console.log(`❌ ${description} (文件读取失败)`)
    return false
  }
}

// 验证步骤
console.log('📁 检查文件存在性:')
const filesCheck = [
  checkFileExists('src/views/aiAgent/index.vue', '主页面文件'),
  checkFileExists('src/views/aiAgent/components/AgentTable.vue', '表格组件'),
  checkFileExists('src/views/aiAgent/components/SearchBar.vue', '搜索组件'),
  checkFileExists('src/api/aiAgent.js', 'API接口文件'),
  checkFileExists('src/mock/aiAgent.js', '模拟数据文件'),
  checkFileExists('src/views/aiAgent/README.md', '说明文档')
]

console.log('\n📝 检查路由配置:')
const routerCheck = [
  checkFileContent('src/router/index.js', 'AiAgent', '路由组件导入'),
  checkFileContent('src/router/index.js', '/aiAgent', '路由路径配置'),
  checkFileContent('src/router/routerMap.js', 'AiAgentManage', '路由映射配置')
]

console.log('\n🎯 检查菜单配置:')
const menuCheck = [
  checkFileContent('src/views/layout/side_menu/side_menu.vue', 'addAiAgentMenu', '临时菜单方法'),
  checkFileContent('src/views/layout/side_menu/side_menu.vue', 'this.addAiAgentMenu()', '菜单方法调用')
]

console.log('\n🧩 检查组件配置:')
const componentCheck = [
  checkFileContent('src/views/aiAgent/index.vue', 'SearchBar', '搜索组件引用'),
  checkFileContent('src/views/aiAgent/index.vue', 'AgentTable', '表格组件引用'),
  checkFileContent('src/api/aiAgent.js', 'getAiAgentList', 'API方法定义')
]

// 统计结果
const allChecks = [...filesCheck, ...routerCheck, ...menuCheck, ...componentCheck]
const passedChecks = allChecks.filter(Boolean).length
const totalChecks = allChecks.length

console.log('\n📊 验证结果:')
console.log(`通过: ${passedChecks}/${totalChecks}`)
console.log(`成功率: ${Math.round((passedChecks / totalChecks) * 100)}%`)

if (passedChecks === totalChecks) {
  console.log('\n🎉 所有配置检查通过！')
  console.log('\n📋 下一步操作:')
  console.log('1. 启动开发服务器: npm run dev')
  console.log('2. 登录系统')
  console.log('3. 查看左侧菜单是否显示"智能体应用"')
  console.log('4. 点击菜单测试页面功能')
  console.log('5. 在浏览器控制台运行 testAiAgentMenu() 进行详细测试')
} else {
  console.log('\n⚠️ 部分配置检查未通过，请检查以上标记为 ❌ 的项目')
  console.log('\n🔧 修复建议:')
  
  if (!filesCheck.every(Boolean)) {
    console.log('- 检查文件是否正确创建')
  }
  
  if (!routerCheck.every(Boolean)) {
    console.log('- 检查路由配置是否正确添加')
  }
  
  if (!menuCheck.every(Boolean)) {
    console.log('- 检查菜单临时配置是否正确添加')
  }
  
  if (!componentCheck.every(Boolean)) {
    console.log('- 检查组件引用和API配置是否正确')
  }
}

console.log('\n📚 相关文档:')
console.log('- 配置说明: docs/智能体应用菜单配置说明.md')
console.log('- 功能说明: src/views/aiAgent/README.md')

console.log('\n🆘 如果遇到问题:')
console.log('1. 检查控制台错误信息')
console.log('2. 确认后端权限系统配置')
console.log('3. 清除浏览器缓存重新登录')
console.log('4. 联系开发团队获取支持')

process.exit(passedChecks === totalChecks ? 0 : 1)
